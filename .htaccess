# Security settings for Tel<PERSON><PERSON> Bot

# Prevent access to sensitive files
<Files "config.php">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "storage_abstraction.php">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "database_functions.php">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "bot_handlers.php">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "admin_handlers.php">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "user_account_handlers.php">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "database.sql">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

# Prevent access to log files
<Files "*.log">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

# Prevent access to backup files
<Files "*.bak">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.backup">
    Order allow,deny
    <PERSON>y from all
</Files>

# Prevent access to JSON data files
<Files "*.json">
    Order allow,deny
    Deny from all
</Files>

# Prevent access to data directory
<Directory "data">
    Order allow,deny
    <PERSON><PERSON> from all
</Directory>

# Hide PHP errors from public
php_flag display_errors off
php_flag log_errors on

# Set security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Prevent directory browsing
Options -Indexes

# Custom error pages (optional)
ErrorDocument 403 "Access Denied"
ErrorDocument 404 "Not Found"

# Rate limiting (if supported by hosting)
<IfModule mod_evasive24.c>
    DOSHashTableSize    2048
    DOSPageCount        10
    DOSPageInterval     1
    DOSSiteCount        50
    DOSSiteInterval     1
    DOSBlockingPeriod   600
</IfModule>
