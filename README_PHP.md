# 🤖 Telegram Referral Bot - PHP Version

A complete PHP implementation of the Telegram referral bot, designed for Hostinger shared hosting with webhook functionality.

## 📋 Features

### ✅ Complete Feature Replication
- **Exact replica** of the original Node.js bot functionality
- **Admin Panel** with all original commands and features
- **Referral System** with automatic reward distribution
- **Withdrawal Management** with bank account verification
- **OTP Verification** using renflair.in API
- **Gift Broadcasting** system for bonus campaigns
- **User Management** with ban/unban functionality
- **Multi-step Operations** with session management

### 🎯 User Features
- `/start` command with referral tracking
- Channel joining verification
- Automatic joining bonus (random amount)
- Referral rewards (random amount per referral)
- Wallet management (balance, withdrawals, reports)
- Bank account information setup with OTP verification
- Withdrawal requests (₹100, ₹200, ₹400, ₹600, ₹800, ₹1000)
- Promotion and withdrawal history
- Gift bonus claiming

### 👑 Admin Features
- `/admin` command for admin panel access
- Add/Remove user balance
- Ban/Unban users
- Set main channel and private logs channel
- Configure maintenance mode
- Set OTP API key
- Configure referral and joining bonus amounts
- Check user records
- Approve/Reject withdrawal requests
- Broadcast gift campaigns
- Broadcast text messages to all users

## 🏗️ Architecture

### 🔄 Dual Storage System
The bot supports **two storage modes** that can be switched with a simple configuration change:

- **JSON Mode**: File-based storage for development and testing
- **MySQL Mode**: Database storage for production deployment

### 📁 File Structure
```
/
├── webhook.php                 # Main webhook endpoint
├── config.php                  # Configuration and storage settings
├── storage_abstraction.php    # Storage abstraction layer
├── database.sql               # Database schema (MySQL mode)
├── database_functions.php     # Database operations wrapper
├── bot_handlers.php           # User message handlers
├── admin_handlers.php         # Admin command handlers
├── user_account_handlers.php  # Account management handlers
├── setup.php                  # Setup and testing interface
├── migrate_data.php           # Data migration tool
├── .htaccess                  # Security configuration
├── STORAGE_MODES.md           # Storage documentation
└── README_PHP.md              # This file
```

### 📂 Data Directory (JSON Mode)
```
/data/
├── users.json                 # User profiles and data
├── admin_settings.json        # Bot configuration
├── user_sessions.json         # Multi-step operation state
└── bot_info.json             # Cached bot information
```

### 🗄️ Database Schema
- **users** - User profiles and balances
- **user_accounts** - Bank account information
- **admin_settings** - Bot configuration
- **promotion_reports** - Referral tracking
- **withdrawal_reports** - Withdrawal history
- **user_sessions** - Multi-step operation state
- **bot_info** - Cached bot information

## 🚀 Installation Guide

### 📋 Prerequisites
- **Hostinger Shared Hosting** (or any PHP 7.4+ hosting)
- **MySQL Database** access
- **Telegram Bot Token** (from @BotFather)
- **OTP API Key** (from renflair.in)

### 🔧 Step-by-Step Setup

#### 1. **Create Telegram Bot**
```bash
# Message @BotFather on Telegram
/newbot
# Follow instructions to get your bot token
```

#### 2. **Prepare Hosting**
- Log into Hostinger hPanel
- Create a new MySQL database
- Note down database credentials

#### 3. **Upload Files**
- Upload all PHP files to `public_html` folder
- Ensure file permissions are set correctly (644 for files, 755 for directories)

#### 4. **Database Setup**
- Open phpMyAdmin in hPanel
- Import `database.sql` file
- Verify all tables are created

#### 5. **Configuration**
Edit `config.php` with your details:
```php
// Choose storage mode: 'json' or 'mysql'
define('STORAGE_MODE', 'json'); // For development
// define('STORAGE_MODE', 'mysql'); // For production

// Database Configuration (only needed for MySQL mode)
define('DB_HOST', 'localhost');
define('DB_NAME', 'your_database_name');
define('DB_USER', 'your_database_user');
define('DB_PASS', 'your_database_password');

// Bot Configuration
define('BOT_TOKEN', 'your_bot_token_here');
define('ADMIN_ID', your_telegram_user_id);
```

#### 6. **Setup and Testing**
- Visit `https://yourdomain.com/setup.php`
- Run all tests to verify configuration
- Set webhook URL: `https://yourdomain.com/webhook.php`
- Test bot by sending `/start` in Telegram

#### 7. **Security**
- Delete `setup.php` after successful setup
- Verify `.htaccess` is protecting sensitive files
- Test that config.php is not accessible via browser

## 🔧 Configuration Options

### 🔄 Storage Modes

#### 📁 JSON Mode (Development)
Perfect for development and testing:
```php
define('STORAGE_MODE', 'json');
```
**Advantages:**
- ✅ No database setup required
- ✅ Human-readable data files
- ✅ Easy backup and transfer
- ✅ Quick development setup

#### 🗄️ MySQL Mode (Production)
Recommended for production deployment:
```php
define('STORAGE_MODE', 'mysql');
```
**Advantages:**
- ✅ Scalable for thousands of users
- ✅ ACID compliance and data integrity
- ✅ Concurrent access support
- ✅ Advanced querying capabilities

### 🤖 Bot Settings (config.php)
```php
// Storage Configuration
define('STORAGE_MODE', 'json'); // or 'mysql'

// Database Configuration (MySQL mode only)
define('DB_HOST', 'localhost');
define('DB_NAME', 'your_database_name');
define('DB_USER', 'your_database_user');
define('DB_PASS', 'your_database_password');

// Bot Configuration
define('BOT_TOKEN', 'your_bot_token');
define('ADMIN_ID', your_admin_telegram_id);

// API URLs
define('TELEGRAM_API_URL', 'https://api.telegram.org/bot' . BOT_TOKEN . '/');
define('OTP_API_URL', 'https://sms.renflair.in/V1.php');
```

### ⚙️ Admin Panel Settings
Access via `/admin` command in Telegram:
- **Main Channel**: Channel users must join
- **Private Logs Channel**: Admin notifications
- **Maintenance Status**: On/Off
- **OTP API Key**: For mobile verification
- **Per Refer Amount**: Maximum reward per referral
- **Joining Bonus Amount**: Maximum joining bonus

## 🎮 Usage Guide

### 👤 For Users
1. **Start**: Send `/start` or use referral link
2. **Join Channel**: Click the channel link and join
3. **Get Money**: Click "💰GET MONEY💰" button
4. **Invite Friends**: Share your referral link
5. **Set Account Info**: Configure bank details for withdrawals
6. **Withdraw**: Request withdrawal when balance ≥ ₹100

### 👑 For Admins
1. **Access Panel**: Send `/admin` command
2. **Manage Users**: Add/remove balance, ban/unban
3. **Configure Bot**: Set channels, amounts, maintenance
4. **Handle Withdrawals**: Approve/reject withdrawal requests
5. **Broadcast**: Send gifts or messages to all users

## 🔒 Security Features

### 🛡️ Built-in Protection
- **File Access Control**: Sensitive files protected via .htaccess
- **Input Validation**: All user inputs validated and sanitized
- **SQL Injection Prevention**: Prepared statements used throughout
- **Session Management**: Secure session handling for multi-step operations
- **Admin Verification**: All admin functions verify user ID
- **Rate Limiting**: Basic protection against spam

### 🔐 Security Best Practices
- Keep bot token secret
- Use strong database passwords
- Regularly backup database
- Monitor admin logs channel
- Update PHP and hosting regularly

## 🐛 Troubleshooting

### ❌ Common Issues

#### **Webhook Not Working**
- Verify webhook URL is correct and accessible
- Check SSL certificate is valid
- Ensure webhook.php has proper permissions
- Check error logs for PHP errors

#### **Database Connection Failed**
- Verify database credentials in config.php
- Ensure database exists and is accessible
- Check if hosting allows external connections

#### **Bot Not Responding**
- Verify bot token is correct
- Check if bot is blocked or deleted
- Ensure webhook is set correctly
- Check Telegram API limits

#### **OTP Not Working**
- Verify OTP API key is correct
- Check renflair.in service status
- Ensure mobile number format is correct

### 📊 Monitoring
- Check admin logs channel for user activities
- Monitor database size and performance
- Review error logs regularly
- Test bot functionality periodically

## 🔄 Migration from Node.js

If migrating from the original Node.js version:

1. **Export Data**: Extract users.json and adminPanel.json
2. **Convert Data**: Use provided migration script (if needed)
3. **Import to MySQL**: Insert data into new database schema
4. **Update Configuration**: Set same admin ID and settings
5. **Test Thoroughly**: Verify all functionality works

## 📞 Support

### 🆘 Getting Help
- Check this README thoroughly
- Review setup.php test results
- Check hosting error logs
- Verify all configuration settings

### 🔧 Maintenance
- Regular database backups
- Monitor disk space usage
- Update bot settings as needed
- Clean old session data periodically

## 📄 License

This project is provided as-is for educational and personal use. Please ensure compliance with Telegram's Terms of Service and local regulations when operating bots.

---

**🎉 Congratulations!** You now have a fully functional Telegram referral bot running on PHP with webhook support, perfectly suited for Hostinger shared hosting!
