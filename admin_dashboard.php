<?php
// Dashboard Overview Page
// This file is included by admin_panel.php when page=dashboard

// Format numbers for display
function formatNumber($number) {
    if ($number >= 1000000) {
        return number_format($number / 1000000, 1) . 'M';
    } elseif ($number >= 1000) {
        return number_format($number / 1000, 1) . 'K';
    }
    return number_format($number);
}

function formatCurrency($amount) {
    return '₹' . number_format($amount, 2);
}
?>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <i class="fas fa-users"></i>
            </div>
            <div class="stats-value text-primary"><?php echo formatNumber($stats['total_users']); ?></div>
            <div class="stats-label">Total Users</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="stats-value text-success"><?php echo formatCurrency($stats['total_withdrawals']); ?></div>
            <div class="stats-label">Total Withdrawals</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stats-value text-warning"><?php echo formatCurrency($stats['pending_withdrawals']); ?></div>
            <div class="stats-label">Pending Withdrawals</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);">
                <i class="fas fa-wallet"></i>
            </div>
            <div class="stats-value text-info"><?php echo formatCurrency($stats['total_balances']); ?></div>
            <div class="stats-label">Total User Balances</div>
        </div>
    </div>
</div>

<!-- Activity Statistics -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #e83e8c 0%, #fd7e14 100%);">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="stats-value text-danger"><?php echo formatNumber($stats['active_users_7d']); ?></div>
            <div class="stats-label">Active Users (7 days)</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #6610f2 0%, #e83e8c 100%);">
                <i class="fas fa-calendar-alt"></i>
            </div>
            <div class="stats-value text-purple"><?php echo formatNumber($stats['active_users_30d']); ?></div>
            <div class="stats-label">Active Users (30 days)</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #20c997 0%, #28a745 100%);">
                <i class="fas fa-user-plus"></i>
            </div>
            <div class="stats-value text-success"><?php echo formatNumber($stats['new_users_today']); ?></div>
            <div class="stats-label">New Users Today</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%);">
                <i class="fas fa-share-alt"></i>
            </div>
            <div class="stats-value text-warning"><?php echo formatNumber($stats['total_referrals']); ?></div>
            <div class="stats-label">Total Referrals</div>
        </div>
    </div>
</div>

<!-- Additional Statistics -->
<div class="row mb-4">
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);">
                <i class="fas fa-user-slash"></i>
            </div>
            <div class="stats-value text-danger"><?php echo formatNumber($stats['banned_users']); ?></div>
            <div class="stats-label">Banned Users</div>
        </div>
    </div>
    
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #198754 0%, #20c997 100%);">
                <i class="fas fa-percentage"></i>
            </div>
            <div class="stats-value text-success">
                <?php 
                $activeRate = $stats['total_users'] > 0 ? 
                    round(($stats['active_users_7d'] / $stats['total_users']) * 100, 1) : 0;
                echo $activeRate . '%';
                ?>
            </div>
            <div class="stats-label">7-Day Activity Rate</div>
        </div>
    </div>
    
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #0d6efd 0%, #6610f2 100%);">
                <i class="fas fa-coins"></i>
            </div>
            <div class="stats-value text-primary">
                <?php 
                $avgBalance = $stats['total_users'] > 0 ? 
                    $stats['total_balances'] / $stats['total_users'] : 0;
                echo formatCurrency($avgBalance);
                ?>
            </div>
            <div class="stats-label">Average Balance</div>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="row">
    <!-- User Activity Distribution -->
    <div class="col-lg-6 mb-4">
        <div class="chart-container">
            <h5 class="mb-3"><i class="fas fa-chart-doughnut me-2"></i>User Activity Distribution</h5>
            <canvas id="activityChart" height="300"></canvas>
        </div>
    </div>
    
    <!-- Financial Overview -->
    <div class="col-lg-6 mb-4">
        <div class="chart-container">
            <h5 class="mb-3"><i class="fas fa-chart-pie me-2"></i>Financial Overview</h5>
            <canvas id="financialChart" height="300"></canvas>
        </div>
    </div>
</div>

<!-- Activity Distribution Bar Chart -->
<div class="row">
    <div class="col-12 mb-4">
        <div class="chart-container">
            <h5 class="mb-3"><i class="fas fa-chart-bar me-2"></i>Activity Distribution</h5>
            <canvas id="activityBarChart" height="150"></canvas>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="chart-container">
            <h5 class="mb-3"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
            <div class="row">
                <div class="col-md-3 mb-3">
                    <a href="?page=users" class="btn btn-outline-primary w-100">
                        <i class="fas fa-users me-2"></i>Manage Users
                    </a>
                </div>
                <div class="col-md-3 mb-3">
                    <a href="?page=financial" class="btn btn-outline-success w-100">
                        <i class="fas fa-chart-line me-2"></i>Financial Reports
                    </a>
                </div>
                <div class="col-md-3 mb-3">
                    <a href="?page=leaderboards" class="btn btn-outline-warning w-100">
                        <i class="fas fa-trophy me-2"></i>View Leaderboards
                    </a>
                </div>
                <div class="col-md-3 mb-3">
                    <a href="?page=statistics" class="btn btn-outline-info w-100">
                        <i class="fas fa-chart-bar me-2"></i>Bot Statistics
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// User Activity Distribution Chart (Doughnut)
const activityCtx = document.getElementById('activityChart').getContext('2d');
const activityChart = new Chart(activityCtx, {
    type: 'doughnut',
    data: {
        labels: ['Active Users (7d)', 'Active Users (30d)', 'Inactive Users', 'Banned Users'],
        datasets: [{
            data: [
                <?php echo $stats['active_users_7d']; ?>,
                <?php echo $stats['active_users_30d']; ?>,
                <?php echo $stats['total_users'] - $stats['active_users_30d']; ?>,
                <?php echo $stats['banned_users']; ?>
            ],
            backgroundColor: [
                '#28a745',
                '#17a2b8',
                '#6c757d',
                '#dc3545'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Financial Overview Chart (Pie)
const financialCtx = document.getElementById('financialChart').getContext('2d');
const financialChart = new Chart(financialCtx, {
    type: 'pie',
    data: {
        labels: ['Total Withdrawals', 'Pending Withdrawals', 'User Balances'],
        datasets: [{
            data: [
                <?php echo $stats['total_withdrawals']; ?>,
                <?php echo $stats['pending_withdrawals']; ?>,
                <?php echo $stats['total_balances']; ?>
            ],
            backgroundColor: [
                '#28a745',
                '#ffc107',
                '#17a2b8'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Activity Distribution Bar Chart
const activityBarCtx = document.getElementById('activityBarChart').getContext('2d');
const activityBarChart = new Chart(activityBarCtx, {
    type: 'bar',
    data: {
        labels: ['Total Users', 'Active (7d)', 'Active (30d)', 'New Today', 'Referrals', 'Banned'],
        datasets: [{
            label: 'Count',
            data: [
                <?php echo $stats['total_users']; ?>,
                <?php echo $stats['active_users_7d']; ?>,
                <?php echo $stats['active_users_30d']; ?>,
                <?php echo $stats['new_users_today']; ?>,
                <?php echo $stats['total_referrals']; ?>,
                <?php echo $stats['banned_users']; ?>
            ],
            backgroundColor: [
                '#667eea',
                '#28a745',
                '#17a2b8',
                '#20c997',
                '#ffc107',
                '#dc3545'
            ],
            borderColor: [
                '#667eea',
                '#28a745',
                '#17a2b8',
                '#20c997',
                '#ffc107',
                '#dc3545'
            ],
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
</script>
