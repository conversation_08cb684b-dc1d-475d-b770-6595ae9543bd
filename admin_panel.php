<?php
session_start();
require_once 'config.php';
require_once 'database_functions.php';

// Admin Panel Configuration
define('ADMIN_PASSCODE', '1412');
define('SESSION_TIMEOUT', 3600); // 1 hour

// Check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['admin_logged_in']) && 
           $_SESSION['admin_logged_in'] === true &&
           isset($_SESSION['login_time']) &&
           (time() - $_SESSION['login_time']) < SESSION_TIMEOUT;
}

// Handle login
if (isset($_POST['login'])) {
    $passcode = $_POST['passcode'] ?? '';
    if ($passcode === ADMIN_PASSCODE) {
        $_SESSION['admin_logged_in'] = true;
        $_SESSION['login_time'] = time();
        header('Location: admin_panel.php');
        exit;
    } else {
        $login_error = 'Invalid passcode. Please try again.';
    }
}

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: admin_panel.php');
    exit;
}

// If not logged in, show login form
if (!isLoggedIn()) {
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Admin Panel - Login</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .login-card {
                background: rgba(255, 255, 255, 0.95);
                border-radius: 15px;
                box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
            .login-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border-radius: 15px 15px 0 0;
            }
            .form-control:focus {
                border-color: #667eea;
                box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            }
            .btn-login {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border: none;
                border-radius: 25px;
                padding: 12px 30px;
                font-weight: 600;
                transition: all 0.3s ease;
            }
            .btn-login:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-4">
                    <div class="card login-card">
                        <div class="card-header login-header text-center py-4">
                            <h3><i class="fas fa-shield-alt me-2"></i>Admin Panel</h3>
                            <p class="mb-0">Telegram Bot Management System</p>
                        </div>
                        <div class="card-body p-4">
                            <?php if (isset($login_error)): ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo $login_error; ?>
                                </div>
                            <?php endif; ?>
                            <form method="POST">
                                <div class="mb-3">
                                    <label for="passcode" class="form-label">
                                        <i class="fas fa-key me-2"></i>Access Passcode
                                    </label>
                                    <input type="password" class="form-control" id="passcode" name="passcode" 
                                           placeholder="Enter admin passcode" required>
                                </div>
                                <div class="d-grid">
                                    <button type="submit" name="login" class="btn btn-primary btn-login">
                                        <i class="fas fa-sign-in-alt me-2"></i>Login
                                    </button>
                                </div>
                            </form>
                        </div>
                        <div class="card-footer text-center text-muted">
                            <small><i class="fas fa-lock me-1"></i>Secure Access Required</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Admin Panel Data Access Layer
class AdminDataManager {
    
    public static function getStatistics() {
        // Check for cached stats first
        $cacheFile = DATA_DIR . 'admin_stats_cache.json';
        $cacheTimeout = 300; // 5 minutes
        
        if (file_exists($cacheFile)) {
            $cache = json_decode(file_get_contents($cacheFile), true);
            if ($cache && isset($cache['timestamp']) && (time() - $cache['timestamp']) < $cacheTimeout) {
                return $cache['stats'];
            }
        }
        
        // Generate fresh statistics
        $stats = self::generateStatistics();
        
        // Cache the results
        $cacheData = [
            'timestamp' => time(),
            'stats' => $stats
        ];
        file_put_contents($cacheFile, json_encode($cacheData));
        
        return $stats;
    }
    
    private static function generateStatistics() {
        if (STORAGE_MODE === 'json') {
            return self::generateStatisticsJson();
        } else {
            return self::generateStatisticsMysql();
        }
    }
    
    private static function generateStatisticsJson() {
        $users = [];
        if (file_exists(USERS_FILE)) {
            $users = json_decode(file_get_contents(USERS_FILE), true) ?: [];
        }
        
        $totalUsers = count($users);
        $totalWithdrawals = 0;
        $pendingWithdrawals = 0;
        $totalBalances = 0;
        $totalReferrals = 0;
        $bannedUsers = 0;
        $activeUsers7d = 0;
        $activeUsers30d = 0;
        $newUsersToday = 0;
        
        $today = date('Y-m-d');
        $sevenDaysAgo = strtotime('-7 days');
        $thirtyDaysAgo = strtotime('-30 days');
        
        foreach ($users as $user) {
            // Financial data
            $totalWithdrawals += $user['successful_withdraw'] ?? 0;
            $pendingWithdrawals += $user['withdraw_under_review'] ?? 0;
            $totalBalances += $user['balance'] ?? 0;
            
            // Referral data
            if (isset($user['promotion_report']) && is_array($user['promotion_report'])) {
                $totalReferrals += count($user['promotion_report']);
            }
            
            // User status
            if ($user['banned'] ?? false) {
                $bannedUsers++;
            }
            
            // Activity tracking (simplified - would need last_activity field for accurate tracking)
            // For now, we'll use registration date as proxy
            if (isset($user['created_at'])) {
                $createdTime = strtotime($user['created_at']);
                if ($createdTime >= $sevenDaysAgo) {
                    $activeUsers7d++;
                }
                if ($createdTime >= $thirtyDaysAgo) {
                    $activeUsers30d++;
                }
                if (date('Y-m-d', $createdTime) === $today) {
                    $newUsersToday++;
                }
            }
        }
        
        return [
            'total_users' => $totalUsers,
            'total_withdrawals' => $totalWithdrawals,
            'pending_withdrawals' => $pendingWithdrawals,
            'total_balances' => $totalBalances,
            'active_users_7d' => $activeUsers7d,
            'active_users_30d' => $activeUsers30d,
            'new_users_today' => $newUsersToday,
            'total_referrals' => $totalReferrals,
            'banned_users' => $bannedUsers
        ];
    }
    
    private static function generateStatisticsMysql() {
        // MySQL implementation for statistics
        // This would use proper SQL queries for better performance
        // Implementation would go here when MySQL mode is used
        return [
            'total_users' => 0,
            'total_withdrawals' => 0,
            'pending_withdrawals' => 0,
            'total_balances' => 0,
            'active_users_7d' => 0,
            'active_users_30d' => 0,
            'new_users_today' => 0,
            'total_referrals' => 0,
            'banned_users' => 0
        ];
    }
}

// Get current page
$page = $_GET['page'] ?? 'dashboard';
$stats = AdminDataManager::getStatistics();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - Telegram Bot Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --sidebar-width: 250px;
        }

        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            z-index: 1000;
            transition: all 0.3s ease;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .nav-item {
            margin: 5px 15px;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            border-radius: 8px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
        }

        .nav-link:hover, .nav-link.active {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(5px);
        }

        .nav-link i {
            margin-right: 10px;
            width: 20px;
        }

        .main-content {
            margin-left: var(--sidebar-width);
            padding: 20px;
            min-height: 100vh;
        }

        .top-bar {
            background: white;
            padding: 15px 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            border: none;
            transition: all 0.3s ease;
            height: 100%;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin-bottom: 15px;
        }

        .stats-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stats-label {
            color: #6c757d;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            margin-bottom: 25px;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .main-content {
                margin-left: 0;
            }

            .sidebar.show {
                transform: translateX(0);
            }
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .table-responsive {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            overflow: hidden;
        }

        .table th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border: none;
            font-weight: 600;
        }

        .btn-action {
            padding: 5px 10px;
            margin: 2px;
            border-radius: 5px;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-robot me-2"></i>Bot Admin</h4>
            <small>Management Panel</small>
        </div>
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="?page=dashboard" class="nav-link <?php echo $page === 'dashboard' ? 'active' : ''; ?>">
                    <i class="fas fa-tachometer-alt"></i>Dashboard
                </a>
            </div>
            <div class="nav-item">
                <a href="?page=users" class="nav-link <?php echo $page === 'users' ? 'active' : ''; ?>">
                    <i class="fas fa-users"></i>User Management
                </a>
            </div>
            <div class="nav-item">
                <a href="?page=financial" class="nav-link <?php echo $page === 'financial' ? 'active' : ''; ?>">
                    <i class="fas fa-chart-line"></i>Financial Management
                </a>
            </div>
            <div class="nav-item">
                <a href="?page=leaderboards" class="nav-link <?php echo $page === 'leaderboards' ? 'active' : ''; ?>">
                    <i class="fas fa-trophy"></i>Leaderboards
                </a>
            </div>
            <div class="nav-item">
                <a href="?page=statistics" class="nav-link <?php echo $page === 'statistics' ? 'active' : ''; ?>">
                    <i class="fas fa-chart-bar"></i>Bot Statistics
                </a>
            </div>
            <div class="nav-item mt-4">
                <a href="?logout=1" class="nav-link text-warning">
                    <i class="fas fa-sign-out-alt"></i>Logout
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="top-bar">
            <div>
                <h5 class="mb-0">
                    <?php
                    $pageTitle = [
                        'dashboard' => 'Dashboard Overview',
                        'users' => 'User Management',
                        'financial' => 'Financial Management',
                        'leaderboards' => 'Leaderboards',
                        'statistics' => 'Bot Statistics'
                    ];
                    echo $pageTitle[$page] ?? 'Dashboard';
                    ?>
                </h5>
                <small class="text-muted">Last updated: <?php echo date('Y-m-d H:i:s'); ?></small>
            </div>
            <div>
                <button class="btn btn-outline-primary btn-sm" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
            </div>
        </div>

        <?php
        // Include page content based on current page
        switch ($page) {
            case 'dashboard':
                include 'admin_dashboard.php';
                break;
            case 'users':
                include 'admin_users.php';
                break;
            case 'financial':
                include 'admin_financial.php';
                break;
            case 'leaderboards':
                include 'admin_leaderboards.php';
                break;
            case 'statistics':
                include 'admin_statistics.php';
                break;
            default:
                include 'admin_dashboard.php';
        }
        ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function refreshData() {
            // Clear cache and reload
            fetch('admin_panel.php?action=clear_cache', {method: 'POST'})
                .then(() => location.reload());
        }

        // Auto-refresh every 5 minutes
        setInterval(refreshData, 300000);
    </script>
</body>
</html>
