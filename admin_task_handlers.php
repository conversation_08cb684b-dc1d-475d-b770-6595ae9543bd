<?php
require_once 'config.php';
require_once 'core_functions.php';

// Manage Tasks handler
function handleManageTasks($userId, $chatId) {
    if (!isAdmin($userId)) return;

    $allTasks = getAllTasks();

    if (empty($allTasks)) {
        $message = "📋 <b>Task Management</b>\n\n";
        $message .= "❌ No tasks found.\n\n";
        $message .= "Use \"➕ Add New Task\" to create your first task.";

        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => '➕ Add New Task', 'callback_data' => 'addNewTask']
                ],
                [
                    ['text' => '↩️ Back to Admin Panel', 'callback_data' => 'admin']
                ]
            ]
        ];

        sendMessage($chatId, $message, $keyboard, 'HTML');
        return;
    }

    $message = "📋 <b>Task Management</b>\n\n";
    $message .= "Total Tasks: " . count($allTasks) . "\n\n";

    $keyboard = ['inline_keyboard' => []];

    foreach ($allTasks as $task) {
        $statusIcon = $task['status'] === 'active' ? '✅' : '❌';
        $taskName = strlen($task['name']) > 20 ? substr($task['name'], 0, 20) . '...' : $task['name'];

        $keyboard['inline_keyboard'][] = [
            ['text' => "{$statusIcon} {$taskName} - ₹{$task['reward_amount']}", 'callback_data' => "editTask_{$task['task_id']}"]
        ];
    }

    $keyboard['inline_keyboard'][] = [
        ['text' => '➕ Add New Task', 'callback_data' => 'addNewTask']
    ];
    $keyboard['inline_keyboard'][] = [
        ['text' => '📊 View Pending Submissions', 'callback_data' => 'viewPendingSubmissions']
    ];
    $keyboard['inline_keyboard'][] = [
        ['text' => '↩️ Back to Admin Panel', 'callback_data' => 'admin']
    ];

    sendMessage($chatId, $message, $keyboard, 'HTML');
}

// Add New Task handler
function handleAddNewTask($userId, $chatId) {
    if (!isAdmin($userId)) return;

    $message = "➕ <b>Add New Task</b>\n\n";
    $message .= "Let's create a new task for users to complete.\n\n";
    $message .= "📝 <b>Step 1:</b> Enter the task name\n";
    $message .= "Keep it short and descriptive (max 50 characters).\n\n";
    $message .= "Send /cancel to cancel the process.";

    sendMessage($chatId, $message, null, 'HTML');
    setUserSession($userId, 'add_task_name');
}

// Add task step 2 - Task name
function handleAddTaskStep2($userId, $chatId, $text) {
    if (!isAdmin($userId)) return;

    if (strlen($text) > 50) {
        sendMessage($chatId, "❌ Task name too long. Please keep it under 50 characters.");
        return;
    }

    $message = "➕ <b>Add New Task</b>\n\n";
    $message .= "📝 <b>Task Name:</b> {$text}\n\n";
    $message .= "📋 <b>Step 2:</b> Enter the task description\n";
    $message .= "Provide detailed instructions on what users need to do.\n\n";
    $message .= "Send /cancel to cancel the process.";

    sendMessage($chatId, $message, null, 'HTML');
    setUserSession($userId, 'add_task_description', ['name' => $text]);
}

// Add task step 3 - Description
function handleAddTaskStep3($userId, $chatId, $text, $data) {
    if (!isAdmin($userId)) return;

    if (strlen($text) > 500) {
        sendMessage($chatId, "❌ Description too long. Please keep it under 500 characters.");
        return;
    }

    $message = "➕ <b>Add New Task</b>\n\n";
    $message .= "📝 <b>Task Name:</b> {$data['name']}\n";
    $message .= "📋 <b>Description:</b> {$text}\n\n";
    $message .= "💰 <b>Step 3:</b> Enter the reward amount (in ₹)\n";
    $message .= "Enter only the number (e.g., 25 for ₹25).\n\n";
    $message .= "Send /cancel to cancel the process.";

    sendMessage($chatId, $message, null, 'HTML');
    setUserSession($userId, 'add_task_reward', [
        'name' => $data['name'],
        'description' => $text
    ]);
}

// Add task step 4 - Reward amount
function handleAddTaskStep4($userId, $chatId, $text, $data) {
    if (!isAdmin($userId)) return;

    if (!is_numeric($text) || $text <= 0) {
        sendMessage($chatId, "❌ Invalid amount. Please enter a positive number.");
        return;
    }

    $rewardAmount = (float)$text;

    $message = "➕ <b>Add New Task</b>\n\n";
    $message .= "📝 <b>Task Name:</b> {$data['name']}\n";
    $message .= "📋 <b>Description:</b> {$data['description']}\n";
    $message .= "💰 <b>Reward:</b> ₹{$rewardAmount}\n\n";
    $message .= "🖼️ <b>Step 4:</b> Send media URL (optional)\n";
    $message .= "Send an image or video URL to help users understand the task.\n";
    $message .= "Send \"skip\" to skip this step.\n\n";
    $message .= "Send /cancel to cancel the process.";

    sendMessage($chatId, $message, null, 'HTML');
    setUserSession($userId, 'add_task_media', [
        'name' => $data['name'],
        'description' => $data['description'],
        'reward_amount' => $rewardAmount
    ]);
}

// Add task step 5 - Media URL
function handleAddTaskStep5($userId, $chatId, $text, $data) {
    if (!isAdmin($userId)) return;

    $mediaUrl = '';
    if (strtolower($text) !== 'skip') {
        // Basic URL validation
        if (!filter_var($text, FILTER_VALIDATE_URL)) {
            sendMessage($chatId, "❌ Invalid URL. Please enter a valid URL or send \"skip\" to skip.");
            return;
        }
        $mediaUrl = $text;
    }

    $message = "➕ <b>Add New Task</b>\n\n";
    $message .= "📝 <b>Task Name:</b> {$data['name']}\n";
    $message .= "📋 <b>Description:</b> {$data['description']}\n";
    $message .= "💰 <b>Reward:</b> ₹{$data['reward_amount']}\n";
    $message .= "🖼️ <b>Media:</b> " . ($mediaUrl ? "Provided" : "None") . "\n\n";
    $message .= "⚙️ <b>Step 5:</b> Set task status\n";
    $message .= "Choose whether the task should be active immediately.";

    $keyboard = [
        'inline_keyboard' => [
            [
                ['text' => '✅ Active', 'callback_data' => 'setTaskStatus_active'],
                ['text' => '❌ Inactive', 'callback_data' => 'setTaskStatus_inactive']
            ],
            [
                ['text' => '🚫 Cancel', 'callback_data' => 'admin']
            ]
        ]
    ];

    sendMessage($chatId, $message, $keyboard, 'HTML');
    setUserSession($userId, 'add_task_status', [
        'name' => $data['name'],
        'description' => $data['description'],
        'reward_amount' => $data['reward_amount'],
        'media_url' => $mediaUrl
    ]);
}

// Add task step 6 - Status and create task
function handleAddTaskStep6($userId, $chatId, $status) {
    if (!isAdmin($userId)) return;

    $session = getUserSession($userId);
    if (!$session || $session['step'] !== 'add_task_status') {
        sendMessage($chatId, "❌ Session expired. Please start again.");
        clearUserSession($userId);
        return;
    }

    $data = $session['data'];
    $taskData = [
        'name' => $data['name'],
        'description' => $data['description'],
        'reward_amount' => $data['reward_amount'],
        'media_url' => $data['media_url'],
        'status' => $status,
        'created_by' => $userId
    ];

    if (addTask($taskData)) {
        $message = "✅ <b>Task Created Successfully!</b>\n\n";
        $message .= "📝 <b>Name:</b> {$taskData['name']}\n";
        $message .= "📋 <b>Description:</b> {$taskData['description']}\n";
        $message .= "💰 <b>Reward:</b> ₹{$taskData['reward_amount']}\n";
        $message .= "⚙️ <b>Status:</b> " . ucfirst($status) . "\n\n";
        $message .= "The task is now available for users!";

        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => '📋 Manage Tasks', 'callback_data' => 'manageTasks']
                ],
                [
                    ['text' => '➕ Add Another Task', 'callback_data' => 'addNewTask']
                ],
                [
                    ['text' => '↩️ Back to Admin Panel', 'callback_data' => 'admin']
                ]
            ]
        ];

        sendMessage($chatId, $message, $keyboard, 'HTML');
    } else {
        sendMessage($chatId, "❌ Error creating task. Please try again.");
    }

    clearUserSession($userId);
}

// Generate Gift Code handler
function handleGenerateGiftCode($userId, $chatId) {
    if (!isAdmin($userId)) return;

    $message = "🎫 <b>Generate Gift Code</b>\n\n";
    $message .= "Let's create a new gift code for users to redeem.\n\n";
    $message .= "📝 <b>Step 1:</b> Enter the gift code text\n";
    $message .= "Choose a unique code (letters and numbers only).\n\n";
    $message .= "Send /cancel to cancel the process.";

    sendMessage($chatId, $message, null, 'HTML');
    setUserSession($userId, 'generate_gift_code');
}

// Generate gift code step 2
function handleGenerateGiftCodeStep2($userId, $chatId, $text) {
    if (!isAdmin($userId)) return;

    // Validate code format
    if (!preg_match('/^[A-Za-z0-9]+$/', $text)) {
        sendMessage($chatId, "❌ Invalid code format. Use only letters and numbers.");
        return;
    }

    if (strlen($text) < 3 || strlen($text) > 20) {
        sendMessage($chatId, "❌ Code must be between 3 and 20 characters.");
        return;
    }

    // Check if code already exists
    $existingCodes = getAllGiftCodes();
    foreach ($existingCodes as $code) {
        if (strtolower($code['code']) === strtolower($text)) {
            sendMessage($chatId, "❌ This code already exists. Please choose a different code.");
            return;
        }
    }

    $message = "🎫 <b>Generate Gift Code</b>\n\n";
    $message .= "📝 <b>Code:</b> {$text}\n\n";
    $message .= "💰 <b>Step 2:</b> Enter the gift amount (in ₹)\n";
    $message .= "Enter only the number (e.g., 50 for ₹50).\n\n";
    $message .= "Send /cancel to cancel the process.";

    sendMessage($chatId, $message, null, 'HTML');
    setUserSession($userId, 'generate_gift_amount', ['code' => $text]);
}

// Generate gift code step 3
function handleGenerateGiftCodeStep3($userId, $chatId, $text, $data) {
    if (!isAdmin($userId)) return;

    if (!is_numeric($text) || $text <= 0) {
        sendMessage($chatId, "❌ Invalid amount. Please enter a positive number.");
        return;
    }

    $amount = (float)$text;

    $message = "🎫 <b>Generate Gift Code</b>\n\n";
    $message .= "📝 <b>Code:</b> {$data['code']}\n";
    $message .= "💰 <b>Amount:</b> ₹{$amount}\n\n";
    $message .= "🔢 <b>Step 3:</b> Enter usage limit (optional)\n";
    $message .= "How many times can this code be used?\n";
    $message .= "Send \"unlimited\" for no limit or a number.\n\n";
    $message .= "Send /cancel to cancel the process.";

    sendMessage($chatId, $message, null, 'HTML');
    setUserSession($userId, 'generate_gift_limit', [
        'code' => $data['code'],
        'amount' => $amount
    ]);
}

// Generate gift code step 4
function handleGenerateGiftCodeStep4($userId, $chatId, $text, $data) {
    if (!isAdmin($userId)) return;

    $usageLimit = 0; // 0 means unlimited
    if (strtolower($text) !== 'unlimited') {
        if (!is_numeric($text) || $text <= 0) {
            sendMessage($chatId, "❌ Invalid limit. Enter a positive number or \"unlimited\".");
            return;
        }
        $usageLimit = (int)$text;
    }

    $codeData = [
        'code' => $data['code'],
        'amount' => $data['amount'],
        'usage_limit' => $usageLimit,
        'expiry_date' => 0, // No expiry for now
        'created_by' => $userId
    ];

    if (addGiftCode($codeData)) {
        $message = "✅ <b>Gift Code Created Successfully!</b>\n\n";
        $message .= "📝 <b>Code:</b> <code>{$codeData['code']}</code>\n";
        $message .= "💰 <b>Amount:</b> ₹{$codeData['amount']}\n";
        $message .= "🔢 <b>Usage Limit:</b> " . ($usageLimit > 0 ? $usageLimit : "Unlimited") . "\n\n";
        $message .= "Users can now redeem this code in the Extra Rewards section!";

        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => '🎫 Generate Another Code', 'callback_data' => 'generateGiftCode']
                ],
                [
                    ['text' => '↩️ Back to Admin Panel', 'callback_data' => 'admin']
                ]
            ]
        ];

        sendMessage($chatId, $message, $keyboard, 'HTML');
    } else {
        sendMessage($chatId, "❌ Error creating gift code. Please try again.");
    }

    clearUserSession($userId);
}

// View pending submissions
function handleViewPendingSubmissions($userId, $chatId) {
    if (!isAdmin($userId)) return;

    $pendingSubmissions = getPendingTaskSubmissions();

    if (empty($pendingSubmissions)) {
        $message = "📊 <b>Pending Task Submissions</b>\n\n";
        $message .= "✅ No pending submissions found.\n\n";
        $message .= "All tasks have been reviewed!";

        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => '↩️ Back to Task Management', 'callback_data' => 'manageTasks']
                ]
            ]
        ];

        sendMessage($chatId, $message, $keyboard, 'HTML');
        return;
    }

    $message = "📊 <b>Pending Task Submissions</b>\n\n";
    $message .= "Total Pending: " . count($pendingSubmissions) . "\n\n";

    foreach ($pendingSubmissions as $submission) {
        $task = getTaskById($submission['task_id']);
        $user = getUser($submission['user_id']);

        $submissionMessage = "👤 <b>User:</b> {$user['first_name']} (ID: {$submission['user_id']})\n";
        $submissionMessage .= "📋 <b>Task:</b> {$task['name']}\n";
        $submissionMessage .= "💰 <b>Reward:</b> ₹{$task['reward_amount']}\n";
        $submissionMessage .= "📅 <b>Submitted:</b> " . date('Y-m-d H:i', $submission['submitted_at']) . "\n";
        $submissionMessage .= "🆔 <b>Submission ID:</b> {$submission['submission_id']}";

        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => '✅ Approve', 'callback_data' => "approveTask_{$submission['submission_id']}"],
                    ['text' => '❌ Reject', 'callback_data' => "rejectTask_{$submission['submission_id']}"]
                ]
            ]
        ];

        sendMessage($chatId, $submissionMessage, $keyboard, 'HTML');
    }

    $backKeyboard = [
        'inline_keyboard' => [
            [
                ['text' => '↩️ Back to Task Management', 'callback_data' => 'manageTasks']
            ]
        ]
    ];

    sendMessage($chatId, "Use the buttons above to approve or reject submissions.", $backKeyboard);
}

// Approve task submission
function handleApproveTaskSubmission($userId, $chatId, $submissionId) {
    if (!isAdmin($userId)) return;

    if (updateTaskSubmissionStatus($submissionId, 'approved')) {
        // Get submission details
        $submissions = getAllTaskSubmissions();
        $submission = null;
        foreach ($submissions as $sub) {
            if ($sub['submission_id'] === $submissionId) {
                $submission = $sub;
                break;
            }
        }

        if ($submission) {
            $task = getTaskById($submission['task_id']);
            $user = getUser($submission['user_id']);

            // Notify admin
            sendMessage($chatId, "✅ <b>Task Approved!</b>\n\n👤 User: {$user['first_name']}\n📋 Task: {$task['name']}\n💰 Reward: ₹{$task['reward_amount']} added to user's balance.", null, 'HTML');

            // Notify user
            $userKeyboard = [
                'inline_keyboard' => [
                    [
                        ['text' => '💰 My Wallet', 'callback_data' => 'myWallet']
                    ],
                    [
                        ['text' => '📋 View More Tasks', 'callback_data' => 'taskRewards']
                    ]
                ]
            ];

            sendMessage($submission['user_id'], "🎉 <b>Task Approved!</b>\n\n📋 <b>Task:</b> {$task['name']}\n💰 <b>Reward:</b> ₹{$task['reward_amount']}\n\n✅ The reward has been added to your wallet!", $userKeyboard, 'HTML');
        }
    } else {
        sendMessage($chatId, "❌ Error approving task submission.");
    }
}

// Reject task submission
function handleRejectTaskSubmission($userId, $chatId, $submissionId) {
    if (!isAdmin($userId)) return;

    if (updateTaskSubmissionStatus($submissionId, 'rejected')) {
        // Get submission details
        $submissions = getAllTaskSubmissions();
        $submission = null;
        foreach ($submissions as $sub) {
            if ($sub['submission_id'] === $submissionId) {
                $submission = $sub;
                break;
            }
        }

        if ($submission) {
            $task = getTaskById($submission['task_id']);
            $user = getUser($submission['user_id']);

            // Notify admin
            sendMessage($chatId, "❌ <b>Task Rejected!</b>\n\n👤 User: {$user['first_name']}\n📋 Task: {$task['name']}\n\nUser has been notified.", null, 'HTML');

            // Notify user
            $userKeyboard = [
                'inline_keyboard' => [
                    [
                        ['text' => '📋 View Tasks', 'callback_data' => 'taskRewards']
                    ],
                    [
                        ['text' => '💰 My Wallet', 'callback_data' => 'myWallet']
                    ]
                ]
            ];

            sendMessage($submission['user_id'], "❌ <b>Task Submission Rejected</b>\n\n📋 <b>Task:</b> {$task['name']}\n\n😔 Your submission did not meet the requirements. Please try again with a better proof of completion.", $userKeyboard, 'HTML');
        }
    } else {
        sendMessage($chatId, "❌ Error rejecting task submission.");
    }
}

// Level Rewards Configuration handlers
function handleConfigureLevelRewards($userId, $chatId) {
    if (!isAdmin($userId)) return;

    $config = getLevelRewardsConfig();
    $enabled = isLevelRewardsEnabled();

    $message = "🏆 <b>Level Rewards Configuration</b>\n\n";
    $message .= "⚙️ <b>Status:</b> " . ($enabled ? "✅ Enabled" : "❌ Disabled") . "\n\n";
    $message .= "📊 <b>Current Configuration:</b>\n\n";

    for ($level = 1; $level <= 6; $level++) {
        $referrals = $config['referral_requirements'][$level - 1];
        $bonus = $config['bonus_amounts'][$level - 1];
        $message .= "<b>Level {$level}:</b> {$referrals} referrals = ₹{$bonus}\n";
    }

    $message .= "\n📝 <b>To update configuration:</b>\n";
    $message .= "Send referral requirements (comma-separated):\n";
    $message .= "Example: <code>1,5,10,15,20,25</code>\n\n";
    $message .= "Send /cancel to cancel the process.";

    sendMessage($chatId, $message, null, 'HTML');
    setUserSession($userId, 'configure_level_referrals');
}

function handleConfigureLevelReferralsStep2($userId, $chatId, $text) {
    if (!isAdmin($userId)) return;

    // Parse referral requirements
    $referrals = array_map('trim', explode(',', $text));

    // Validate input
    if (count($referrals) !== 6) {
        sendMessage($chatId, "❌ Please provide exactly 6 referral requirements separated by commas.");
        return;
    }

    foreach ($referrals as $referral) {
        if (!is_numeric($referral) || $referral < 0) {
            sendMessage($chatId, "❌ All referral requirements must be positive numbers.");
            return;
        }
    }

    // Convert to integers
    $referrals = array_map('intval', $referrals);

    // Check if requirements are in ascending order
    for ($i = 1; $i < 6; $i++) {
        if ($referrals[$i] <= $referrals[$i - 1]) {
            sendMessage($chatId, "❌ Referral requirements must be in ascending order (each level should require more referrals than the previous).");
            return;
        }
    }

    $message = "🏆 <b>Level Rewards Configuration</b>\n\n";
    $message .= "📊 <b>Referral Requirements Set:</b>\n";
    for ($i = 0; $i < 6; $i++) {
        $level = $i + 1;
        $message .= "Level {$level}: {$referrals[$i]} referrals\n";
    }

    $message .= "\n💰 <b>Now send bonus amounts (comma-separated):</b>\n";
    $message .= "Example: <code>2,10,15,20,25,30</code>\n\n";
    $message .= "Send /cancel to cancel the process.";

    sendMessage($chatId, $message, null, 'HTML');
    setUserSession($userId, 'configure_level_bonuses', ['referrals' => $referrals]);
}

function handleConfigureLevelBonusesStep3($userId, $chatId, $text, $data) {
    if (!isAdmin($userId)) return;

    // Parse bonus amounts
    $bonuses = array_map('trim', explode(',', $text));

    // Validate input
    if (count($bonuses) !== 6) {
        sendMessage($chatId, "❌ Please provide exactly 6 bonus amounts separated by commas.");
        return;
    }

    foreach ($bonuses as $bonus) {
        if (!is_numeric($bonus) || $bonus <= 0) {
            sendMessage($chatId, "❌ All bonus amounts must be positive numbers.");
            return;
        }
    }

    // Convert to floats
    $bonuses = array_map('floatval', $bonuses);
    $referrals = $data['referrals'];

    // Update configuration
    if (updateLevelRewardsConfig($referrals, $bonuses)) {
        $message = "✅ <b>Level Rewards Configuration Updated!</b>\n\n";
        $message .= "📊 <b>New Configuration:</b>\n\n";

        for ($level = 1; $level <= 6; $level++) {
            $referralReq = $referrals[$level - 1];
            $bonusAmount = $bonuses[$level - 1];
            $message .= "<b>Level {$level}:</b> {$referralReq} referrals = ₹{$bonusAmount}\n";
        }

        $message .= "\n🎉 The new configuration is now active!";

        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => '🏆 Configure Again', 'callback_data' => 'configureLevelRewards']
                ],
                [
                    ['text' => '↩️ Back to Admin Panel', 'callback_data' => 'admin']
                ]
            ]
        ];

        sendMessage($chatId, $message, $keyboard, 'HTML');
    } else {
        sendMessage($chatId, "❌ Error updating level rewards configuration.");
    }

    clearUserSession($userId);
}

function handleToggleLevelBonus($userId, $chatId) {
    if (!isAdmin($userId)) return;

    $currentStatus = isLevelRewardsEnabled();
    $newStatus = !$currentStatus;

    if (toggleLevelRewards($newStatus)) {
        $statusText = $newStatus ? "✅ Enabled" : "❌ Disabled";
        $actionText = $newStatus ? "enabled" : "disabled";

        $message = "🔄 <b>Level Rewards System Updated!</b>\n\n";
        $message .= "⚙️ <b>Status:</b> {$statusText}\n\n";
        $message .= "The level rewards system has been {$actionText}.";

        if ($newStatus) {
            $config = getLevelRewardsConfig();
            $message .= "\n\n📊 <b>Current Configuration:</b>\n";
            for ($level = 1; $level <= 6; $level++) {
                $referrals = $config['referral_requirements'][$level - 1];
                $bonus = $config['bonus_amounts'][$level - 1];
                $message .= "Level {$level}: {$referrals} referrals = ₹{$bonus}\n";
            }
        }

        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => '🔄 Toggle Again', 'callback_data' => 'toggleLevelBonus']
                ],
                [
                    ['text' => '🏆 Configure Levels', 'callback_data' => 'configureLevelRewards']
                ],
                [
                    ['text' => '↩️ Back to Admin Panel', 'callback_data' => 'admin']
                ]
            ]
        ];

        sendMessage($chatId, $message, $keyboard, 'HTML');
    } else {
        sendMessage($chatId, "❌ Error updating level rewards status.");
    }
}
?>
