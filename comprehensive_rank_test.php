<?php
/**
 * Comprehensive Test Suite for /rank Command
 * Tests all aspects of the rank command functionality
 */

require_once 'config.php';
require_once 'core_functions.php';
require_once 'admin_handlers.php';

// Test configuration
$testResults = [];
$totalTests = 0;
$passedTests = 0;

// Mock sendMessage function to capture output
$capturedMessages = [];
function sendMessage($chatId, $message, $keyboard = null, $parseMode = null) {
    global $capturedMessages;
    $capturedMessages[] = [
        'chatId' => $chatId,
        'message' => $message,
        'keyboard' => $keyboard,
        'parseMode' => $parseMode
    ];
    return true;
}

function runTest($testName, $testFunction) {
    global $testResults, $totalTests, $passedTests, $capturedMessages;
    
    $totalTests++;
    $capturedMessages = []; // Reset captured messages
    
    echo "\n🧪 Running Test: {$testName}\n";
    echo str_repeat("-", 50) . "\n";
    
    try {
        $result = $testFunction();
        if ($result) {
            echo "✅ PASSED\n";
            $testResults[$testName] = 'PASSED';
            $passedTests++;
        } else {
            echo "❌ FAILED\n";
            $testResults[$testName] = 'FAILED';
        }
    } catch (Exception $e) {
        echo "❌ ERROR: " . $e->getMessage() . "\n";
        $testResults[$testName] = 'ERROR: ' . $e->getMessage();
    }
}

// Test 1: Admin Access Verification
function testAdminAccess() {
    global $capturedMessages;
    
    echo "Testing admin access for all configured admin IDs...\n";
    
    $adminIds = ADMIN_IDS;
    $testChatId = 12345;
    
    foreach ($adminIds as $adminId) {
        echo "  Testing admin ID: {$adminId}\n";
        
        // Clear previous messages
        $capturedMessages = [];
        
        // Test the command
        handleRankCommand($adminId, $testChatId);
        
        // Check if message was sent (should not be access denied)
        if (empty($capturedMessages)) {
            echo "    ❌ No message sent for admin {$adminId}\n";
            return false;
        }
        
        $message = $capturedMessages[0]['message'];
        if (strpos($message, 'Access Denied') !== false) {
            echo "    ❌ Access denied for valid admin {$adminId}\n";
            return false;
        }
        
        echo "    ✅ Admin {$adminId} has access\n";
    }
    
    return true;
}

// Test 2: Non-Admin Rejection
function testNonAdminRejection() {
    global $capturedMessages;
    
    echo "Testing non-admin user rejection...\n";
    
    $nonAdminIds = [999999999, 111111111, 555555555];
    $testChatId = 12345;
    
    foreach ($nonAdminIds as $nonAdminId) {
        echo "  Testing non-admin ID: {$nonAdminId}\n";
        
        // Clear previous messages
        $capturedMessages = [];
        
        // Test the command
        handleRankCommand($nonAdminId, $testChatId);
        
        // Check if access was denied
        if (empty($capturedMessages)) {
            echo "    ❌ No message sent for non-admin {$nonAdminId}\n";
            return false;
        }
        
        $message = $capturedMessages[0]['message'];
        if (strpos($message, 'Access Denied') === false) {
            echo "    ❌ Non-admin {$nonAdminId} was not properly rejected\n";
            echo "    Message: " . substr($message, 0, 100) . "...\n";
            return false;
        }
        
        echo "    ✅ Non-admin {$nonAdminId} properly rejected\n";
    }
    
    return true;
}

// Test 3: Data Retrieval Functions
function testDataRetrieval() {
    echo "Testing data retrieval functions...\n";
    
    // Test getTopUsersByWithdrawals function
    try {
        $topUsers = getTopUsersByWithdrawals(5);
        echo "  ✅ getTopUsersByWithdrawals() executed successfully\n";
        echo "  📊 Retrieved " . count($topUsers) . " users\n";
        
        if (!empty($topUsers)) {
            $firstUser = $topUsers[0];
            $requiredFields = ['user_id', 'first_name', 'successful_withdraw', 'total_referrals'];
            
            foreach ($requiredFields as $field) {
                if (!isset($firstUser[$field])) {
                    echo "    ❌ Missing required field: {$field}\n";
                    return false;
                }
            }
            echo "  ✅ User data structure is correct\n";
        }
        
        return true;
    } catch (Exception $e) {
        echo "  ❌ Error in data retrieval: " . $e->getMessage() . "\n";
        return false;
    }
}

// Test 4: Rank Emoji Function
function testRankEmoji() {
    echo "Testing rank emoji function...\n";
    
    $expectedEmojis = [
        1 => '🥇',
        2 => '🥈', 
        3 => '🥉',
        4 => '🏅',
        5 => '🏅',
        6 => '📍',
        10 => '📍',
        15 => '📍'
    ];
    
    foreach ($expectedEmojis as $rank => $expectedEmoji) {
        $actualEmoji = getRankEmoji($rank);
        if ($actualEmoji !== $expectedEmoji) {
            echo "  ❌ Rank {$rank}: Expected {$expectedEmoji}, got {$actualEmoji}\n";
            return false;
        }
        echo "  ✅ Rank {$rank}: {$actualEmoji}\n";
    }
    
    return true;
}

// Test 5: Message Format Validation
function testMessageFormat() {
    global $capturedMessages;
    
    echo "Testing message format and content...\n";
    
    $adminId = ADMIN_IDS[0];
    $testChatId = 12345;
    
    // Clear previous messages
    $capturedMessages = [];
    
    // Execute command
    handleRankCommand($adminId, $testChatId);
    
    if (empty($capturedMessages)) {
        echo "  ❌ No message captured\n";
        return false;
    }
    
    $message = $capturedMessages[0]['message'];
    $parseMode = $capturedMessages[0]['parseMode'];
    
    // Check parse mode
    if ($parseMode !== 'HTML') {
        echo "  ❌ Parse mode should be HTML, got: " . ($parseMode ?? 'null') . "\n";
        return false;
    }
    echo "  ✅ Parse mode is HTML\n";
    
    // Check required message components
    $requiredComponents = [
        '🏆 TOP WITHDRAWAL RANKINGS',
        'Top 15 Users by Total Successful Withdrawals',
        'SUMMARY STATISTICS',
        'Rankings based on total successful withdrawal amounts',
        'Generated:'
    ];
    
    foreach ($requiredComponents as $component) {
        if (strpos($message, $component) === false) {
            echo "  ❌ Missing component: {$component}\n";
            return false;
        }
    }
    echo "  ✅ All required message components present\n";
    
    return true;
}

// Test 6: Performance Test
function testPerformance() {
    echo "Testing performance...\n";
    
    $adminId = ADMIN_IDS[0];
    $testChatId = 12345;
    
    $startTime = microtime(true);
    
    // Execute command multiple times
    for ($i = 0; $i < 3; $i++) {
        handleRankCommand($adminId, $testChatId);
    }
    
    $endTime = microtime(true);
    $totalTime = ($endTime - $startTime) * 1000; // Convert to milliseconds
    $averageTime = $totalTime / 3;
    
    echo "  📊 Average execution time: " . round($averageTime, 2) . "ms\n";
    
    if ($averageTime > 5000) { // 5 seconds
        echo "  ⚠️  Warning: Execution time is high\n";
        return false;
    } else if ($averageTime > 2000) { // 2 seconds
        echo "  ⚠️  Execution time is acceptable but could be optimized\n";
    } else {
        echo "  ✅ Performance is good\n";
    }
    
    return true;
}

// Test 7: Storage Mode Compatibility
function testStorageCompatibility() {
    echo "Testing storage mode compatibility...\n";
    
    $currentMode = STORAGE_MODE;
    echo "  Current storage mode: {$currentMode}\n";
    
    if ($currentMode === 'json') {
        // Test JSON mode
        if (!file_exists(USERS_FILE)) {
            echo "  ❌ Users file does not exist: " . USERS_FILE . "\n";
            return false;
        }
        
        if (!is_readable(USERS_FILE)) {
            echo "  ❌ Users file is not readable\n";
            return false;
        }
        
        $users = readJsonFile(USERS_FILE);
        if ($users === false || !is_array($users)) {
            echo "  ❌ Cannot read users data from JSON file\n";
            return false;
        }
        
        echo "  ✅ JSON storage mode working correctly\n";
        echo "  📊 Total users in file: " . count($users) . "\n";
        
    } else if ($currentMode === 'mysql') {
        // Test MySQL mode
        try {
            $pdo = getDB();
            $stmt = $pdo->query("SELECT COUNT(*) as total FROM users");
            $result = $stmt->fetch();
            echo "  ✅ MySQL storage mode working correctly\n";
            echo "  📊 Total users in database: " . $result['total'] . "\n";
        } catch (Exception $e) {
            echo "  ❌ MySQL connection failed: " . $e->getMessage() . "\n";
            return false;
        }
    } else {
        echo "  ❌ Unknown storage mode: {$currentMode}\n";
        return false;
    }
    
    return true;
}

// Test 8: Edge Cases
function testEdgeCases() {
    echo "Testing edge cases...\n";
    
    // Test with different limits
    try {
        $result1 = getTopUsersByWithdrawals(1);
        $result5 = getTopUsersByWithdrawals(5);
        $result15 = getTopUsersByWithdrawals(15);
        $result100 = getTopUsersByWithdrawals(100);
        
        echo "  ✅ Different limits work correctly\n";
        echo "    Limit 1: " . count($result1) . " users\n";
        echo "    Limit 5: " . count($result5) . " users\n";
        echo "    Limit 15: " . count($result15) . " users\n";
        echo "    Limit 100: " . count($result100) . " users\n";
        
        return true;
    } catch (Exception $e) {
        echo "  ❌ Edge case test failed: " . $e->getMessage() . "\n";
        return false;
    }
}

// Run all tests
echo "🚀 Starting Comprehensive /rank Command Test Suite\n";
echo "==================================================\n";

runTest("Admin Access Verification", "testAdminAccess");
runTest("Non-Admin Rejection", "testNonAdminRejection");
runTest("Data Retrieval Functions", "testDataRetrieval");
runTest("Rank Emoji Function", "testRankEmoji");
runTest("Message Format Validation", "testMessageFormat");
runTest("Performance Test", "testPerformance");
runTest("Storage Mode Compatibility", "testStorageCompatibility");
runTest("Edge Cases", "testEdgeCases");

// Display results
echo "\n" . str_repeat("=", 50) . "\n";
echo "🎯 TEST RESULTS SUMMARY\n";
echo str_repeat("=", 50) . "\n";

foreach ($testResults as $testName => $result) {
    $status = ($result === 'PASSED') ? '✅' : '❌';
    echo "{$status} {$testName}: {$result}\n";
}

echo "\n📊 OVERALL RESULTS:\n";
echo "Total Tests: {$totalTests}\n";
echo "Passed: {$passedTests}\n";
echo "Failed: " . ($totalTests - $passedTests) . "\n";
echo "Success Rate: " . round(($passedTests / $totalTests) * 100, 1) . "%\n";

if ($passedTests === $totalTests) {
    echo "\n🎉 ALL TESTS PASSED! The /rank command is working correctly.\n";
} else {
    echo "\n⚠️  Some tests failed. Please review the issues above.\n";
}

echo "\n📋 NEXT STEPS:\n";
echo "1. Review any failed tests and fix issues\n";
echo "2. Test the command manually in Telegram\n";
echo "3. Monitor performance with real user data\n";
echo "4. Check error logs for any runtime issues\n";
?>
