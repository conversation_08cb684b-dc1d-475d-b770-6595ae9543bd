<?php
// Core functions for the Telegram bot
// This file contains all the utility functions and should not be modified

// Define constants if not already defined
if (!defined('BANNED_TEXT')) {
    define('BANNED_TEXT', "<i>🚫 You are banned from using this bot.</i>");
}
if (!defined('MAINTENANCE_TEXT')) {
    define('MAINTENANCE_TEXT', "<i>⚙️ Bot is currently under maintenance, please try again later.</i>");
}

// Storage initialization
function initializeStorage() {
    if (STORAGE_MODE === 'json') {
        initializeJsonStorage();
    } else {
        initializeMysqlStorage();
    }
}

// JSON Storage initialization
function initializeJsonStorage() {
    // Create data directory if it doesn't exist
    if (!is_dir(DATA_DIR)) {
        mkdir(DATA_DIR, 0755, true);
    }

    // Initialize JSON files if they don't exist
    $adminDefaults = [
        'main_channel' => '',
        'private_logs_channel' => '',
        'maintenance_status' => 'Off',
        'otp_website_api_key' => '',
        'per_refer_amount' => 0,
        'joining_bonus_amount' => 0,
        'gift_channel' => '',
        'gift_amount' => 0,
        'force_sub_channels' => [], // Array of force subscription channels
        'level_rewards_enabled' => true, // Enable/disable level rewards system
        'level_rewards_config' => [
            'referral_requirements' => [1, 5, 10, 15, 20, 25], // Default referral requirements for 6 levels
            'bonus_amounts' => [2, 10, 15, 20, 25, 30] // Default bonus amounts for 6 levels
        ]
    ];

    // Create admin settings for all admin IDs
    $adminSettings = [];
    $allAdminIds = getAllAdminIds();
    foreach ($allAdminIds as $adminId) {
        $adminSettings[$adminId] = $adminDefaults;
    }

    // Add gift broadcast storage (admin_id = 0)
    $giftBroadcastDefaults = $adminDefaults;
    $giftBroadcastDefaults['gift_broadcast_id'] = '';
    $adminSettings[0] = $giftBroadcastDefaults;

    $defaultFiles = [
        USERS_FILE => [],
        ADMIN_FILE => $adminSettings,
        SESSIONS_FILE => [],
        BOT_INFO_FILE => [
            'username' => '',
            'first_name' => ''
        ],
        DATA_DIR . 'tasks.json' => [],
        DATA_DIR . 'gift_codes.json' => [],
        DATA_DIR . 'task_submissions.json' => [],
        DATA_DIR . 'broadcast_logs.json' => []
    ];

    foreach ($defaultFiles as $file => $defaultData) {
        if (!file_exists($file)) {
            file_put_contents($file, json_encode($defaultData, JSON_PRETTY_PRINT));
        }
    }
}

// MySQL Storage initialization
function initializeMysqlStorage() {
    // Database connection will be handled by getDB() function
}

// Database connection (only used in MySQL mode)
function getDB() {
    if (STORAGE_MODE !== 'mysql') {
        throw new Exception("Database connection attempted in JSON mode");
    }

    static $pdo = null;

    if ($pdo === null) {
        try {
            $pdo = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                DB_USER,
                DB_PASS,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false
                ]
            );
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            die("Database connection failed");
        }
    }

    return $pdo;
}

// JSON file operations
function readJsonFile($filename) {
    if (!file_exists($filename)) {
        return [];
    }

    $fp = fopen($filename, 'r');
    if (!$fp) {
        error_log("Failed to open file for reading: " . $filename);
        return [];
    }

    // Acquire a shared lock
    if (flock($fp, LOCK_SH)) {
        $content = '';
        while (!feof($fp)) {
            $content .= fread($fp, 8192);
        }
        flock($fp, LOCK_UN); // Release the lock
    } else {
        error_log("Failed to acquire shared lock for file: " . $filename);
        fclose($fp);
        return []; // Or handle error appropriately
    }
    fclose($fp);

    $data = json_decode($content, true);

    if ($data === null) {
        if ($content === '' || strtolower($content) === 'null' || trim($content) === '[]') {
            // File is empty, explicitly "null", or an empty JSON array "[]". Treat as legitimately empty.
            error_log("[ReadJsonFileDebug] File content is empty, 'null', or '[]', treating as empty array: " . $filename . ". Raw content (if not empty): " . $content);
        } else {
            // json_decode failed for non-empty, non-"null", non-"[]" content. This is a potential corruption or invalid JSON.
            error_log("[ReadJsonFileDebug] Failed to decode JSON from file: " . $filename . ". Content snippet: " . substr($content, 0, 500)); // Log more content
        }
        return []; // In all cases where $data is null after json_decode, return an empty array.
    }

    return $data;
}

function writeJsonFile($filename, $data) {
    $jsonData = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

    if ($jsonData === false) {
        error_log("Failed to encode JSON data for file: " . $filename);
        return false;
    }

    $result = file_put_contents($filename, $jsonData, LOCK_EX);

    if ($result === false) {
        error_log("Failed to write JSON file: " . $filename);
        return false;
    }

    return true;
}

// Telegram API helper function
function telegramRequest($method, $data = []) {
    $url = TELEGRAM_API_URL . $method;

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    if (curl_errno($ch)) {
        error_log("Telegram API cURL error: " . curl_error($ch));
        curl_close($ch);
        return false;
    }

    curl_close($ch);

    if ($httpCode !== 200) {
        error_log("Telegram API HTTP error: " . $httpCode . " - " . $response);
        return false;
    }

    $decoded = json_decode($response, true);

    if (!$decoded || !$decoded['ok']) {
        error_log("Telegram API response error: " . $response);
        return false;
    }

    return $decoded['result'];
}

// Send message function
function sendMessage($chatId, $text, $replyMarkup = null, $parseMode = 'HTML', $disableWebPagePreview = false) {
    $data = [
        'chat_id' => $chatId,
        'text' => $text,
        'parse_mode' => $parseMode,
        'disable_web_page_preview' => $disableWebPagePreview
    ];

    if ($replyMarkup) {
        $data['reply_markup'] = $replyMarkup;
    }

    return telegramRequest('sendMessage', $data);
}

// Edit message function
function editMessageText($chatId, $messageId, $text, $replyMarkup = null, $parseMode = 'HTML') {
    $data = [
        'chat_id' => $chatId,
        'message_id' => $messageId,
        'text' => $text,
        'parse_mode' => $parseMode
    ];

    if ($replyMarkup) {
        $data['reply_markup'] = $replyMarkup;
    }

    return telegramRequest('editMessageText', $data);
}

// Delete message function
function deleteMessage($chatId, $messageId) {
    return telegramRequest('deleteMessage', [
        'chat_id' => $chatId,
        'message_id' => $messageId
    ]);
}

// Answer callback query function
function answerCallbackQuery($callbackQueryId, $text, $showAlert = false) {
    $params = [
        'callback_query_id' => $callbackQueryId,
        'text' => $text,
        'show_alert' => $showAlert
    ];

    return telegramRequest('answerCallbackQuery', $params);
}

// Send photo function
function sendPhoto($chatId, $photo, $caption = '', $keyboard = null, $parseMode = null) {
    // Check if photo is a file path or URL
    if (is_string($photo) && file_exists($photo)) {
        // Handle local file upload
        $params = [
            'chat_id' => $chatId,
            'caption' => $caption
        ];

        if ($keyboard) {
            $params['reply_markup'] = json_encode($keyboard);
        }

        if ($parseMode) {
            $params['parse_mode'] = $parseMode;
        }

        // Use cURL for file upload
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.telegram.org/bot' . BOT_TOKEN . '/sendPhoto');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        // Add file to params
        $params['photo'] = new CURLFile($photo);

        curl_setopt($ch, CURLOPT_POSTFIELDS, $params);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        return $httpCode === 200 && $response !== false;
    } else {
        // Handle URL or file_id
        $params = [
            'chat_id' => $chatId,
            'photo' => $photo,
            'caption' => $caption
        ];

        if ($keyboard) {
            $params['reply_markup'] = json_encode($keyboard);
        }

        if ($parseMode) {
            $params['parse_mode'] = $parseMode;
        }

        return telegramRequest('sendPhoto', $params);
    }
}

// Forward message function
function forwardMessage($chatId, $fromChatId, $messageId) {
    $params = [
        'chat_id' => $chatId,
        'from_chat_id' => $fromChatId,
        'message_id' => $messageId
    ];

    return telegramRequest('forwardMessage', $params);
}

// Send video function
function sendVideo($chatId, $video, $caption = '', $keyboard = null, $parseMode = null) {
    $params = [
        'chat_id' => $chatId,
        'video' => $video,
        'caption' => $caption
    ];

    if ($keyboard) {
        $params['reply_markup'] = json_encode($keyboard);
    }

    if ($parseMode) {
        $params['parse_mode'] = $parseMode;
    }

    return telegramRequest('sendVideo', $params);
}

// Send document function
function sendDocument($chatId, $document, $caption = '', $keyboard = null, $parseMode = null) {
    $params = [
        'chat_id' => $chatId,
        'document' => $document,
        'caption' => $caption
    ];

    if ($keyboard) {
        $params['reply_markup'] = json_encode($keyboard);
    }

    if ($parseMode) {
        $params['parse_mode'] = $parseMode;
    }

    return telegramRequest('sendDocument', $params);
}

// Send audio function
function sendAudio($chatId, $audio, $caption = '', $keyboard = null, $parseMode = null) {
    $params = [
        'chat_id' => $chatId,
        'audio' => $audio,
        'caption' => $caption
    ];

    if ($keyboard) {
        $params['reply_markup'] = json_encode($keyboard);
    }

    if ($parseMode) {
        $params['parse_mode'] = $parseMode;
    }

    return telegramRequest('sendAudio', $params);
}

// Send voice function
function sendVoice($chatId, $voice, $caption = '', $keyboard = null, $parseMode = null) {
    $params = [
        'chat_id' => $chatId,
        'voice' => $voice
    ];

    if (!empty($caption)) {
        $params['caption'] = $caption;
    }

    if ($keyboard) {
        $params['reply_markup'] = json_encode($keyboard);
    }

    if ($parseMode) {
        $params['parse_mode'] = $parseMode;
    }

    return telegramRequest('sendVoice', $params);
}

// Send video note function
function sendVideoNote($chatId, $videoNote, $keyboard = null) {
    $params = [
        'chat_id' => $chatId,
        'video_note' => $videoNote
    ];

    if ($keyboard) {
        $params['reply_markup'] = json_encode($keyboard);
    }

    return telegramRequest('sendVideoNote', $params);
}

// Get chat member function
function getChatMember($chatId, $userId) {
    $apiChatId = $chatId; // Default to using $chatId as is (for numeric IDs)

    // If $chatId is a string (username) and doesn't already start with '@', prepend it.
    if (!is_numeric($chatId) && strpos($chatId, '@') !== 0) {
        $apiChatId = '@' . $chatId;
    }

    $params = [
        'chat_id' => $apiChatId,
        'user_id' => $userId
    ];
    // Log parameters being sent to Telegram API for getChatMember
    error_log("[GiftClaimDebug] getChatMember: Sending params to Telegram: " . json_encode($params));
    $response = telegramRequest('getChatMember', $params);
    // Log raw response from Telegram API for getChatMember
    error_log("[GiftClaimDebug] getChatMember: Raw response from Telegram for user {$userId} in chat '{$apiChatId}': " . json_encode($response));
    return $response;
}

// Get chat information function
function getChat($chatId) {
    $apiChatId = $chatId;

    // If $chatId is a string (username) and doesn't already start with '@', prepend it.
    if (!is_numeric($chatId) && strpos($chatId, '@') !== 0) {
        $apiChatId = '@' . $chatId;
    }

    $params = [
        'chat_id' => $apiChatId
    ];

    return telegramRequest('getChat', $params);
}

// Check if bot is admin in channel
function isBotAdminInChannel($chatId) {
    $botInfo = getBotInfo();
    if (!$botInfo) {
        return false;
    }

    $botId = $botInfo['id'];
    $chatMember = getChatMember($chatId, $botId);

    if (!$chatMember) {
        return false;
    }

    return in_array($chatMember['status'], ['administrator', 'creator']);
}

// Verify channel exists and bot has admin access
function verifyChannelAccess($channelUsername) {
    // First check if channel exists
    $chatInfo = getChat($channelUsername);
    if (!$chatInfo) {
        return [
            'success' => false,
            'error' => 'Channel not found or not accessible. Please check the channel username.'
        ];
    }

    // Check if it's actually a channel
    if ($chatInfo['type'] !== 'channel') {
        return [
            'success' => false,
            'error' => 'This is not a channel. Please provide a valid channel username.'
        ];
    }

    // Check if bot is admin
    if (!isBotAdminInChannel($channelUsername)) {
        return [
            'success' => false,
            'error' => 'Bot is not an administrator in this channel. Please add the bot as admin first.'
        ];
    }

    return [
        'success' => true,
        'channel_info' => $chatInfo
    ];
}

// Enhanced channel verification for private channels
function verifyPrivateChannelAccess($inviteLink) {
    // Extract channel information from invite link
    if (!preg_match('/(?:https?:\/\/)?(?:www\.)?(?:t\.me\/|telegram\.me\/)\+([a-zA-Z0-9_-]+)/', $inviteLink, $matches)) {
        return [
            'success' => false,
            'error' => 'Invalid invite link format. Please provide a valid Telegram invite link (e.g., https://t.me/+AbCdEfGhIjKlMnOp).'
        ];
    }

    $inviteToken = $matches[1];
    error_log("[PrivateChannelDebug] Extracted invite token: {$inviteToken}");

    // For private channels, we need to use a different approach since getChat doesn't work with invite links
    // We'll try to get updates and look for any messages from this channel to extract the chat ID
    // Alternative: Use exportChatInviteLink to verify bot admin status

    // First, let's try to get bot info to ensure API is working
    $botInfo = getBotInfo();
    if (!$botInfo) {
        return [
            'success' => false,
            'error' => 'Unable to connect to Telegram API. Please check bot token and network connection.'
        ];
    }

    error_log("[PrivateChannelDebug] Bot info retrieved successfully: " . $botInfo['username']);

    // For private channels, we need to use a workaround since direct getChat with invite links doesn't work
    // We'll attempt to use getChatMember with the bot's own ID to test access
    // But first we need to extract the actual chat ID from the invite link

    // Try alternative approach: attempt to get chat administrators
    // This will only work if the bot is already in the channel
    $chatId = null;

    // Since we can't directly get chat info from invite link, we'll use a different strategy:
    // 1. Check if the invite link format is valid
    // 2. Provide instructions for manual verification
    // 3. Allow admin to proceed with a warning

    // Try advanced verification method
    $advancedResult = verifyPrivateChannelAdvanced($inviteLink, $inviteToken);
    if ($advancedResult['success']) {
        return $advancedResult;
    }

    // If advanced verification fails, provide manual verification option
    return [
        'success' => true,
        'channel_info' => [
            'type' => 'channel',
            'title' => 'Private Channel (Manual Verification)',
            'id' => null, // Will be determined during actual usage
            'invite_link' => $inviteLink
        ],
        'channel_id' => null,
        'warning' => 'Private channel verification requires manual confirmation. Please ensure: 1) The bot has been added to the channel, 2) The bot has admin permissions, 3) The invite link is valid and active.',
        'manual_verification' => true,
        'advanced_error' => $advancedResult['error'] ?? 'Advanced verification failed'
    ];
}

// Advanced private channel verification using multiple methods
function verifyPrivateChannelAdvanced($inviteLink, $inviteToken) {
    error_log("[AdvancedPrivateChannelDebug] Starting advanced verification for invite link: {$inviteLink}");

    // Method 1: Try to use approveChatJoinRequest API to test if bot has access
    // This will fail gracefully if bot doesn't have access
    $testResult = telegramRequest('approveChatJoinRequest', [
        'chat_id' => $inviteLink,
        'user_id' => 0 // Invalid user ID to test access without actually approving anyone
    ]);

    // If we get a specific error about user not found, it means we have access to the chat
    if ($testResult === false) {
        $lastError = error_get_last();
        error_log("[AdvancedPrivateChannelDebug] approveChatJoinRequest test result: " . json_encode($lastError));
    }

    // Method 2: Try to get chat administrators
    // This will work if bot is admin in the channel
    $adminResult = telegramRequest('getChatAdministrators', ['chat_id' => $inviteLink]);
    if ($adminResult && is_array($adminResult)) {
        error_log("[AdvancedPrivateChannelDebug] Successfully got chat administrators");

        // Check if bot is in the admin list
        $botInfo = getBotInfo();
        $botId = $botInfo['id'];
        $isBotAdmin = false;
        $chatTitle = 'Private Channel';
        $chatId = null;

        foreach ($adminResult as $admin) {
            if ($admin['user']['id'] == $botId) {
                $isBotAdmin = true;
                error_log("[AdvancedPrivateChannelDebug] Bot found as admin with status: " . $admin['status']);
                break;
            }
        }

        if ($isBotAdmin) {
            // Try to get more chat info
            $chatInfo = telegramRequest('getChat', ['chat_id' => $inviteLink]);
            if ($chatInfo) {
                $chatTitle = $chatInfo['title'] ?? 'Private Channel';
                $chatId = $chatInfo['id'] ?? null;
                error_log("[AdvancedPrivateChannelDebug] Got chat info - Title: {$chatTitle}, ID: {$chatId}");
            }

            return [
                'success' => true,
                'channel_info' => [
                    'type' => 'channel',
                    'title' => $chatTitle,
                    'id' => $chatId,
                    'invite_link' => $inviteLink
                ],
                'channel_id' => $chatId,
                'verification_method' => 'getChatAdministrators'
            ];
        } else {
            return [
                'success' => false,
                'error' => 'Bot is not an administrator in this private channel. Please add the bot as admin with the following permissions: Delete messages, Ban users, Invite users, Pin messages.'
            ];
        }
    }

    // Method 3: Try exportChatInviteLink to test admin permissions
    $exportResult = telegramRequest('exportChatInviteLink', ['chat_id' => $inviteLink]);
    if ($exportResult) {
        error_log("[AdvancedPrivateChannelDebug] Successfully exported chat invite link");

        // If we can export invite link, we have admin access
        $chatInfo = telegramRequest('getChat', ['chat_id' => $inviteLink]);
        $chatTitle = $chatInfo['title'] ?? 'Private Channel';
        $chatId = $chatInfo['id'] ?? null;

        return [
            'success' => true,
            'channel_info' => [
                'type' => 'channel',
                'title' => $chatTitle,
                'id' => $chatId,
                'invite_link' => $inviteLink
            ],
            'channel_id' => $chatId,
            'verification_method' => 'exportChatInviteLink'
        ];
    }

    error_log("[AdvancedPrivateChannelDebug] All verification methods failed");

    return [
        'success' => false,
        'error' => 'Unable to verify private channel access. This could mean: 1) The invite link is invalid or expired, 2) The bot has not been added to the channel, 3) The bot does not have admin permissions. Please check these requirements and try again.'
    ];
}

// Verify private channel using numeric channel ID
function verifyPrivateChannelById($channelId) {
    error_log("[PrivateChannelByIdDebug] Starting verification for channel ID: {$channelId}");

    // Validate channel ID format
    if (!preg_match('/^-100\d{10,}$/', $channelId)) {
        return [
            'success' => false,
            'error' => 'Invalid channel ID format. Private channel IDs must start with -100 followed by at least 10 digits (e.g., -1001234567890).'
        ];
    }

    // Test bot API connection first
    $botInfo = getBotInfo();
    if (!$botInfo) {
        return [
            'success' => false,
            'error' => 'Unable to connect to Telegram API. Please check bot token and network connection.'
        ];
    }

    error_log("[PrivateChannelByIdDebug] Bot info retrieved: " . $botInfo['username']);

    // Method 1: Try to get chat information
    $chatInfo = telegramRequest('getChat', ['chat_id' => $channelId]);
    if (!$chatInfo) {
        return [
            'success' => false,
            'error' => 'Unable to access channel with ID ' . $channelId . '. Please ensure: 1) The channel ID is correct, 2) The bot has been added to the channel, 3) The bot has admin permissions.'
        ];
    }

    error_log("[PrivateChannelByIdDebug] Chat info retrieved: " . json_encode($chatInfo));

    // Verify it's actually a channel
    if ($chatInfo['type'] !== 'channel') {
        return [
            'success' => false,
            'error' => 'The provided ID does not belong to a channel. Please provide a valid channel ID.'
        ];
    }

    // Method 2: Check if bot is admin
    $botId = $botInfo['id'];
    $chatMember = getChatMember($channelId, $botId);

    if (!$chatMember) {
        return [
            'success' => false,
            'error' => 'Unable to verify bot membership in channel. Please ensure the bot has been added to the channel.'
        ];
    }

    error_log("[PrivateChannelByIdDebug] Bot membership status: " . $chatMember['status']);

    if (!in_array($chatMember['status'], ['administrator', 'creator'])) {
        return [
            'success' => false,
            'error' => 'Bot is not an administrator in this channel. Current status: ' . $chatMember['status'] . '. Please make the bot an admin with the following permissions: Delete messages, Ban users, Invite users, Pin messages.'
        ];
    }

    // Method 3: Test admin permissions by getting chat administrators
    $adminResult = telegramRequest('getChatAdministrators', ['chat_id' => $channelId]);
    if (!$adminResult || !is_array($adminResult)) {
        return [
            'success' => false,
            'error' => 'Unable to retrieve channel administrators. Please verify bot has admin permissions.'
        ];
    }

    error_log("[PrivateChannelByIdDebug] Successfully verified channel with " . count($adminResult) . " administrators");

    return [
        'success' => true,
        'channel_info' => [
            'type' => 'channel',
            'title' => $chatInfo['title'] ?? 'Private Channel',
            'id' => $chatInfo['id'],
            'username' => $chatInfo['username'] ?? null
        ],
        'channel_id' => $chatInfo['id'],
        'verification_method' => 'channel_id_direct',
        'bot_status' => $chatMember['status']
    ];
}

// Detect channel type from input
function detectChannelType($input) {
    // Check if it's a private channel ID (starts with -100)
    if (preg_match('/^-100\d{10,}$/', $input)) {
        return 'private';
    }

    // Check if it's an invite link (legacy support - will be converted to ID request)
    if (preg_match('/(?:https?:\/\/)?(?:www\.)?(?:t\.me\/|telegram\.me\/)\+([a-zA-Z0-9_-]+)/', $input)) {
        return 'private_invite_link';
    }

    // Check if it's a username (public channel)
    if (preg_match('/^[a-zA-Z0-9_]+$/', $input)) {
        return 'public';
    }

    return 'unknown';
}

// Get bot info function
function getBotInfo() {
    return telegramRequest('getMe');
}

// Set webhook function
function setWebhook($url) {
    return telegramRequest('setWebhook', [
        'url' => $url
    ]);
}

// Delete webhook function
function deleteWebhook() {
    return telegramRequest('deleteWebhook');
}

// OTP API function
function sendOTP($phone, $otp, $apiKey) {
    $url = OTP_API_URL . "?API={$apiKey}&PHONE={$phone}&OTP={$otp}";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $response = curl_exec($ch);
    curl_close($ch);

    return $response;
}

// Utility functions
function isValidTelegramId($id) {
    return is_numeric($id) && $id > 0;
}

function isValidAmount($amount) {
    return is_numeric($amount) && $amount >= 0;
}

function generateOTP() {
    return rand(1000, 9999);
}

function getCurrentDate() {
    date_default_timezone_set('Asia/Kolkata');
    return date('d-m-Y H:i:s');
}

function formatCurrency($amount) {
    return '₹' . number_format($amount, 0);
}

// Configuration helper functions
function getBotConfig($key, $default = null) {
    // Get configuration from constants first, then from storage, then default
    switch ($key) {
        case 'bot_username':
            return defined('BOT_USERNAME') ? BOT_USERNAME : getBotUsername();
        case 'main_channel':
            return defined('MAIN_CHANNEL') ? MAIN_CHANNEL : getStoredAdminSetting('main_channel', '');
        case 'private_logs_channel':
            return defined('PRIVATE_LOGS_CHANNEL') ? PRIVATE_LOGS_CHANNEL : getStoredAdminSetting('private_logs_channel', '');
        case 'maintenance_mode':
            return defined('MAINTENANCE_MODE') ? (MAINTENANCE_MODE ? 'On' : 'Off') : getStoredAdminSetting('maintenance_status', 'Off');
        case 'per_refer_amount':
            return defined('PER_REFER_AMOUNT') ? PER_REFER_AMOUNT : getStoredAdminSetting('per_refer_amount', 50);
        case 'joining_bonus_amount':
            return defined('JOINING_BONUS_AMOUNT') ? JOINING_BONUS_AMOUNT : getStoredAdminSetting('joining_bonus_amount', 100);
        case 'otp_api_key':
            return defined('OTP_API_KEY') ? OTP_API_KEY : getStoredAdminSetting('otp_website_api_key', '');
        case 'gift_channel':
            return defined('GIFT_CHANNEL') ? GIFT_CHANNEL : getStoredAdminSetting('gift_channel', '');
        case 'gift_amount':
            return defined('GIFT_AMOUNT') ? GIFT_AMOUNT : getStoredAdminSetting('gift_amount', 25);
        default:
            return $default;
    }
}

function getStoredAdminSetting($field, $default = '') {
    try {
        require_once 'storage_abstraction.php';
        $adminSettings = StorageManager::getAdminSettings();
        return $adminSettings[$field] ?? $default;
    } catch (Exception $e) {
        return $default;
    }
}

function formatMessage($template, $replacements = []) {
    $message = $template;
    foreach ($replacements as $key => $value) {
        $message = str_replace('{' . $key . '}', $value, $message);
    }
    return $message;
}

function getWelcomeMessage($joiningBonusAmount, $mainChannel = null) {
    $allChannels = getAllForceSubChannels();

    // Use generic user-facing amount instead of actual configured amount
    $userDisplayAmount = getUserFacingJoiningBonusAmount();

    if (empty($allChannels)) {
        // Fallback to main channel if no force sub channels
        $template = defined('WELCOME_MESSAGE_TEMPLATE') ? WELCOME_MESSAGE_TEMPLATE :
            '🎁 Make Money Easily! Get upto ₹{bonus}!\n\n🔺 <a href="https://t.me/{channel}">Click & Join Our Channel</a>\n\n🔷 Must Join Our Channel Before Clicking On [💰GET MONEY💰]';

        return formatMessage($template, [
            'bonus' => $userDisplayAmount,
            'channel' => $mainChannel ?: getBotConfig('main_channel')
        ]);
    }

    // Build multi-channel welcome message with custom format
    $message = "<b>🎁 Make Money Easily! Get Upto ₹100!</b>\n\n";

    foreach ($allChannels as $channel) {
        $channelUsername = $channel['username'] ?? str_replace('@', '', $channel['id']);

        $message .= "🔺 <a href=\"https://t.me/{$channelUsername}\">Click & Join Our Channel</a>\n";
    }

    $message .= "\n<b>🔷 Must Join Above All Channels Before Clicking [💰GET MONEY💰]</b>\n\n";
 return $message;
}

function getInvitationMessage($perReferAmount, $joiningBonusAmount, $referralLink) {
    // Use generic user-facing amounts instead of actual configured amounts
    $userReferralAmount = getUserFacingReferralAmount();
    $userBonusAmount = getUserFacingJoiningBonusAmount();

    $template = defined('INVITATION_MESSAGE_TEMPLATE') ? INVITATION_MESSAGE_TEMPLATE :
        '🎉 Invite your friends to get money!\n\nPer Invite You Get: Up to ₹{per_refer}\n\n🔗your invitation link(👇️Click to copy)\n\n<code>✨Join me and get up to ₹{bonus}\n{referral_link}</code>';

    return formatMessage($template, [
        'per_refer' => $userReferralAmount,
        'bonus' => $userBonusAmount,
        'referral_link' => $referralLink
    ]);
}

function getWithdrawalAmounts() {
    return defined('WITHDRAWAL_AMOUNTS') ? WITHDRAWAL_AMOUNTS : [100, 200, 400, 600, 800, 1000];
}

function getMinWithdrawalAmount() {
    return defined('MIN_WITHDRAWAL_AMOUNT') ? MIN_WITHDRAWAL_AMOUNT : 100;
}

/**
 * Parse range string (e.g., "20-50") into min and max values
 * @param string $range Range string in format "min-max"
 * @param int $defaultMin Default minimum if parsing fails
 * @param int $defaultMax Default maximum if parsing fails
 * @return array Array with 'min' and 'max' keys
 */
function parseAmountRange($range, $defaultMin = 20, $defaultMax = 50) {
    if (empty($range)) {
        return ['min' => $defaultMin, 'max' => $defaultMax];
    }

    if (preg_match('/^(\d+)-(\d+)$/', $range, $matches)) {
        $min = intval($matches[1]);
        $max = intval($matches[2]);

        // Validate range
        if ($min > 0 && $max > 0 && $min <= $max) {
            return ['min' => $min, 'max' => $max];
        }
    }

    // Return defaults if parsing fails
    return ['min' => $defaultMin, 'max' => $defaultMax];
}

/**
 * Get referral amount range from admin settings
 * @param int $adminId Admin ID (optional)
 * @return array Array with 'min' and 'max' keys
 */
function getReferralAmountRange($adminId = ADMIN_ID) {
    $adminSettings = getAdminSettings($adminId);
    $range = $adminSettings['per_refer_amount_range'] ?? '';

    // If no range is set, try to create from legacy single value
    if (empty($range)) {
        $legacyAmount = $adminSettings['per_refer_amount'] ?? 50;
        $range = "20-{$legacyAmount}";
    }

    return parseAmountRange($range, 20, 50);
}

/**
 * Get joining bonus amount range from admin settings
 * @param int $adminId Admin ID (optional)
 * @return array Array with 'min' and 'max' keys
 */
function getJoiningBonusAmountRange($adminId = ADMIN_ID) {
    $adminSettings = getAdminSettings($adminId);
    $range = $adminSettings['joining_bonus_amount_range'] ?? '';

    // If no range is set, try to create from legacy single value
    if (empty($range)) {
        $legacyAmount = $adminSettings['joining_bonus_amount'] ?? 50;
        $range = "20-{$legacyAmount}";
    }

    return parseAmountRange($range, 20, 50);
}

/**
 * Generate random amount within referral range
 * @param int $adminId Admin ID (optional)
 * @return int Random amount within configured range
 */
function generateReferralAmount($adminId = ADMIN_ID) {
    $range = getReferralAmountRange($adminId);
    return rand($range['min'], $range['max']);
}

/**
 * Generate random amount within joining bonus range
 * @param int $adminId Admin ID (optional)
 * @return int Random amount within configured range
 */
function generateJoiningBonusAmount($adminId = ADMIN_ID) {
    $range = getJoiningBonusAmountRange($adminId);
    return rand($range['min'], $range['max']);
}

/**
 * Get user-facing display amount for referrals (generic maximum)
 * This hides the actual configured range from users
 * @return string Generic maximum amount for user display
 */
function getUserFacingReferralAmount() {
    return defined('USER_DISPLAY_REFERRAL_MAX') ? USER_DISPLAY_REFERRAL_MAX : '100';
}

/**
 * Get user-facing display amount for joining bonus (generic maximum)
 * This hides the actual configured range from users
 * @return string Generic maximum amount for user display
 */
function getUserFacingJoiningBonusAmount() {
    return defined('USER_DISPLAY_BONUS_MAX') ? USER_DISPLAY_BONUS_MAX : '100';
}

function isMaintenanceMode() {
    return getBotConfig('maintenance_mode') === 'On';
}

function isRateLimitEnabled() {
    return defined('RATE_LIMIT_ENABLED') ? RATE_LIMIT_ENABLED : true;
}

function getMaxRequestsPerMinute() {
    return defined('MAX_REQUESTS_PER_MINUTE') ? MAX_REQUESTS_PER_MINUTE : 30;
}

// Admin verification functions
function isAdmin($userId) {
    // Check if user ID is in the admin list
    if (defined('ADMIN_IDS') && is_array(ADMIN_IDS)) {
        return in_array($userId, ADMIN_IDS);
    }

    // Fallback to single admin ID for backward compatibility
    return defined('ADMIN_ID') && $userId == ADMIN_ID;
}

function getAllAdminIds() {
    // Return all admin IDs
    if (defined('ADMIN_IDS') && is_array(ADMIN_IDS)) {
        return ADMIN_IDS;
    }

    // Fallback to single admin ID
    return defined('ADMIN_ID') ? [ADMIN_ID] : [];
}

// Gift broadcast management functions
function setCurrentGiftBroadcast($channel, $amount, $channelData = []) {
    $broadcastData = [
        'channel' => $channel,
        'amount' => $amount,
        'created_at' => time(),
        'broadcast_id' => uniqid('gift_', true),
        'channel_type' => $channelData['type'] ?? 'public',
        'channel_id' => $channelData['id'] ?? null,
        'invite_link' => $channelData['invite_link'] ?? null,
        'channel_title' => $channelData['title'] ?? $channel
    ];

    if (STORAGE_MODE === 'json') {
        $giftFile = DATA_DIR . 'current_gift_broadcast.json';
        return file_put_contents($giftFile, json_encode($broadcastData, JSON_PRETTY_PRINT)) !== false;
    } else {
        // For MySQL, we could store in a separate table or use admin_settings
        // For now, use admin_settings with a special admin_id of 0
        require_once 'storage_abstraction.php';
        $success = StorageManager::updateAdminSetting('gift_channel', $channel, 0);
        $success = $success && StorageManager::updateAdminSetting('gift_amount', $amount, 0);
        $success = $success && StorageManager::updateAdminSetting('gift_broadcast_id', $broadcastData['broadcast_id'], 0);
        $success = $success && StorageManager::updateAdminSetting('gift_channel_type', $broadcastData['channel_type'], 0);
        $success = $success && StorageManager::updateAdminSetting('gift_channel_id', $broadcastData['channel_id'], 0);
        $success = $success && StorageManager::updateAdminSetting('gift_invite_link', $broadcastData['invite_link'], 0);
        $success = $success && StorageManager::updateAdminSetting('gift_channel_title', $broadcastData['channel_title'], 0);
        return $success;
    }
}

function getCurrentGiftBroadcast() {
    if (STORAGE_MODE === 'json') {
        $giftFile = DATA_DIR . 'current_gift_broadcast.json';
        if (file_exists($giftFile)) {
            $data = json_decode(file_get_contents($giftFile), true);
            return $data ?: null;
        }
        return null;
    } else {
        // For MySQL mode
        require_once 'storage_abstraction.php';
        $settings = StorageManager::getAdminSettings(0);
        if (!empty($settings['gift_channel']) && !empty($settings['gift_amount'])) {
            return [
                'channel' => $settings['gift_channel'],
                'amount' => $settings['gift_amount'],
                'broadcast_id' => $settings['gift_broadcast_id'] ?? 'legacy',
                'channel_type' => $settings['gift_channel_type'] ?? 'public',
                'channel_id' => $settings['gift_channel_id'] ?? null,
                'invite_link' => $settings['gift_invite_link'] ?? null,
                'channel_title' => $settings['gift_channel_title'] ?? $settings['gift_channel']
            ];
        }
        return null;
    }
}

function clearCurrentGiftBroadcast() {
    if (STORAGE_MODE === 'json') {
        $giftFile = DATA_DIR . 'current_gift_broadcast.json';
        if (file_exists($giftFile)) {
            return unlink($giftFile);
        }
        return true;
    } else {
        // For MySQL mode
        require_once 'storage_abstraction.php';
        $success = StorageManager::updateAdminSetting('gift_channel', '', 0);
        $success = $success && StorageManager::updateAdminSetting('gift_amount', 0, 0);
        $success = $success && StorageManager::updateAdminSetting('gift_broadcast_id', '', 0);
        $success = $success && StorageManager::updateAdminSetting('gift_channel_type', '', 0);
        $success = $success && StorageManager::updateAdminSetting('gift_channel_id', '', 0);
        $success = $success && StorageManager::updateAdminSetting('gift_invite_link', '', 0);
        $success = $success && StorageManager::updateAdminSetting('gift_channel_title', '', 0);
        return $success;
    }
}

// Force subscription channel management functions
function getForceSubChannels($adminId = null) {
    // Use global admin ID (0) for force subscription channels to ensure consistency across all admins
    $globalAdminId = 0;

    require_once 'storage_abstraction.php';
    $settings = StorageManager::getAdminSettings($globalAdminId);
    return isset($settings['force_sub_channels']) ? $settings['force_sub_channels'] : [];
}

function addForceSubChannel($channelData, $adminId = null) {
    // Use global admin ID (0) for force subscription channels to ensure consistency across all admins
    $globalAdminId = 0;

    require_once 'storage_abstraction.php';
    $channels = getForceSubChannels();

    // Check if channel already exists
    foreach ($channels as $channel) {
        if ($channel['id'] === $channelData['id'] || $channel['username'] === $channelData['username']) {
            return false; // Channel already exists
        }
    }

    $channels[] = $channelData;
    return StorageManager::updateAdminSetting('force_sub_channels', $channels, $globalAdminId);
}

function removeForceSubChannel($channelId, $adminId = null) {
    // Use global admin ID (0) for force subscription channels to ensure consistency across all admins
    $globalAdminId = 0;

    require_once 'storage_abstraction.php';
    $channels = getForceSubChannels();

    // Log the channel ID and type for debugging
    error_log("[ForceSubChannelRemoval] Attempting to remove channel ID: {$channelId} (type: " . gettype($channelId) . ")");

    $filteredChannels = array_filter($channels, function($channel) use ($channelId) {
        // Use loose comparison to handle both string and integer channel IDs
        return $channel['id'] != $channelId;
    });

    // Re-index array
    $filteredChannels = array_values($filteredChannels);

    // Log the removal operation for debugging
    error_log("[ForceSubChannelRemoval] Original channels count: " . count($channels));
    error_log("[ForceSubChannelRemoval] Filtered channels count: " . count($filteredChannels));

    // Log channel details for debugging
    foreach ($channels as $index => $channel) {
        $channelIdType = gettype($channel['id']);
        $channelIdValue = $channel['id'];
        $match = ($channel['id'] == $channelId) ? 'MATCH' : 'NO_MATCH';
        error_log("[ForceSubChannelRemoval] Channel {$index}: ID={$channelIdValue} (type: {$channelIdType}) - {$match}");
    }

    $result = StorageManager::updateAdminSetting('force_sub_channels', $filteredChannels, $globalAdminId);

    if ($result) {
        error_log("[ForceSubChannelRemoval] Successfully updated storage with " . count($filteredChannels) . " channels");
    } else {
        error_log("[ForceSubChannelRemoval] Failed to update storage");
    }

    return $result;
}

function getAllForceSubChannels() {
    // Get force sub channels from global storage and merge with main channel
    $allChannels = [];

    // Add main channel
    $mainChannel = getBotConfig('main_channel');
    if (!empty($mainChannel)) {
        $allChannels[] = [
            'id' => '@' . $mainChannel,
            'username' => $mainChannel,
            'title' => 'Main Channel',
            'type' => 'main'
        ];
    }

    // Add force sub channels from global storage
    $forceSubChannels = getForceSubChannels();
    foreach ($forceSubChannels as $channel) {
        // Avoid duplicates with main channel
        $exists = false;
        foreach ($allChannels as $existingChannel) {
            if ($existingChannel['id'] === $channel['id']) {
                $exists = true;
                break;
            }
        }
        if (!$exists) {
            $allChannels[] = $channel;
        }
    }

    return $allChannels;
}

function verifyUserMembershipInAllChannels($userId) {
    $allChannels = getAllForceSubChannels();
    $missingChannels = [];

    foreach ($allChannels as $channel) {
        $channelId = $channel['username'] ?? str_replace('@', '', $channel['id']);
        $chatMember = getChatMember($channelId, $userId);

        if (!$chatMember || !in_array($chatMember['status'], ['member', 'administrator', 'creator'])) {
            $missingChannels[] = $channel;
        }
    }

    return [
        'all_joined' => empty($missingChannels),
        'missing_channels' => $missingChannels
    ];
}

// Withdrawal tax and control system functions
function getWithdrawalSettings() {
    // Use global admin ID (0) for withdrawal settings to ensure consistency across all admins
    $globalAdminId = 0;

    require_once 'storage_abstraction.php';
    $settings = StorageManager::getAdminSettings($globalAdminId);

    // Return default settings if not configured
    return [
        'withdrawal_enabled' => isset($settings['withdrawal_enabled']) ? (bool)$settings['withdrawal_enabled'] : true,
        'tax_type' => isset($settings['withdrawal_tax_type']) ? $settings['withdrawal_tax_type'] : 'none',
        'tax_amount' => isset($settings['withdrawal_tax_amount']) ? (float)$settings['withdrawal_tax_amount'] : 0
    ];
}

function updateWithdrawalSettings($enabled, $taxType, $taxAmount) {
    // Use global admin ID (0) for withdrawal settings to ensure consistency across all admins
    $globalAdminId = 0;

    require_once 'storage_abstraction.php';

    $success = true;
    $success = $success && StorageManager::updateAdminSetting('withdrawal_enabled', (bool)$enabled, $globalAdminId);
    $success = $success && StorageManager::updateAdminSetting('withdrawal_tax_type', $taxType, $globalAdminId);
    $success = $success && StorageManager::updateAdminSetting('withdrawal_tax_amount', (float)$taxAmount, $globalAdminId);

    return $success;
}

function isWithdrawalEnabled() {
    $settings = getWithdrawalSettings();
    return $settings['withdrawal_enabled'];
}

function calculateWithdrawalTax($amount) {
    $settings = getWithdrawalSettings();

    if ($settings['tax_type'] === 'none' || $settings['tax_amount'] <= 0) {
        return [
            'original_amount' => $amount,
            'tax_amount' => 0,
            'final_amount' => $amount,
            'tax_type' => 'none'
        ];
    }

    $taxAmount = 0;

    if ($settings['tax_type'] === 'fixed') {
        $taxAmount = $settings['tax_amount'];
    } elseif ($settings['tax_type'] === 'percentage') {
        $taxAmount = ($amount * $settings['tax_amount']) / 100;
    }

    // Ensure tax doesn't exceed the withdrawal amount
    $taxAmount = min($taxAmount, $amount);
    $finalAmount = max(0, $amount - $taxAmount);

    return [
        'original_amount' => $amount,
        'tax_amount' => $taxAmount,
        'final_amount' => $finalAmount,
        'tax_type' => $settings['tax_type'],
        'tax_rate' => $settings['tax_amount']
    ];
}

// Task management functions
function getAllTasks() {
    if (STORAGE_MODE === 'json') {
        $tasksFile = DATA_DIR . 'tasks.json';
        if (file_exists($tasksFile)) {
            $tasks = json_decode(file_get_contents($tasksFile), true);
            return $tasks ?: [];
        }
        return [];
    } else {
        // MySQL implementation
        require_once 'database_functions.php';
        return getTasksFromDatabase();
    }
}

function getActiveTasks() {
    $allTasks = getAllTasks();
    return array_filter($allTasks, function($task) {
        return $task['status'] === 'active';
    });
}

function addTask($taskData) {
    if (STORAGE_MODE === 'json') {
        $tasksFile = DATA_DIR . 'tasks.json';
        $tasks = getAllTasks();

        $taskData['task_id'] = uniqid('task_', true);
        $taskData['created_at'] = time();

        $tasks[] = $taskData;

        return file_put_contents($tasksFile, json_encode($tasks, JSON_PRETTY_PRINT)) !== false;
    } else {
        // MySQL implementation
        require_once 'database_functions.php';
        return addTaskToDatabase($taskData);
    }
}

function updateTask($taskId, $taskData) {
    if (STORAGE_MODE === 'json') {
        $tasksFile = DATA_DIR . 'tasks.json';
        $tasks = getAllTasks();

        foreach ($tasks as &$task) {
            if ($task['task_id'] === $taskId) {
                $task = array_merge($task, $taskData);
                $task['updated_at'] = time();
                break;
            }
        }

        return file_put_contents($tasksFile, json_encode($tasks, JSON_PRETTY_PRINT)) !== false;
    } else {
        // MySQL implementation
        require_once 'database_functions.php';
        return updateTaskInDatabase($taskId, $taskData);
    }
}

function deleteTask($taskId) {
    if (STORAGE_MODE === 'json') {
        $tasksFile = DATA_DIR . 'tasks.json';
        $tasks = getAllTasks();

        $tasks = array_filter($tasks, function($task) use ($taskId) {
            return $task['task_id'] !== $taskId;
        });

        $tasks = array_values($tasks); // Re-index array

        return file_put_contents($tasksFile, json_encode($tasks, JSON_PRETTY_PRINT)) !== false;
    } else {
        // MySQL implementation
        require_once 'database_functions.php';
        return deleteTaskFromDatabase($taskId);
    }
}

function getTaskById($taskId) {
    $tasks = getAllTasks();
    foreach ($tasks as $task) {
        if ($task['task_id'] === $taskId) {
            return $task;
        }
    }
    return null;
}

// Gift code management functions
function getAllGiftCodes() {
    if (STORAGE_MODE === 'json') {
        $giftCodesFile = DATA_DIR . 'gift_codes.json';
        if (file_exists($giftCodesFile)) {
            $codes = json_decode(file_get_contents($giftCodesFile), true);
            return $codes ?: [];
        }
        return [];
    } else {
        // MySQL implementation
        require_once 'database_functions.php';
        return getGiftCodesFromDatabase();
    }
}

function addGiftCode($codeData) {
    if (STORAGE_MODE === 'json') {
        $giftCodesFile = DATA_DIR . 'gift_codes.json';
        $codes = getAllGiftCodes();

        // Check if code already exists
        foreach ($codes as $code) {
            if ($code['code'] === $codeData['code']) {
                return false; // Code already exists
            }
        }

        $codeData['created_at'] = time();
        $codeData['used_count'] = 0;

        $codes[] = $codeData;

        return file_put_contents($giftCodesFile, json_encode($codes, JSON_PRETTY_PRINT)) !== false;
    } else {
        // MySQL implementation
        require_once 'database_functions.php';
        return addGiftCodeToDatabase($codeData);
    }
}

function redeemGiftCode($code, $userId) {
    if (STORAGE_MODE === 'json') {
        $giftCodesFile = DATA_DIR . 'gift_codes.json';
        $codes = getAllGiftCodes();

        foreach ($codes as &$giftCode) {
            if ($giftCode['code'] === $code) {
                // Check if code is expired
                if (isset($giftCode['expiry_date']) && $giftCode['expiry_date'] > 0 && time() > $giftCode['expiry_date']) {
                    return ['success' => false, 'message' => 'Gift code has expired.'];
                }

                // Check if user has already redeemed this code
                if (isset($giftCode['redeemed_by']) && in_array($userId, $giftCode['redeemed_by'])) {
                    return ['success' => false, 'message' => 'You have already redeemed this gift code.'];
                }

                // Check usage limit
                if (isset($giftCode['usage_limit']) && $giftCode['usage_limit'] > 0 && $giftCode['used_count'] >= $giftCode['usage_limit']) {
                    return ['success' => false, 'message' => 'Gift code usage limit reached.'];
                }

                // Initialize redeemed_by array if not exists
                if (!isset($giftCode['redeemed_by'])) {
                    $giftCode['redeemed_by'] = [];
                }

                // Redeem the code
                $giftCode['used_count']++;
                $giftCode['last_used'] = time();
                $giftCode['redeemed_by'][] = $userId;

                // Add to user balance
                updateUserBalance($userId, $giftCode['amount'], 'add');

                // Save updated codes
                file_put_contents($giftCodesFile, json_encode($codes, JSON_PRETTY_PRINT));

                return ['success' => true, 'amount' => $giftCode['amount']];
            }
        }

        return ['success' => false, 'message' => 'Invalid gift code.'];
    } else {
        // MySQL implementation
        require_once 'database_functions.php';
        return redeemGiftCodeFromDatabase($code, $userId);
    }
}

// Task submission management functions
function addTaskSubmission($submissionData) {
    if (STORAGE_MODE === 'json') {
        $submissionsFile = DATA_DIR . 'task_submissions.json';
        $submissions = getAllTaskSubmissions();

        $submissionData['submission_id'] = uniqid('sub_', true);
        $submissionData['submitted_at'] = time();
        $submissionData['status'] = 'pending';

        $submissions[] = $submissionData;

        return file_put_contents($submissionsFile, json_encode($submissions, JSON_PRETTY_PRINT)) !== false ? $submissionData['submission_id'] : false;
    } else {
        // MySQL implementation
        require_once 'database_functions.php';
        return addTaskSubmissionToDatabase($submissionData);
    }
}

function getAllTaskSubmissions() {
    if (STORAGE_MODE === 'json') {
        $submissionsFile = DATA_DIR . 'task_submissions.json';
        if (file_exists($submissionsFile)) {
            $submissions = json_decode(file_get_contents($submissionsFile), true);
            return $submissions ?: [];
        }
        return [];
    } else {
        // MySQL implementation
        require_once 'database_functions.php';
        return getTaskSubmissionsFromDatabase();
    }
}

function updateTaskSubmissionStatus($submissionId, $status, $adminNote = '') {
    if (STORAGE_MODE === 'json') {
        $submissionsFile = DATA_DIR . 'task_submissions.json';
        $submissions = getAllTaskSubmissions();

        foreach ($submissions as &$submission) {
            if ($submission['submission_id'] === $submissionId) {
                $submission['status'] = $status;
                $submission['admin_note'] = $adminNote;
                $submission['reviewed_at'] = time();

                // If approved, add reward to user balance
                if ($status === 'approved') {
                    $task = getTaskById($submission['task_id']);
                    if ($task) {
                        updateUserBalance($submission['user_id'], $task['reward_amount'], 'add');
                    }
                }

                break;
            }
        }

        return file_put_contents($submissionsFile, json_encode($submissions, JSON_PRETTY_PRINT)) !== false;
    } else {
        // MySQL implementation
        require_once 'database_functions.php';
        return updateTaskSubmissionStatusInDatabase($submissionId, $status, $adminNote);
    }
}

function getPendingTaskSubmissions() {
    $submissions = getAllTaskSubmissions();
    return array_filter($submissions, function($submission) {
        return $submission['status'] === 'pending';
    });
}

function getUserTaskSubmissions($userId) {
    $submissions = getAllTaskSubmissions();
    return array_filter($submissions, function($submission) use ($userId) {
        return $submission['user_id'] == $userId;
    });
}

// Enhanced broadcast system functions with session-based tracking and cancellation support
function broadcastMessage($message, $adminId) {
    // Initialize broadcast session
    $broadcastId = initializeBroadcastSession($adminId, $message);

    $allUsers = getAllUsers();
    $results = [
        'broadcast_id' => $broadcastId,
        'total_users' => count($allUsers),
        'success_count' => 0,
        'failed_count' => 0,
        'blocked_count' => 0,
        'errors' => [],
        'start_time' => time(),
        'processed_users' => [],
        'status' => 'running'
    ];

    // Validate we have users to broadcast to
    if (empty($allUsers)) {
        error_log("[BroadcastError] No users found for broadcast");
        $results['errors'][] = "No users found in database";
        $results['status'] = 'failed';
        updateBroadcastSession($broadcastId, $results);
        return $results;
    }

    // Remove duplicates and ensure consistent ordering
    $allUsers = array_unique($allUsers);
    $allUsers = array_values($allUsers); // Re-index array
    $results['total_users'] = count($allUsers);

    // Detect message type
    $messageType = detectMessageType($message);

    // Log broadcast start
    error_log("[BroadcastStart] Admin {$adminId} starting {$messageType} broadcast to " . count($allUsers) . " users (Broadcast ID: {$broadcastId})");
    logBroadcastActivity($adminId, $messageType, count($allUsers), $broadcastId);

    // Update session with initial data
    updateBroadcastSession($broadcastId, $results);

    // Process users with progress tracking and cancellation support
    $processedCount = 0;
    $lastProgressUpdate = 0;

    foreach ($allUsers as $targetUserId) {
        // Check for cancellation before processing each user
        if (isBroadcastCancelled($broadcastId)) {
            error_log("[BroadcastCancelled] Broadcast {$broadcastId} cancelled by admin at user {$processedCount}/{$results['total_users']}");
            $results['status'] = 'cancelled';
            $results['cancelled_at'] = time();
            break;
        }

        $processedCount++;

        // Skip if already processed (robust duplicate prevention)
        if (in_array($targetUserId, $results['processed_users'])) {
            error_log("[BroadcastWarning] User {$targetUserId} already processed, skipping");
            continue;
        }

        // Add to processed list immediately to prevent duplicates
        $results['processed_users'][] = $targetUserId;

        try {
            $success = false;

            // Real-time progress updates every 10 users or every 30 seconds
            $currentTime = time();
            if ($processedCount % 10 === 0 || ($currentTime - $lastProgressUpdate) >= 30) {
                $progressPercent = round(($processedCount / $results['total_users']) * 100, 1);
                $estimatedTimeRemaining = calculateEstimatedTime($results['start_time'], $processedCount, $results['total_users']);

                // Send real-time progress to admin
                sendBroadcastProgress($adminId, $broadcastId, $processedCount, $results['total_users'],
                                   $results['success_count'], $results['failed_count'],
                                   $progressPercent, $estimatedTimeRemaining);

                // Update session with current progress
                updateBroadcastSession($broadcastId, $results);

                $lastProgressUpdate = $currentTime;
                error_log("[BroadcastProgress] Broadcast {$broadcastId}: {$processedCount}/{$results['total_users']} ({$progressPercent}%) - Success: {$results['success_count']}, Failed: {$results['failed_count']}, ETA: {$estimatedTimeRemaining}s");
            }

            switch ($messageType) {
                case 'text':
                    $success = sendMessage($targetUserId, $message['text'], null, 'HTML');
                    break;

                case 'photo':
                    $caption = isset($message['caption']) ? $message['caption'] : '';
                    $success = sendPhoto($targetUserId, $message['photo'][0]['file_id'], $caption, null, 'HTML');
                    break;

                case 'video':
                    $caption = isset($message['caption']) ? $message['caption'] : '';
                    $success = sendVideo($targetUserId, $message['video']['file_id'], $caption, null, 'HTML');
                    break;

                case 'document':
                    $caption = isset($message['caption']) ? $message['caption'] : '';
                    $success = sendDocument($targetUserId, $message['document']['file_id'], $caption, null, 'HTML');
                    break;

                case 'audio':
                    $caption = isset($message['caption']) ? $message['caption'] : '';
                    $success = sendAudio($targetUserId, $message['audio']['file_id'], $caption, null, 'HTML');
                    break;

                case 'voice':
                    $success = sendVoice($targetUserId, $message['voice']['file_id']);
                    break;

                case 'video_note':
                    $success = sendVideoNote($targetUserId, $message['video_note']['file_id']);
                    break;

                default:
                    $results['errors'][] = "Unsupported message type: {$messageType} for user {$targetUserId}";
                    $results['failed_count']++;
                    continue 2;
            }

            if ($success) {
                $results['success_count']++;
                error_log("[BroadcastSuccess] Message sent to user {$targetUserId}");
            } else {
                $results['failed_count']++;
                $results['errors'][] = "Failed to send {$messageType} message to user {$targetUserId}";
                error_log("[BroadcastFailed] Failed to send to user {$targetUserId}");

                // Check if user blocked the bot
                if (isUserBlocked($targetUserId)) {
                    $results['blocked_count']++;
                }
            }

            // Enhanced rate limiting for shared hosting
            // Longer delay to prevent hitting Telegram API limits
            usleep(100000); // 100ms delay (was 50ms)

        } catch (Exception $e) {
            $errorMessage = $e->getMessage();
            error_log("[BroadcastException] Error for user {$targetUserId}: " . $errorMessage);

            // Check if user blocked the bot
            if (strpos($errorMessage, 'blocked') !== false || strpos($errorMessage, 'user is deactivated') !== false) {
                $results['blocked_count']++;
            } else {
                $results['failed_count']++;
                $results['errors'][] = "User {$targetUserId}: {$errorMessage}";
            }
        }

        // Additional safety delay every 10 messages for shared hosting
        if ($processedCount % 10 === 0) {
            usleep(200000); // 200ms additional delay every 10 messages
        }
    }

    // Calculate completion stats
    $results['end_time'] = time();
    $results['duration'] = $results['end_time'] - $results['start_time'];
    $results['success_rate'] = $results['total_users'] > 0 ? round(($results['success_count'] / $results['total_users']) * 100, 2) : 0;

    // Set final status if not already set (cancelled)
    if ($results['status'] === 'running') {
        $results['status'] = 'completed';
    }

    // Log completion
    $statusText = $results['status'] === 'cancelled' ? 'CANCELLED' : 'COMPLETED';
    error_log("[Broadcast{$statusText}] Admin {$adminId} broadcast {$results['status']} (ID: {$broadcastId}). " .
              "Processed: {$processedCount}/{$results['total_users']}, Success: {$results['success_count']}, " .
              "Failed: {$results['failed_count']}, Blocked: {$results['blocked_count']}, " .
              "Duration: {$results['duration']}s, Success Rate: {$results['success_rate']}%");

    // Update final session state
    updateBroadcastSession($broadcastId, $results);

    // Send final completion message to admin
    sendBroadcastCompletion($adminId, $broadcastId, $results);

    // Log final broadcast results
    logBroadcastCompletion($adminId, $results);

    // Clean up broadcast session
    cleanupBroadcastSession($broadcastId);

    // Remove processed_users from results to keep response clean
    unset($results['processed_users']);

    return $results;
}

function detectMessageType($message) {
    if (isset($message['photo'])) {
        return 'photo';
    } elseif (isset($message['video'])) {
        return 'video';
    } elseif (isset($message['document'])) {
        return 'document';
    } elseif (isset($message['audio'])) {
        return 'audio';
    } elseif (isset($message['voice'])) {
        return 'voice';
    } elseif (isset($message['video_note'])) {
        return 'video_note';
    } elseif (isset($message['text'])) {
        return 'text';
    } else {
        return 'unknown';
    }
}

// Broadcast session management functions
function initializeBroadcastSession($adminId, $message) {
    $broadcastId = uniqid('broadcast_', true);
    $sessionData = [
        'broadcast_id' => $broadcastId,
        'admin_id' => $adminId,
        'message' => $message,
        'status' => 'initializing',
        'created_at' => time(),
        'last_update' => time(),
        'cancelled' => false
    ];

    // Store session data
    if (STORAGE_MODE === 'json') {
        $sessionFile = DATA_DIR . 'broadcast_sessions.json';
        $sessions = [];

        if (file_exists($sessionFile)) {
            $sessions = json_decode(file_get_contents($sessionFile), true) ?: [];
        }

        $sessions[$broadcastId] = $sessionData;
        file_put_contents($sessionFile, json_encode($sessions, JSON_PRETTY_PRINT));
    }

    error_log("[BroadcastSession] Initialized broadcast session {$broadcastId} for admin {$adminId}");
    return $broadcastId;
}

function updateBroadcastSession($broadcastId, $results) {
    if (STORAGE_MODE === 'json') {
        $sessionFile = DATA_DIR . 'broadcast_sessions.json';
        $sessions = [];

        if (file_exists($sessionFile)) {
            $sessions = json_decode(file_get_contents($sessionFile), true) ?: [];
        }

        if (isset($sessions[$broadcastId])) {
            $sessions[$broadcastId]['results'] = $results;
            $sessions[$broadcastId]['last_update'] = time();
            $sessions[$broadcastId]['status'] = $results['status'] ?? 'running';

            file_put_contents($sessionFile, json_encode($sessions, JSON_PRETTY_PRINT));
        }
    }
}

function isBroadcastCancelled($broadcastId) {
    if (STORAGE_MODE === 'json') {
        $sessionFile = DATA_DIR . 'broadcast_sessions.json';

        if (file_exists($sessionFile)) {
            $sessions = json_decode(file_get_contents($sessionFile), true) ?: [];
            return isset($sessions[$broadcastId]) && ($sessions[$broadcastId]['cancelled'] ?? false);
        }
    }

    return false;
}

function cancelBroadcast($broadcastId, $adminId) {
    if (STORAGE_MODE === 'json') {
        $sessionFile = DATA_DIR . 'broadcast_sessions.json';
        $sessions = [];

        if (file_exists($sessionFile)) {
            $sessions = json_decode(file_get_contents($sessionFile), true) ?: [];
        }

        if (isset($sessions[$broadcastId])) {
            $sessions[$broadcastId]['cancelled'] = true;
            $sessions[$broadcastId]['cancelled_at'] = time();
            $sessions[$broadcastId]['cancelled_by'] = $adminId;
            $sessions[$broadcastId]['status'] = 'cancelling';

            file_put_contents($sessionFile, json_encode($sessions, JSON_PRETTY_PRINT));
            error_log("[BroadcastCancel] Admin {$adminId} cancelled broadcast {$broadcastId}");
            return true;
        }
    }

    return false;
}

function getActiveBroadcast($adminId) {
    if (STORAGE_MODE === 'json') {
        $sessionFile = DATA_DIR . 'broadcast_sessions.json';

        if (file_exists($sessionFile)) {
            $sessions = json_decode(file_get_contents($sessionFile), true) ?: [];

            foreach ($sessions as $broadcastId => $session) {
                if ($session['admin_id'] == $adminId &&
                    in_array($session['status'] ?? 'unknown', ['initializing', 'running', 'cancelling'])) {
                    return $broadcastId;
                }
            }
        }
    }

    return null;
}

function cleanupBroadcastSession($broadcastId) {
    if (STORAGE_MODE === 'json') {
        $sessionFile = DATA_DIR . 'broadcast_sessions.json';

        if (file_exists($sessionFile)) {
            $sessions = json_decode(file_get_contents($sessionFile), true) ?: [];

            if (isset($sessions[$broadcastId])) {
                unset($sessions[$broadcastId]);
                file_put_contents($sessionFile, json_encode($sessions, JSON_PRETTY_PRINT));
                error_log("[BroadcastSession] Cleaned up session {$broadcastId}");
            }
        }
    }
}

function logBroadcastActivity($adminId, $messageType, $userCount, $broadcastId = null) {
    $logEntry = [
        'admin_id' => $adminId,
        'broadcast_id' => $broadcastId,
        'message_type' => $messageType,
        'user_count' => $userCount,
        'timestamp' => time(),
        'date' => date('Y-m-d H:i:s'),
        'status' => 'started'
    ];

    if (STORAGE_MODE === 'json') {
        $logFile = DATA_DIR . 'broadcast_logs.json';
        $logs = [];

        if (file_exists($logFile)) {
            $logs = json_decode(file_get_contents($logFile), true) ?: [];
        }

        $logs[] = $logEntry;

        // Keep only last 100 entries
        if (count($logs) > 100) {
            $logs = array_slice($logs, -100);
        }

        file_put_contents($logFile, json_encode($logs, JSON_PRETTY_PRINT));
    }
    // For MySQL, could implement database logging here
}

// Real-time progress and completion notification functions
function sendBroadcastProgress($adminId, $broadcastId, $processed, $total, $success, $failed, $percent, $eta) {
    $progressMessage = "📊 <b>Broadcast Progress</b>\n\n";
    $progressMessage .= "🆔 <b>ID:</b> <code>" . substr($broadcastId, -8) . "</code>\n";
    $progressMessage .= "📈 <b>Progress:</b> {$processed}/{$total} ({$percent}%)\n";
    $progressMessage .= "✅ <b>Success:</b> {$success}\n";
    $progressMessage .= "❌ <b>Failed:</b> {$failed}\n";
    $progressMessage .= "⏱️ <b>ETA:</b> " . formatTime($eta) . "\n\n";
    $progressMessage .= "💡 Send /cancel to stop the broadcast";

    // Create progress bar
    $progressBar = createProgressBar($percent);
    $progressMessage .= "\n\n{$progressBar}";

    sendMessage($adminId, $progressMessage, null, 'HTML');
}

function sendBroadcastCompletion($adminId, $broadcastId, $results) {
    $status = $results['status'];
    $statusEmoji = $status === 'completed' ? '✅' : ($status === 'cancelled' ? '🚫' : '⚠️');
    $statusText = ucfirst($status);

    $completionMessage = "{$statusEmoji} <b>Broadcast {$statusText}</b>\n\n";
    $completionMessage .= "🆔 <b>ID:</b> <code>" . substr($broadcastId, -8) . "</code>\n";
    $completionMessage .= "📊 <b>Final Statistics:</b>\n";
    $completionMessage .= "👥 <b>Total Users:</b> {$results['total_users']}\n";
    $completionMessage .= "✅ <b>Successful:</b> {$results['success_count']}\n";
    $completionMessage .= "❌ <b>Failed:</b> {$results['failed_count']}\n";
    $completionMessage .= "🚫 <b>Blocked:</b> {$results['blocked_count']}\n";
    $completionMessage .= "📈 <b>Success Rate:</b> {$results['success_rate']}%\n";
    $completionMessage .= "⏱️ <b>Duration:</b> " . formatTime($results['duration']) . "\n";

    if ($status === 'cancelled') {
        $completionMessage .= "\n🛑 <b>Broadcast was cancelled by admin</b>";
    }

    sendMessage($adminId, $completionMessage, null, 'HTML');
}

function calculateEstimatedTime($startTime, $processed, $total) {
    if ($processed <= 0) return 0;

    $elapsed = time() - $startTime;
    $rate = $processed / $elapsed; // users per second
    $remaining = $total - $processed;

    return $rate > 0 ? round($remaining / $rate) : 0;
}

function formatTime($seconds) {
    if ($seconds < 60) {
        return "{$seconds}s";
    } elseif ($seconds < 3600) {
        $minutes = floor($seconds / 60);
        $secs = $seconds % 60;
        return "{$minutes}m {$secs}s";
    } else {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        return "{$hours}h {$minutes}m";
    }
}

function createProgressBar($percent, $length = 20) {
    $filled = round(($percent / 100) * $length);
    $empty = $length - $filled;

    $bar = str_repeat('█', $filled) . str_repeat('░', $empty);
    return "▐{$bar}▌ {$percent}%";
}

function logBroadcastCompletion($adminId, $results) {
    $logEntry = [
        'admin_id' => $adminId,
        'message_type' => 'completion',
        'total_users' => $results['total_users'],
        'success_count' => $results['success_count'],
        'failed_count' => $results['failed_count'],
        'blocked_count' => $results['blocked_count'],
        'success_rate' => $results['success_rate'],
        'duration' => $results['duration'],
        'timestamp' => time(),
        'date' => date('Y-m-d H:i:s'),
        'status' => 'completed'
    ];

    if (STORAGE_MODE === 'json') {
        $logFile = DATA_DIR . 'broadcast_logs.json';
        $logs = [];

        if (file_exists($logFile)) {
            $logs = json_decode(file_get_contents($logFile), true) ?: [];
        }

        $logs[] = $logEntry;

        // Keep only last 100 entries
        if (count($logs) > 100) {
            $logs = array_slice($logs, -100);
        }

        file_put_contents($logFile, json_encode($logs, JSON_PRETTY_PRINT));
    }
    // For MySQL, could implement database logging here
}

function isUserBlocked($userId) {
    // Simple check by trying to get chat info
    // This is a basic implementation - in practice, you'd track this more systematically
    try {
        $result = telegramRequest('getChat', ['chat_id' => $userId]);
        return !$result;
    } catch (Exception $e) {
        $errorMessage = $e->getMessage();
        return (strpos($errorMessage, 'blocked') !== false ||
                strpos($errorMessage, 'user is deactivated') !== false ||
                strpos($errorMessage, 'chat not found') !== false);
    }
}

// Enhanced gift broadcast function
function broadcastGiftMessage($giftChannel, $giftAmount, $channelData, $allUsers, $adminId) {
    $results = [
        'total_users' => count($allUsers),
        'success_count' => 0,
        'failed_count' => 0,
        'blocked_count' => 0,
        'errors' => [],
        'start_time' => time(),
        'processed_users' => []
    ];

    // Validate we have users to broadcast to
    if (empty($allUsers)) {
        error_log("[GiftBroadcastError] No users found for gift broadcast");
        $results['errors'][] = "No users found in database";
        return $results;
    }

    // Log gift broadcast start
    error_log("[GiftBroadcastStart] Admin {$adminId} starting gift broadcast to " . count($allUsers) . " users for channel '{$giftChannel}' with amount ₹{$giftAmount}");
    logGiftBroadcastActivity($adminId, $giftChannel, $giftAmount, count($allUsers));

    // Generate appropriate join link and text based on channel type
    if ($channelData['type'] === 'private') {
        $joinLink = $channelData['invite_link'] ?? "https://t.me/";
        $joinText = "Click & Join Private Channel";
    } else {
        $joinLink = "https://t.me/{$channelData['username']}";
        $joinText = "Click & Join Channel";
    }

    $giftMessage = "<b>🎁 EXTRA BONUS 👇!\n\n👉 <a href=\"{$joinLink}\">{$joinText}</a>\n\n<blockquote>🟢 Must Join 👆 This Channel Before Click [🎁 Claim Bonus]</blockquote>\n\n👇👇👇👇👇</b>";

    $giftKeyboard = [
        'inline_keyboard' => [
            [
                ['text' => '🎁 Claim Bonus', 'callback_data' => 'claimBonus']
            ]
        ]
    ];

    // Process users with progress tracking
    $processedCount = 0;
    foreach ($allUsers as $targetUserId) {
        $processedCount++;

        // Skip if already processed (safety check)
        if (in_array($targetUserId, $results['processed_users'])) {
            error_log("[GiftBroadcastWarning] User {$targetUserId} already processed, skipping");
            continue;
        }

        // Add to processed list immediately
        $results['processed_users'][] = $targetUserId;

        try {
            // Log progress every 50 users
            if ($processedCount % 50 === 0) {
                error_log("[GiftBroadcastProgress] Processed {$processedCount}/{$results['total_users']} users. Success: {$results['success_count']}, Failed: {$results['failed_count']}");
            }

            $success = sendMessage($targetUserId, $giftMessage, $giftKeyboard, 'HTML', true);

            if ($success) {
                $results['success_count']++;
                error_log("[GiftBroadcastSuccess] Gift message sent to user {$targetUserId}");
            } else {
                $results['failed_count']++;
                $results['errors'][] = "Failed to send gift message to user {$targetUserId}";
                error_log("[GiftBroadcastFailed] Failed to send gift message to user {$targetUserId}");

                // Check if user blocked the bot
                if (isUserBlocked($targetUserId)) {
                    $results['blocked_count']++;
                }
            }

            // Enhanced rate limiting for shared hosting
            usleep(100000); // 100ms delay

        } catch (Exception $e) {
            $errorMessage = $e->getMessage();
            error_log("[GiftBroadcastException] Error for user {$targetUserId}: " . $errorMessage);

            // Check if user blocked the bot
            if (strpos($errorMessage, 'blocked') !== false || strpos($errorMessage, 'user is deactivated') !== false) {
                $results['blocked_count']++;
            } else {
                $results['failed_count']++;
                $results['errors'][] = "User {$targetUserId}: {$errorMessage}";
            }
        }

        // Additional safety delay every 10 messages for shared hosting
        if ($processedCount % 10 === 0) {
            usleep(200000); // 200ms additional delay every 10 messages
        }
    }

    // Calculate completion stats
    $results['end_time'] = time();
    $results['duration'] = $results['end_time'] - $results['start_time'];
    $results['success_rate'] = $results['total_users'] > 0 ? round(($results['success_count'] / $results['total_users']) * 100, 2) : 0;

    // Log completion
    error_log("[GiftBroadcastComplete] Admin {$adminId} gift broadcast finished. " .
              "Channel: {$giftChannel}, Amount: ₹{$giftAmount}, " .
              "Total: {$results['total_users']}, Success: {$results['success_count']}, " .
              "Failed: {$results['failed_count']}, Blocked: {$results['blocked_count']}, " .
              "Duration: {$results['duration']}s, Success Rate: {$results['success_rate']}%");

    // Log final gift broadcast results
    logGiftBroadcastCompletion($adminId, $giftChannel, $giftAmount, $results);

    // Remove processed_users from results to keep response clean
    unset($results['processed_users']);

    return $results;
}

// Gift broadcast logging functions
function logGiftBroadcastActivity($adminId, $giftChannel, $giftAmount, $userCount) {
    $logEntry = [
        'admin_id' => $adminId,
        'message_type' => 'gift_broadcast',
        'gift_channel' => $giftChannel,
        'gift_amount' => $giftAmount,
        'user_count' => $userCount,
        'timestamp' => time(),
        'date' => date('Y-m-d H:i:s'),
        'status' => 'started'
    ];

    if (STORAGE_MODE === 'json') {
        $logFile = DATA_DIR . 'gift_broadcast_logs.json';
        $logs = [];

        if (file_exists($logFile)) {
            $logs = json_decode(file_get_contents($logFile), true) ?: [];
        }

        $logs[] = $logEntry;

        // Keep only last 100 entries
        if (count($logs) > 100) {
            $logs = array_slice($logs, -100);
        }

        file_put_contents($logFile, json_encode($logs, JSON_PRETTY_PRINT));
    }
    // For MySQL, could implement database logging here
}

function logGiftBroadcastCompletion($adminId, $giftChannel, $giftAmount, $results) {
    $logEntry = [
        'admin_id' => $adminId,
        'message_type' => 'gift_broadcast_completion',
        'gift_channel' => $giftChannel,
        'gift_amount' => $giftAmount,
        'total_users' => $results['total_users'],
        'success_count' => $results['success_count'],
        'failed_count' => $results['failed_count'],
        'blocked_count' => $results['blocked_count'],
        'success_rate' => $results['success_rate'],
        'duration' => $results['duration'],
        'timestamp' => time(),
        'date' => date('Y-m-d H:i:s'),
        'status' => 'completed'
    ];

    if (STORAGE_MODE === 'json') {
        $logFile = DATA_DIR . 'gift_broadcast_logs.json';
        $logs = [];

        if (file_exists($logFile)) {
            $logs = json_decode(file_get_contents($logFile), true) ?: [];
        }

        $logs[] = $logEntry;

        // Keep only last 100 entries
        if (count($logs) > 100) {
            $logs = array_slice($logs, -100);
        }

        file_put_contents($logFile, json_encode($logs, JSON_PRETTY_PRINT));
    }
    // For MySQL, could implement database logging here
}

// Level Rewards system functions
function getLevelRewardsConfig($adminId = null) {
    // Use global admin ID (0) for level rewards configuration to ensure consistency across all admins
    $globalAdminId = 0;

    // Always fetch fresh data from storage to ensure real-time sync
    if (STORAGE_MODE === 'json') {
        // Force reload from file to get latest changes
        $adminFile = ADMIN_FILE;
        if (file_exists($adminFile)) {
            $adminData = json_decode(file_get_contents($adminFile), true);
            if (isset($adminData[$globalAdminId]['level_rewards_config'])) {
                $config = $adminData[$globalAdminId]['level_rewards_config'];

                // Validate configuration structure
                if (isset($config['referral_requirements']) && isset($config['bonus_amounts']) &&
                    is_array($config['referral_requirements']) && is_array($config['bonus_amounts']) &&
                    count($config['referral_requirements']) === 6 && count($config['bonus_amounts']) === 6) {
                    return $config;
                }
            }
        }
    } else {
        // For MySQL, fetch directly from database using global admin ID
        $adminSettings = getAdminSettings($globalAdminId);
        if (isset($adminSettings['level_rewards_config'])) {
            $config = $adminSettings['level_rewards_config'];

            // Validate configuration structure
            if (isset($config['referral_requirements']) && isset($config['bonus_amounts']) &&
                is_array($config['referral_requirements']) && is_array($config['bonus_amounts']) &&
                count($config['referral_requirements']) === 6 && count($config['bonus_amounts']) === 6) {
                return $config;
            }
        }
    }

    // Return default configuration if none found or invalid
    return [
        'referral_requirements' => [1, 5, 10, 15, 20, 25],
        'bonus_amounts' => [2, 10, 15, 20, 25, 30]
    ];
}

function isLevelRewardsEnabled($adminId = null) {
    // Use global admin ID (0) for level rewards configuration to ensure consistency across all admins
    $globalAdminId = 0;

    // Always fetch fresh data from storage to ensure real-time sync
    if (STORAGE_MODE === 'json') {
        // Force reload from file to get latest changes
        $adminFile = ADMIN_FILE;
        if (file_exists($adminFile)) {
            $adminData = json_decode(file_get_contents($adminFile), true);
            if (isset($adminData[$globalAdminId]['level_rewards_enabled'])) {
                return (bool)$adminData[$globalAdminId]['level_rewards_enabled'];
            }
        }
    } else {
        // For MySQL, fetch directly from database using global admin ID
        $adminSettings = getAdminSettings($globalAdminId);
        if (isset($adminSettings['level_rewards_enabled'])) {
            return (bool)$adminSettings['level_rewards_enabled'];
        }
    }

    // Default to enabled if not found
    return true;
}

function updateLevelRewardsConfig($referralRequirements, $bonusAmounts, $adminId = null) {
    // Use global admin ID (0) for level rewards configuration to ensure consistency across all admins
    $globalAdminId = 0;
    // Validate input arrays
    if (!is_array($referralRequirements) || !is_array($bonusAmounts) ||
        count($referralRequirements) !== 6 || count($bonusAmounts) !== 6) {
        return false;
    }

    // Ensure all values are numeric and positive
    foreach ($referralRequirements as $req) {
        if (!is_numeric($req) || $req < 0) {
            return false;
        }
    }

    foreach ($bonusAmounts as $amount) {
        if (!is_numeric($amount) || $amount < 0) {
            return false;
        }
    }

    $config = [
        'referral_requirements' => array_map('intval', $referralRequirements),
        'bonus_amounts' => array_map('floatval', $bonusAmounts)
    ];

    // Update configuration using global admin ID and force immediate save
    $result = updateAdminSetting('level_rewards_config', $config, $globalAdminId);

    // Force immediate cache clearing for real-time sync
    if ($result) {
        clearLevelRewardsCache();

        // Additional verification for JSON storage
        if (STORAGE_MODE === 'json') {
            // Verify the update was successful by reading back
            $verifyConfig = getLevelRewardsConfig();
            if ($verifyConfig['referral_requirements'] !== $config['referral_requirements'] ||
                $verifyConfig['bonus_amounts'] !== $config['bonus_amounts']) {
                // If verification fails, try to update again
                $result = updateAdminSetting('level_rewards_config', $config, $globalAdminId);
                if ($result) {
                    clearLevelRewardsCache();
                }
            }
        }
    }

    return $result;
}

function toggleLevelRewards($enabled, $adminId = null) {
    // Use global admin ID (0) for level rewards configuration to ensure consistency across all admins
    $globalAdminId = 0;

    $result = updateAdminSetting('level_rewards_enabled', $enabled, $globalAdminId);

    // Force file system sync for immediate availability
    if (STORAGE_MODE === 'json' && $result) {
        clearstatcache();
    }

    return $result;
}

// Clear level rewards cache for immediate updates
function clearLevelRewardsCache() {
    if (STORAGE_MODE === 'json') {
        // Clear file system cache
        clearstatcache();

        // Force garbage collection to clear any PHP internal caches
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }
    }

    // Clear any opcache if enabled
    if (function_exists('opcache_reset')) {
        opcache_reset();
    }
}

function getUserReferralCount($userId) {
    $user = getUser($userId);
    if (!$user) return 0;

    // Count referrals from promotion report
    return count($user['promotion_report'] ?? []);
}

function getUserClaimedLevels($userId) {
    $user = getUser($userId);
    if (!$user) return [];

    return $user['claimed_levels'] ?? [];
}

function getUserEligibleLevels($userId) {
    $referralCount = getUserReferralCount($userId);
    $claimedLevels = getUserClaimedLevels($userId);
    $config = getLevelRewardsConfig();

    $eligibleLevels = [];

    for ($level = 1; $level <= 6; $level++) {
        $requiredReferrals = $config['referral_requirements'][$level - 1];

        if ($referralCount >= $requiredReferrals && !in_array($level, $claimedLevels)) {
            $eligibleLevels[] = $level;
        }
    }

    return $eligibleLevels;
}

function claimLevelBonus($userId, $level) {
    $config = getLevelRewardsConfig();
    $eligibleLevels = getUserEligibleLevels($userId);

    if (!in_array($level, $eligibleLevels)) {
        return ['success' => false, 'message' => 'You are not eligible for this level bonus.'];
    }

    $bonusAmount = $config['bonus_amounts'][$level - 1];

    // Add bonus to user balance
    if (updateUserBalance($userId, $bonusAmount, 'add')) {
        // Mark level as claimed
        $user = getUser($userId);
        $claimedLevels = $user['claimed_levels'] ?? [];
        $claimedLevels[] = $level;

        // Update user's claimed levels
        if (updateUserClaimedLevels($userId, $claimedLevels)) {
            return [
                'success' => true,
                'amount' => $bonusAmount,
                'level' => $level
            ];
        }
    }

    return ['success' => false, 'message' => 'Error processing level bonus claim.'];
}

function updateUserClaimedLevels($userId, $claimedLevels) {
    if (STORAGE_MODE === 'json') {
        $users = readJsonFile(USERS_FILE);

        if (!isset($users[$userId])) {
            return false;
        }

        $users[$userId]['claimed_levels'] = $claimedLevels;

        return writeJsonFile(USERS_FILE, $users);
    } else {
        // MySQL implementation would go here
        require_once 'database_functions.php';
        return updateUserClaimedLevelsInDatabase($userId, $claimedLevels);
    }
}

function getUserCurrentLevel($userId) {
    $referralCount = getUserReferralCount($userId);
    $config = getLevelRewardsConfig();

    $currentLevel = 0;

    for ($level = 1; $level <= 6; $level++) {
        $requiredReferrals = $config['referral_requirements'][$level - 1];

        if ($referralCount >= $requiredReferrals) {
            $currentLevel = $level;
        } else {
            break;
        }
    }

    return $currentLevel;
}

function getNextLevelInfo($userId) {
    $currentLevel = getUserCurrentLevel($userId);
    $referralCount = getUserReferralCount($userId);
    $config = getLevelRewardsConfig();

    if ($currentLevel >= 6) {
        return null; // Max level reached
    }

    $nextLevel = $currentLevel + 1;
    $requiredReferrals = $config['referral_requirements'][$nextLevel - 1];
    $remainingReferrals = $requiredReferrals - $referralCount;

    return [
        'level' => $nextLevel,
        'required_referrals' => $requiredReferrals,
        'remaining_referrals' => max(0, $remainingReferrals),
        'bonus_amount' => $config['bonus_amounts'][$nextLevel - 1]
    ];
}

// Rate limiting function
function checkRateLimit($userId) {
    if (!isRateLimitEnabled()) {
        return true;
    }

    if (!defined('DATA_DIR')) {
        return true; // Skip rate limiting if DATA_DIR not defined
    }

    $rateLimitFile = DATA_DIR . 'rate_limits.json';
    $rateLimits = [];

    if (file_exists($rateLimitFile)) {
        $rateLimits = json_decode(file_get_contents($rateLimitFile), true) ?: [];
    }

    $currentTime = time();
    $oneMinuteAgo = $currentTime - 60;

    // Clean old entries
    if (isset($rateLimits[$userId])) {
        $rateLimits[$userId] = array_filter($rateLimits[$userId], function($timestamp) use ($oneMinuteAgo) {
            return $timestamp > $oneMinuteAgo;
        });
    } else {
        $rateLimits[$userId] = [];
    }

    // Check if user exceeded rate limit
    if (count($rateLimits[$userId]) >= getMaxRequestsPerMinute()) {
        return false;
    }

    // Add current request
    $rateLimits[$userId][] = $currentTime;

    // Save rate limits
    file_put_contents($rateLimitFile, json_encode($rateLimits), LOCK_EX);

    return true;
}

// Navigation utility functions for handling media-to-text transitions
function smartEditMessage($chatId, $messageId, $newText, $newKeyboard = null, $parseMode = null) {
    // First try to edit the message text (works for text-only messages)
    $editResult = editMessageText($chatId, $messageId, $newText, $newKeyboard, $parseMode);

    if ($editResult) {
        return true;
    }

    // If editing fails (likely due to media-to-text transition), delete and send new
    try {
        $deleteResult = deleteMessage($chatId, $messageId);

        if ($deleteResult) {
            // Send new text message
            return sendMessage($chatId, $newText, $newKeyboard, $parseMode);
        }
    } catch (Exception $e) {
        error_log("Error in smartEditMessage: " . $e->getMessage());
    }

    // If delete fails, just send a new message (fallback)
    return sendMessage($chatId, $newText, $newKeyboard, $parseMode);
}

function smartNavigateToTextMessage($chatId, $messageId, $text, $keyboard = null, $parseMode = 'HTML') {
    // Always try to delete the current message first for clean navigation
    try {
        $deleteResult = deleteMessage($chatId, $messageId);

        if ($deleteResult) {
            // Send new text message
            return sendMessage($chatId, $text, $keyboard, $parseMode);
        } else {
            // If delete fails, try to edit (for text-only messages)
            return editMessageText($chatId, $messageId, $text, $keyboard, $parseMode);
        }
    } catch (Exception $e) {
        // Fallback: try to edit first, then send new if that fails
        $editResult = editMessageText($chatId, $messageId, $text, $keyboard, $parseMode);

        if (!$editResult) {
            return sendMessage($chatId, $text, $keyboard, $parseMode);
        }

        return $editResult;
    }
}

function smartNavigateToPhotoMessage($chatId, $messageId, $photoPath, $caption, $keyboard = null, $parseMode = 'HTML') {
    // For photo messages, always delete current and send new
    try {
        deleteMessage($chatId, $messageId);
        return sendPhoto($chatId, $photoPath, $caption, $keyboard, $parseMode);
    } catch (Exception $e) {
        error_log("Error in smartNavigateToPhotoMessage: " . $e->getMessage());
        // Fallback to text message if photo fails
        return sendMessage($chatId, $caption, $keyboard, $parseMode);
    }
}

function isMessageWithMedia($update) {
    if (!isset($update['callback_query']['message'])) {
        return false;
    }

    $message = $update['callback_query']['message'];

    return isset($message['photo']) ||
           isset($message['video']) ||
           isset($message['document']) ||
           isset($message['audio']) ||
           isset($message['voice']) ||
           isset($message['video_note']);
}
?>
