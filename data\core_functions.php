<?php
// Core functions for the Telegram bot
// This file contains all the utility functions and should not be modified

// Define constants if not already defined
if (!defined('BANNED_TEXT')) {
    define('BANNED_TEXT', "<i>🚫 You are banned from using this bot.</i>");
}
if (!defined('MAINTENANCE_TEXT')) {
    define('MAINTENANCE_TEXT', "<i>⚙️ Bot is currently under maintenance, please try again later.</i>");
}

// Storage initialization
function initializeStorage() {
    if (STORAGE_MODE === 'json') {
        initializeJsonStorage();
    } else {
        initializeMysqlStorage();
    }
}

// JSON Storage initialization
function initializeJsonStorage() {
    // Create data directory if it doesn't exist
    if (!is_dir(DATA_DIR)) {
        mkdir(DATA_DIR, 0755, true);
    }

    // Initialize JSON files if they don't exist
    $adminDefaults = [
        'main_channel' => '',
        'private_logs_channel' => '',
        'maintenance_status' => 'Off',
        'otp_website_api_key' => '',
        'per_refer_amount' => 0,
        'joining_bonus_amount' => 0,
        'gift_channel' => '',
        'gift_amount' => 0,
        'force_sub_channels' => [], // Array of force subscription channels
        'level_rewards_enabled' => true, // Enable/disable level rewards system
        'level_rewards_config' => [
            'referral_requirements' => [1, 5, 10, 15, 20, 25], // Default referral requirements for 6 levels
            'bonus_amounts' => [2, 10, 15, 20, 25, 30] // Default bonus amounts for 6 levels
        ]
    ];

    // Create admin settings for all admin IDs
    $adminSettings = [];
    $allAdminIds = getAllAdminIds();
    foreach ($allAdminIds as $adminId) {
        $adminSettings[$adminId] = $adminDefaults;
    }

    // Add gift broadcast storage (admin_id = 0)
    $giftBroadcastDefaults = $adminDefaults;
    $giftBroadcastDefaults['gift_broadcast_id'] = '';
    $adminSettings[0] = $giftBroadcastDefaults;

    $defaultFiles = [
        USERS_FILE => [],
        ADMIN_FILE => $adminSettings,
        SESSIONS_FILE => [],
        BOT_INFO_FILE => [
            'username' => '',
            'first_name' => ''
        ],
        DATA_DIR . 'tasks.json' => [],
        DATA_DIR . 'gift_codes.json' => [],
        DATA_DIR . 'task_submissions.json' => [],
        DATA_DIR . 'broadcast_logs.json' => []
    ];

    foreach ($defaultFiles as $file => $defaultData) {
        if (!file_exists($file)) {
            file_put_contents($file, json_encode($defaultData, JSON_PRETTY_PRINT));
        }
    }
}

// MySQL Storage initialization
function initializeMysqlStorage() {
    // Database connection will be handled by getDB() function
}

// Database connection (only used in MySQL mode)
function getDB() {
    if (STORAGE_MODE !== 'mysql') {
        throw new Exception("Database connection attempted in JSON mode");
    }

    static $pdo = null;

    if ($pdo === null) {
        try {
            $pdo = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                DB_USER,
                DB_PASS,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false
                ]
            );
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            die("Database connection failed");
        }
    }

    return $pdo;
}

// JSON file operations
function readJsonFile($filename) {
    if (!file_exists($filename)) {
        return [];
    }

    $fp = fopen($filename, 'r');
    if (!$fp) {
        error_log("Failed to open file for reading: " . $filename);
        return [];
    }

    // Acquire a shared lock
    if (flock($fp, LOCK_SH)) {
        $content = '';
        while (!feof($fp)) {
            $content .= fread($fp, 8192);
        }
        flock($fp, LOCK_UN); // Release the lock
    } else {
        error_log("Failed to acquire shared lock for file: " . $filename);
        fclose($fp);
        return []; // Or handle error appropriately
    }
    fclose($fp);

    $data = json_decode($content, true);

    if ($data === null) {
        if ($content === '' || strtolower($content) === 'null' || trim($content) === '[]') {
            // File is empty, explicitly "null", or an empty JSON array "[]". Treat as legitimately empty.
            error_log("[ReadJsonFileDebug] File content is empty, 'null', or '[]', treating as empty array: " . $filename . ". Raw content (if not empty): " . $content);
        } else {
            // json_decode failed for non-empty, non-"null", non-"[]" content. This is a potential corruption or invalid JSON.
            error_log("[ReadJsonFileDebug] Failed to decode JSON from file: " . $filename . ". Content snippet: " . substr($content, 0, 500)); // Log more content
        }
        return []; // In all cases where $data is null after json_decode, return an empty array.
    }

    return $data;
}

function writeJsonFile($filename, $data) {
    $jsonData = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

    if ($jsonData === false) {
        error_log("Failed to encode JSON data for file: " . $filename);
        return false;
    }

    $result = file_put_contents($filename, $jsonData, LOCK_EX);

    if ($result === false) {
        error_log("Failed to write JSON file: " . $filename);
        return false;
    }

    return true;
}

// Telegram API helper function
function telegramRequest($method, $data = []) {
    $url = TELEGRAM_API_URL . $method;

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    if (curl_errno($ch)) {
        error_log("Telegram API cURL error: " . curl_error($ch));
        curl_close($ch);
        return false;
    }

    curl_close($ch);

    if ($httpCode !== 200) {
        error_log("Telegram API HTTP error: " . $httpCode . " - " . $response);
        return false;
    }

    $decoded = json_decode($response, true);

    if (!$decoded || !$decoded['ok']) {
        error_log("Telegram API response error: " . $response);
        return false;
    }

    return $decoded['result'];
}

// Send message function
function sendMessage($chatId, $text, $replyMarkup = null, $parseMode = 'HTML', $disableWebPagePreview = false) {
    $data = [
        'chat_id' => $chatId,
        'text' => $text,
        'parse_mode' => $parseMode,
        'disable_web_page_preview' => $disableWebPagePreview
    ];

    if ($replyMarkup) {
        $data['reply_markup'] = $replyMarkup;
    }

    return telegramRequest('sendMessage', $data);
}

// Edit message function
function editMessageText($chatId, $messageId, $text, $replyMarkup = null, $parseMode = 'HTML') {
    $data = [
        'chat_id' => $chatId,
        'message_id' => $messageId,
        'text' => $text,
        'parse_mode' => $parseMode
    ];

    if ($replyMarkup) {
        $data['reply_markup'] = $replyMarkup;
    }

    return telegramRequest('editMessageText', $data);
}

// Delete message function
function deleteMessage($chatId, $messageId) {
    return telegramRequest('deleteMessage', [
        'chat_id' => $chatId,
        'message_id' => $messageId
    ]);
}

// Answer callback query function
function answerCallbackQuery($callbackQueryId, $text, $showAlert = false) {
    $params = [
        'callback_query_id' => $callbackQueryId,
        'text' => $text,
        'show_alert' => $showAlert
    ];

    return telegramRequest('answerCallbackQuery', $params);
}

// Send photo function
function sendPhoto($chatId, $photo, $caption = '', $keyboard = null, $parseMode = null) {
    // Check if photo is a file path or URL
    if (is_string($photo) && file_exists($photo)) {
        // Handle local file upload
        $params = [
            'chat_id' => $chatId,
            'caption' => $caption
        ];

        if ($keyboard) {
            $params['reply_markup'] = json_encode($keyboard);
        }

        if ($parseMode) {
            $params['parse_mode'] = $parseMode;
        }

        // Use cURL for file upload
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.telegram.org/bot' . BOT_TOKEN . '/sendPhoto');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        // Add file to params
        $params['photo'] = new CURLFile($photo);

        curl_setopt($ch, CURLOPT_POSTFIELDS, $params);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        return $httpCode === 200 && $response !== false;
    } else {
        // Handle URL or file_id
        $params = [
            'chat_id' => $chatId,
            'photo' => $photo,
            'caption' => $caption
        ];

        if ($keyboard) {
            $params['reply_markup'] = json_encode($keyboard);
        }

        if ($parseMode) {
            $params['parse_mode'] = $parseMode;
        }

        return telegramRequest('sendPhoto', $params);
    }
}

// Forward message function
function forwardMessage($chatId, $fromChatId, $messageId) {
    $params = [
        'chat_id' => $chatId,
        'from_chat_id' => $fromChatId,
        'message_id' => $messageId
    ];

    return telegramRequest('forwardMessage', $params);
}

// Send video function
function sendVideo($chatId, $video, $caption = '', $keyboard = null, $parseMode = null) {
    $params = [
        'chat_id' => $chatId,
        'video' => $video,
        'caption' => $caption
    ];

    if ($keyboard) {
        $params['reply_markup'] = json_encode($keyboard);
    }

    if ($parseMode) {
        $params['parse_mode'] = $parseMode;
    }

    return telegramRequest('sendVideo', $params);
}

// Send document function
function sendDocument($chatId, $document, $caption = '', $keyboard = null, $parseMode = null) {
    $params = [
        'chat_id' => $chatId,
        'document' => $document,
        'caption' => $caption
    ];

    if ($keyboard) {
        $params['reply_markup'] = json_encode($keyboard);
    }

    if ($parseMode) {
        $params['parse_mode'] = $parseMode;
    }

    return telegramRequest('sendDocument', $params);
}

// Send audio function
function sendAudio($chatId, $audio, $caption = '', $keyboard = null, $parseMode = null) {
    $params = [
        'chat_id' => $chatId,
        'audio' => $audio,
        'caption' => $caption
    ];

    if ($keyboard) {
        $params['reply_markup'] = json_encode($keyboard);
    }

    if ($parseMode) {
        $params['parse_mode'] = $parseMode;
    }

    return telegramRequest('sendAudio', $params);
}

// Send voice function
function sendVoice($chatId, $voice, $caption = '', $keyboard = null, $parseMode = null) {
    $params = [
        'chat_id' => $chatId,
        'voice' => $voice
    ];

    if (!empty($caption)) {
        $params['caption'] = $caption;
    }

    if ($keyboard) {
        $params['reply_markup'] = json_encode($keyboard);
    }

    if ($parseMode) {
        $params['parse_mode'] = $parseMode;
    }

    return telegramRequest('sendVoice', $params);
}

// Send video note function
function sendVideoNote($chatId, $videoNote, $keyboard = null) {
    $params = [
        'chat_id' => $chatId,
        'video_note' => $videoNote
    ];

    if ($keyboard) {
        $params['reply_markup'] = json_encode($keyboard);
    }

    return telegramRequest('sendVideoNote', $params);
}

// Get chat member function
function getChatMember($chatId, $userId) {
    $apiChatId = $chatId; // Default to using $chatId as is (for numeric IDs)

    // If $chatId is a string (username) and doesn't already start with '@', prepend it.
    if (!is_numeric($chatId) && strpos($chatId, '@') !== 0) {
        $apiChatId = '@' . $chatId;
    }

    $params = [
        'chat_id' => $apiChatId,
        'user_id' => $userId
    ];
    // Log parameters being sent to Telegram API for getChatMember
    error_log("[GiftClaimDebug] getChatMember: Sending params to Telegram: " . json_encode($params));
    $response = telegramRequest('getChatMember', $params);
    // Log raw response from Telegram API for getChatMember
    error_log("[GiftClaimDebug] getChatMember: Raw response from Telegram for user {$userId} in chat '{$apiChatId}': " . json_encode($response));
    return $response;
}

// Get chat information function
function getChat($chatId) {
    $apiChatId = $chatId;

    // If $chatId is a string (username) and doesn't already start with '@', prepend it.
    if (!is_numeric($chatId) && strpos($chatId, '@') !== 0) {
        $apiChatId = '@' . $chatId;
    }

    $params = [
        'chat_id' => $apiChatId
    ];

    return telegramRequest('getChat', $params);
}

// Check if bot is admin in channel
function isBotAdminInChannel($chatId) {
    $botInfo = getBotInfo();
    if (!$botInfo) {
        return false;
    }

    $botId = $botInfo['id'];
    $chatMember = getChatMember($chatId, $botId);

    if (!$chatMember) {
        return false;
    }

    return in_array($chatMember['status'], ['administrator', 'creator']);
}

// Verify channel exists and bot has admin access
function verifyChannelAccess($channelUsername) {
    // First check if channel exists
    $chatInfo = getChat($channelUsername);
    if (!$chatInfo) {
        return [
            'success' => false,
            'error' => 'Channel not found or not accessible. Please check the channel username.'
        ];
    }

    // Check if it's actually a channel
    if ($chatInfo['type'] !== 'channel') {
        return [
            'success' => false,
            'error' => 'This is not a channel. Please provide a valid channel username.'
        ];
    }

    // Check if bot is admin
    if (!isBotAdminInChannel($channelUsername)) {
        return [
            'success' => false,
            'error' => 'Bot is not an administrator in this channel. Please add the bot as admin first.'
        ];
    }

    return [
        'success' => true,
        'channel_info' => $chatInfo
    ];
}

// Enhanced channel verification for private channels
function verifyPrivateChannelAccess($inviteLink) {
    // Extract channel information from invite link
    if (!preg_match('/(?:https?:\/\/)?(?:www\.)?(?:t\.me\/|telegram\.me\/)\+([a-zA-Z0-9_-]+)/', $inviteLink, $matches)) {
        return [
            'success' => false,
            'error' => 'Invalid invite link format. Please provide a valid Telegram invite link.'
        ];
    }

    // Try to get chat info using the invite link
    $chatInfo = telegramRequest('getChat', ['chat_id' => $inviteLink]);

    if (!$chatInfo) {
        return [
            'success' => false,
            'error' => 'Unable to access channel via invite link. Please ensure the link is valid and the bot has been added to the channel.'
        ];
    }

    // Check if it's actually a channel
    if ($chatInfo['type'] !== 'channel') {
        return [
            'success' => false,
            'error' => 'This is not a channel. Please provide a valid channel invite link.'
        ];
    }

    // For private channels, we need to check if bot is admin using the chat ID
    $chatId = $chatInfo['id'];
    if (!isBotAdminInChannel($chatId)) {
        return [
            'success' => false,
            'error' => 'Bot is not an administrator in this private channel. Please add the bot as admin first.'
        ];
    }

    return [
        'success' => true,
        'channel_info' => $chatInfo,
        'channel_id' => $chatId
    ];
}

// Detect channel type from input
function detectChannelType($input) {
    // Check if it's an invite link (private channel)
    if (preg_match('/(?:https?:\/\/)?(?:www\.)?(?:t\.me\/|telegram\.me\/)\+([a-zA-Z0-9_-]+)/', $input)) {
        return 'private';
    }

    // Check if it's a username (public channel)
    if (preg_match('/^[a-zA-Z0-9_]+$/', $input)) {
        return 'public';
    }

    return 'unknown';
}

// Get bot info function
function getBotInfo() {
    return telegramRequest('getMe');
}

// Set webhook function
function setWebhook($url) {
    return telegramRequest('setWebhook', [
        'url' => $url
    ]);
}

// Delete webhook function
function deleteWebhook() {
    return telegramRequest('deleteWebhook');
}

// OTP API function
function sendOTP($phone, $otp, $apiKey) {
    $url = OTP_API_URL . "?API={$apiKey}&PHONE={$phone}&OTP={$otp}";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $response = curl_exec($ch);
    curl_close($ch);

    return $response;
}

// Utility functions
function isValidTelegramId($id) {
    return is_numeric($id) && $id > 0;
}

function isValidAmount($amount) {
    return is_numeric($amount) && $amount >= 0;
}

function generateOTP() {
    return rand(1000, 9999);
}

function getCurrentDate() {
    date_default_timezone_set('Asia/Kolkata');
    return date('d-m-Y H:i:s');
}

function formatCurrency($amount) {
    return '₹' . number_format($amount, 0);
}

// Configuration helper functions
function getBotConfig($key, $default = null) {
    // Get configuration from constants first, then from storage, then default
    switch ($key) {
        case 'bot_username':
            return defined('BOT_USERNAME') ? BOT_USERNAME : getBotUsername();
        case 'main_channel':
            return defined('MAIN_CHANNEL') ? MAIN_CHANNEL : getStoredAdminSetting('main_channel', '');
        case 'private_logs_channel':
            return defined('PRIVATE_LOGS_CHANNEL') ? PRIVATE_LOGS_CHANNEL : getStoredAdminSetting('private_logs_channel', '');
        case 'maintenance_mode':
            return defined('MAINTENANCE_MODE') ? (MAINTENANCE_MODE ? 'On' : 'Off') : getStoredAdminSetting('maintenance_status', 'Off');
        case 'per_refer_amount':
            return defined('PER_REFER_AMOUNT') ? PER_REFER_AMOUNT : getStoredAdminSetting('per_refer_amount', 50);
        case 'joining_bonus_amount':
            return defined('JOINING_BONUS_AMOUNT') ? JOINING_BONUS_AMOUNT : getStoredAdminSetting('joining_bonus_amount', 100);
        case 'otp_api_key':
            return defined('OTP_API_KEY') ? OTP_API_KEY : getStoredAdminSetting('otp_website_api_key', '');
        case 'gift_channel':
            return defined('GIFT_CHANNEL') ? GIFT_CHANNEL : getStoredAdminSetting('gift_channel', '');
        case 'gift_amount':
            return defined('GIFT_AMOUNT') ? GIFT_AMOUNT : getStoredAdminSetting('gift_amount', 25);
        default:
            return $default;
    }
}

function getStoredAdminSetting($field, $default = '') {
    try {
        require_once 'storage_abstraction.php';
        $adminSettings = StorageManager::getAdminSettings();
        return $adminSettings[$field] ?? $default;
    } catch (Exception $e) {
        return $default;
    }
}

function formatMessage($template, $replacements = []) {
    $message = $template;
    foreach ($replacements as $key => $value) {
        $message = str_replace('{' . $key . '}', $value, $message);
    }
    return $message;
}

function getWelcomeMessage($joiningBonusAmount, $mainChannel = null) {
    $allChannels = getAllForceSubChannels();

    // Use generic user-facing amount instead of actual configured amount
    $userDisplayAmount = getUserFacingJoiningBonusAmount();

    if (empty($allChannels)) {
        // Fallback to main channel if no force sub channels
        $template = defined('WELCOME_MESSAGE_TEMPLATE') ? WELCOME_MESSAGE_TEMPLATE :
            '🎁 Make Money Easily! Get upto ₹{bonus}!\n\n🔺 <a href="https://t.me/{channel}">Click & Join Our Channel</a>\n\n🔷 Must Join Our Channel Before Clicking On [💰GET MONEY💰]';

        return formatMessage($template, [
            'bonus' => $userDisplayAmount,
            'channel' => $mainChannel ?: getBotConfig('main_channel')
        ]);
    }

    // Build multi-channel welcome message with custom format
    $message = "<b>🎁 Make Money Easily! Get Upto ₹100!</b>\n\n";

    foreach ($allChannels as $channel) {
        $channelUsername = $channel['username'] ?? str_replace('@', '', $channel['id']);

        $message .= "🔺 <a href=\"https://t.me/{$channelUsername}\">Click & Join Our Channel</a>\n";
    }

    $message .= "\n<b>🔷 Must Join Above All Channels Before Clicking [💰GET MONEY💰]</b>\n\n";
 return $message;
}

function getInvitationMessage($perReferAmount, $joiningBonusAmount, $referralLink) {
    // Use generic user-facing amounts instead of actual configured amounts
    $userReferralAmount = getUserFacingReferralAmount();
    $userBonusAmount = getUserFacingJoiningBonusAmount();

    $template = defined('INVITATION_MESSAGE_TEMPLATE') ? INVITATION_MESSAGE_TEMPLATE :
        '🎉 Invite your friends to get money!\n\nPer Invite You Get: Up to ₹{per_refer}\n\n🔗your invitation link(👇️Click to copy)\n\n<code>✨Join me and get up to ₹{bonus}\n{referral_link}</code>';

    return formatMessage($template, [
        'per_refer' => $userReferralAmount,
        'bonus' => $userBonusAmount,
        'referral_link' => $referralLink
    ]);
}

function getWithdrawalAmounts() {
    return defined('WITHDRAWAL_AMOUNTS') ? WITHDRAWAL_AMOUNTS : [100, 200, 400, 600, 800, 1000];
}

function getMinWithdrawalAmount() {
    return defined('MIN_WITHDRAWAL_AMOUNT') ? MIN_WITHDRAWAL_AMOUNT : 100;
}

/**
 * Parse range string (e.g., "20-50") into min and max values
 * @param string $range Range string in format "min-max"
 * @param int $defaultMin Default minimum if parsing fails
 * @param int $defaultMax Default maximum if parsing fails
 * @return array Array with 'min' and 'max' keys
 */
function parseAmountRange($range, $defaultMin = 20, $defaultMax = 50) {
    if (empty($range)) {
        return ['min' => $defaultMin, 'max' => $defaultMax];
    }

    if (preg_match('/^(\d+)-(\d+)$/', $range, $matches)) {
        $min = intval($matches[1]);
        $max = intval($matches[2]);

        // Validate range
        if ($min > 0 && $max > 0 && $min <= $max) {
            return ['min' => $min, 'max' => $max];
        }
    }

    // Return defaults if parsing fails
    return ['min' => $defaultMin, 'max' => $defaultMax];
}

/**
 * Get referral amount range from admin settings
 * @param int $adminId Admin ID (optional)
 * @return array Array with 'min' and 'max' keys
 */
function getReferralAmountRange($adminId = ADMIN_ID) {
    $adminSettings = getAdminSettings($adminId);
    $range = $adminSettings['per_refer_amount_range'] ?? '';

    // If no range is set, try to create from legacy single value
    if (empty($range)) {
        $legacyAmount = $adminSettings['per_refer_amount'] ?? 50;
        $range = "20-{$legacyAmount}";
    }

    return parseAmountRange($range, 20, 50);
}

/**
 * Get joining bonus amount range from admin settings
 * @param int $adminId Admin ID (optional)
 * @return array Array with 'min' and 'max' keys
 */
function getJoiningBonusAmountRange($adminId = ADMIN_ID) {
    $adminSettings = getAdminSettings($adminId);
    $range = $adminSettings['joining_bonus_amount_range'] ?? '';

    // If no range is set, try to create from legacy single value
    if (empty($range)) {
        $legacyAmount = $adminSettings['joining_bonus_amount'] ?? 50;
        $range = "20-{$legacyAmount}";
    }

    return parseAmountRange($range, 20, 50);
}

/**
 * Generate random amount within referral range
 * @param int $adminId Admin ID (optional)
 * @return int Random amount within configured range
 */
function generateReferralAmount($adminId = ADMIN_ID) {
    $range = getReferralAmountRange($adminId);
    return rand($range['min'], $range['max']);
}

/**
 * Generate random amount within joining bonus range
 * @param int $adminId Admin ID (optional)
 * @return int Random amount within configured range
 */
function generateJoiningBonusAmount($adminId = ADMIN_ID) {
    $range = getJoiningBonusAmountRange($adminId);
    return rand($range['min'], $range['max']);
}

/**
 * Get user-facing display amount for referrals (generic maximum)
 * This hides the actual configured range from users
 * @return string Generic maximum amount for user display
 */
function getUserFacingReferralAmount() {
    return defined('USER_DISPLAY_REFERRAL_MAX') ? USER_DISPLAY_REFERRAL_MAX : '100';
}

/**
 * Get user-facing display amount for joining bonus (generic maximum)
 * This hides the actual configured range from users
 * @return string Generic maximum amount for user display
 */
function getUserFacingJoiningBonusAmount() {
    return defined('USER_DISPLAY_BONUS_MAX') ? USER_DISPLAY_BONUS_MAX : '100';
}

function isMaintenanceMode() {
    return getBotConfig('maintenance_mode') === 'On';
}

function isRateLimitEnabled() {
    return defined('RATE_LIMIT_ENABLED') ? RATE_LIMIT_ENABLED : true;
}

function getMaxRequestsPerMinute() {
    return defined('MAX_REQUESTS_PER_MINUTE') ? MAX_REQUESTS_PER_MINUTE : 30;
}

// Admin verification functions
function isAdmin($userId) {
    // Check if user ID is in the admin list
    if (defined('ADMIN_IDS') && is_array(ADMIN_IDS)) {
        return in_array($userId, ADMIN_IDS);
    }

    // Fallback to single admin ID for backward compatibility
    return defined('ADMIN_ID') && $userId == ADMIN_ID;
}

function getAllAdminIds() {
    // Return all admin IDs
    if (defined('ADMIN_IDS') && is_array(ADMIN_IDS)) {
        return ADMIN_IDS;
    }

    // Fallback to single admin ID
    return defined('ADMIN_ID') ? [ADMIN_ID] : [];
}

// Gift broadcast management functions
function setCurrentGiftBroadcast($channel, $amount, $channelData = []) {
    $broadcastData = [
        'channel' => $channel,
        'amount' => $amount,
        'created_at' => time(),
        'broadcast_id' => uniqid('gift_', true),
        'channel_type' => $channelData['type'] ?? 'public',
        'channel_id' => $channelData['id'] ?? null,
        'invite_link' => $channelData['invite_link'] ?? null,
        'channel_title' => $channelData['title'] ?? $channel
    ];

    if (STORAGE_MODE === 'json') {
        $giftFile = DATA_DIR . 'current_gift_broadcast.json';
        return file_put_contents($giftFile, json_encode($broadcastData, JSON_PRETTY_PRINT)) !== false;
    } else {
        // For MySQL, we could store in a separate table or use admin_settings
        // For now, use admin_settings with a special admin_id of 0
        require_once 'storage_abstraction.php';
        $success = StorageManager::updateAdminSetting('gift_channel', $channel, 0);
        $success = $success && StorageManager::updateAdminSetting('gift_amount', $amount, 0);
        $success = $success && StorageManager::updateAdminSetting('gift_broadcast_id', $broadcastData['broadcast_id'], 0);
        $success = $success && StorageManager::updateAdminSetting('gift_channel_type', $broadcastData['channel_type'], 0);
        $success = $success && StorageManager::updateAdminSetting('gift_channel_id', $broadcastData['channel_id'], 0);
        $success = $success && StorageManager::updateAdminSetting('gift_invite_link', $broadcastData['invite_link'], 0);
        $success = $success && StorageManager::updateAdminSetting('gift_channel_title', $broadcastData['channel_title'], 0);
        return $success;
    }
}

function getCurrentGiftBroadcast() {
    if (STORAGE_MODE === 'json') {
        $giftFile = DATA_DIR . 'current_gift_broadcast.json';
        if (file_exists($giftFile)) {
            $data = json_decode(file_get_contents($giftFile), true);
            return $data ?: null;
        }
        return null;
    } else {
        // For MySQL mode
        require_once 'storage_abstraction.php';
        $settings = StorageManager::getAdminSettings(0);
        if (!empty($settings['gift_channel']) && !empty($settings['gift_amount'])) {
            return [
                'channel' => $settings['gift_channel'],
                'amount' => $settings['gift_amount'],
                'broadcast_id' => $settings['gift_broadcast_id'] ?? 'legacy',
                'channel_type' => $settings['gift_channel_type'] ?? 'public',
                'channel_id' => $settings['gift_channel_id'] ?? null,
                'invite_link' => $settings['gift_invite_link'] ?? null,
                'channel_title' => $settings['gift_channel_title'] ?? $settings['gift_channel']
            ];
        }
        return null;
    }
}

function clearCurrentGiftBroadcast() {
    if (STORAGE_MODE === 'json') {
        $giftFile = DATA_DIR . 'current_gift_broadcast.json';
        if (file_exists($giftFile)) {
            return unlink($giftFile);
        }
        return true;
    } else {
        // For MySQL mode
        require_once 'storage_abstraction.php';
        $success = StorageManager::updateAdminSetting('gift_channel', '', 0);
        $success = $success && StorageManager::updateAdminSetting('gift_amount', 0, 0);
        $success = $success && StorageManager::updateAdminSetting('gift_broadcast_id', '', 0);
        $success = $success && StorageManager::updateAdminSetting('gift_channel_type', '', 0);
        $success = $success && StorageManager::updateAdminSetting('gift_channel_id', '', 0);
        $success = $success && StorageManager::updateAdminSetting('gift_invite_link', '', 0);
        $success = $success && StorageManager::updateAdminSetting('gift_channel_title', '', 0);
        return $success;
    }
}

// Force subscription channel management functions
function getForceSubChannels($adminId = null) {
    // Use global admin ID (0) for force subscription channels to ensure consistency across all admins
    $globalAdminId = 0;

    require_once 'storage_abstraction.php';
    $settings = StorageManager::getAdminSettings($globalAdminId);
    return isset($settings['force_sub_channels']) ? $settings['force_sub_channels'] : [];
}

function addForceSubChannel($channelData, $adminId = null) {
    // Use global admin ID (0) for force subscription channels to ensure consistency across all admins
    $globalAdminId = 0;

    require_once 'storage_abstraction.php';
    $channels = getForceSubChannels();

    // Check if channel already exists
    foreach ($channels as $channel) {
        if ($channel['id'] === $channelData['id'] || $channel['username'] === $channelData['username']) {
            return false; // Channel already exists
        }
    }

    $channels[] = $channelData;
    return StorageManager::updateAdminSetting('force_sub_channels', $channels, $globalAdminId);
}

function removeForceSubChannel($channelId, $adminId = null) {
    // Use global admin ID (0) for force subscription channels to ensure consistency across all admins
    $globalAdminId = 0;

    require_once 'storage_abstraction.php';
    $channels = getForceSubChannels();

    // Log the channel ID and type for debugging
    error_log("[ForceSubChannelRemoval] Attempting to remove channel ID: {$channelId} (type: " . gettype($channelId) . ")");

    $filteredChannels = array_filter($channels, function($channel) use ($channelId) {
        // Use loose comparison to handle both string and integer channel IDs
        return $channel['id'] != $channelId;
    });

    // Re-index array
    $filteredChannels = array_values($filteredChannels);

    // Log the removal operation for debugging
    error_log("[ForceSubChannelRemoval] Original channels count: " . count($channels));
    error_log("[ForceSubChannelRemoval] Filtered channels count: " . count($filteredChannels));

    // Log channel details for debugging
    foreach ($channels as $index => $channel) {
        $channelIdType = gettype($channel['id']);
        $channelIdValue = $channel['id'];
        $match = ($channel['id'] == $channelId) ? 'MATCH' : 'NO_MATCH';
        error_log("[ForceSubChannelRemoval] Channel {$index}: ID={$channelIdValue} (type: {$channelIdType}) - {$match}");
    }

    $result = StorageManager::updateAdminSetting('force_sub_channels', $filteredChannels, $globalAdminId);

    if ($result) {
        error_log("[ForceSubChannelRemoval] Successfully updated storage with " . count($filteredChannels) . " channels");
    } else {
        error_log("[ForceSubChannelRemoval] Failed to update storage");
    }

    return $result;
}

function getAllForceSubChannels() {
    // Get force sub channels from global storage and merge with main channel
    $allChannels = [];

    // Add main channel
    $mainChannel = getBotConfig('main_channel');
    if (!empty($mainChannel)) {
        $allChannels[] = [
            'id' => '@' . $mainChannel,
            'username' => $mainChannel,
            'title' => 'Main Channel',
            'type' => 'main'
        ];
    }

    // Add force sub channels from global storage
    $forceSubChannels = getForceSubChannels();
    foreach ($forceSubChannels as $channel) {
        // Avoid duplicates with main channel
        $exists = false;
        foreach ($allChannels as $existingChannel) {
            if ($existingChannel['id'] === $channel['id']) {
                $exists = true;
                break;
            }
        }
        if (!$exists) {
            $allChannels[] = $channel;
        }
    }

    return $allChannels;
}

function verifyUserMembershipInAllChannels($userId) {
    $allChannels = getAllForceSubChannels();
    $missingChannels = [];

    foreach ($allChannels as $channel) {
        $channelId = $channel['username'] ?? str_replace('@', '', $channel['id']);
        $chatMember = getChatMember($channelId, $userId);

        if (!$chatMember || !in_array($chatMember['status'], ['member', 'administrator', 'creator'])) {
            $missingChannels[] = $channel;
        }
    }

    return [
        'all_joined' => empty($missingChannels),
        'missing_channels' => $missingChannels
    ];
}

// Withdrawal tax and control system functions
function getWithdrawalSettings() {
    // Use global admin ID (0) for withdrawal settings to ensure consistency across all admins
    $globalAdminId = 0;

    require_once 'storage_abstraction.php';
    $settings = StorageManager::getAdminSettings($globalAdminId);

    // Return default settings if not configured
    return [
        'withdrawal_enabled' => isset($settings['withdrawal_enabled']) ? (bool)$settings['withdrawal_enabled'] : true,
        'tax_type' => isset($settings['withdrawal_tax_type']) ? $settings['withdrawal_tax_type'] : 'none',
        'tax_amount' => isset($settings['withdrawal_tax_amount']) ? (float)$settings['withdrawal_tax_amount'] : 0
    ];
}

function updateWithdrawalSettings($enabled, $taxType, $taxAmount) {
    // Use global admin ID (0) for withdrawal settings to ensure consistency across all admins
    $globalAdminId = 0;

    require_once 'storage_abstraction.php';

    $success = true;
    $success = $success && StorageManager::updateAdminSetting('withdrawal_enabled', (bool)$enabled, $globalAdminId);
    $success = $success && StorageManager::updateAdminSetting('withdrawal_tax_type', $taxType, $globalAdminId);
    $success = $success && StorageManager::updateAdminSetting('withdrawal_tax_amount', (float)$taxAmount, $globalAdminId);

    return $success;
}

function isWithdrawalEnabled() {
    $settings = getWithdrawalSettings();
    return $settings['withdrawal_enabled'];
}

function calculateWithdrawalTax($amount) {
    $settings = getWithdrawalSettings();

    if ($settings['tax_type'] === 'none' || $settings['tax_amount'] <= 0) {
        return [
            'original_amount' => $amount,
            'tax_amount' => 0,
            'final_amount' => $amount,
            'tax_type' => 'none'
        ];
    }

    $taxAmount = 0;

    if ($settings['tax_type'] === 'fixed') {
        $taxAmount = $settings['tax_amount'];
    } elseif ($settings['tax_type'] === 'percentage') {
        $taxAmount = ($amount * $settings['tax_amount']) / 100;
    }

    // Ensure tax doesn't exceed the withdrawal amount
    $taxAmount = min($taxAmount, $amount);
    $finalAmount = max(0, $amount - $taxAmount);

    return [
        'original_amount' => $amount,
        'tax_amount' => $taxAmount,
        'final_amount' => $finalAmount,
        'tax_type' => $settings['tax_type'],
        'tax_rate' => $settings['tax_amount']
    ];
}

// Task management functions
function getAllTasks() {
    if (STORAGE_MODE === 'json') {
        $tasksFile = DATA_DIR . 'tasks.json';
        if (file_exists($tasksFile)) {
            $tasks = json_decode(file_get_contents($tasksFile), true);
            return $tasks ?: [];
        }
        return [];
    } else {
        // MySQL implementation
        require_once 'database_functions.php';
        return getTasksFromDatabase();
    }
}

function getActiveTasks() {
    $allTasks = getAllTasks();
    return array_filter($allTasks, function($task) {
        return $task['status'] === 'active';
    });
}

function addTask($taskData) {
    if (STORAGE_MODE === 'json') {
        $tasksFile = DATA_DIR . 'tasks.json';
        $tasks = getAllTasks();

        $taskData['task_id'] = uniqid('task_', true);
        $taskData['created_at'] = time();

        $tasks[] = $taskData;

        return file_put_contents($tasksFile, json_encode($tasks, JSON_PRETTY_PRINT)) !== false;
    } else {
        // MySQL implementation
        require_once 'database_functions.php';
        return addTaskToDatabase($taskData);
    }
}

function updateTask($taskId, $taskData) {
    if (STORAGE_MODE === 'json') {
        $tasksFile = DATA_DIR . 'tasks.json';
        $tasks = getAllTasks();

        foreach ($tasks as &$task) {
            if ($task['task_id'] === $taskId) {
                $task = array_merge($task, $taskData);
                $task['updated_at'] = time();
                break;
            }
        }

        return file_put_contents($tasksFile, json_encode($tasks, JSON_PRETTY_PRINT)) !== false;
    } else {
        // MySQL implementation
        require_once 'database_functions.php';
        return updateTaskInDatabase($taskId, $taskData);
    }
}

function deleteTask($taskId) {
    if (STORAGE_MODE === 'json') {
        $tasksFile = DATA_DIR . 'tasks.json';
        $tasks = getAllTasks();

        $tasks = array_filter($tasks, function($task) use ($taskId) {
            return $task['task_id'] !== $taskId;
        });

        $tasks = array_values($tasks); // Re-index array

        return file_put_contents($tasksFile, json_encode($tasks, JSON_PRETTY_PRINT)) !== false;
    } else {
        // MySQL implementation
        require_once 'database_functions.php';
        return deleteTaskFromDatabase($taskId);
    }
}

function getTaskById($taskId) {
    $tasks = getAllTasks();
    foreach ($tasks as $task) {
        if ($task['task_id'] === $taskId) {
            return $task;
        }
    }
    return null;
}

// Gift code management functions
function getAllGiftCodes() {
    if (STORAGE_MODE === 'json') {
        $giftCodesFile = DATA_DIR . 'gift_codes.json';
        if (file_exists($giftCodesFile)) {
            $codes = json_decode(file_get_contents($giftCodesFile), true);
            return $codes ?: [];
        }
        return [];
    } else {
        // MySQL implementation
        require_once 'database_functions.php';
        return getGiftCodesFromDatabase();
    }
}

function addGiftCode($codeData) {
    if (STORAGE_MODE === 'json') {
        $giftCodesFile = DATA_DIR . 'gift_codes.json';
        $codes = getAllGiftCodes();

        // Check if code already exists
        foreach ($codes as $code) {
            if ($code['code'] === $codeData['code']) {
                return false; // Code already exists
            }
        }

        $codeData['created_at'] = time();
        $codeData['used_count'] = 0;

        $codes[] = $codeData;

        return file_put_contents($giftCodesFile, json_encode($codes, JSON_PRETTY_PRINT)) !== false;
    } else {
        // MySQL implementation
        require_once 'database_functions.php';
        return addGiftCodeToDatabase($codeData);
    }
}

function redeemGiftCode($code, $userId) {
    if (STORAGE_MODE === 'json') {
        $giftCodesFile = DATA_DIR . 'gift_codes.json';
        $codes = getAllGiftCodes();

        foreach ($codes as &$giftCode) {
            if ($giftCode['code'] === $code) {
                // Check if code is expired
                if (isset($giftCode['expiry_date']) && $giftCode['expiry_date'] > 0 && time() > $giftCode['expiry_date']) {
                    return ['success' => false, 'message' => 'Gift code has expired.'];
                }

                // Check if user has already redeemed this code
                if (isset($giftCode['redeemed_by']) && in_array($userId, $giftCode['redeemed_by'])) {
                    return ['success' => false, 'message' => 'You have already redeemed this gift code.'];
                }

                // Check usage limit
                if (isset($giftCode['usage_limit']) && $giftCode['usage_limit'] > 0 && $giftCode['used_count'] >= $giftCode['usage_limit']) {
                    return ['success' => false, 'message' => 'Gift code usage limit reached.'];
                }

                // Initialize redeemed_by array if not exists
                if (!isset($giftCode['redeemed_by'])) {
                    $giftCode['redeemed_by'] = [];
                }

                // Redeem the code
                $giftCode['used_count']++;
                $giftCode['last_used'] = time();
                $giftCode['redeemed_by'][] = $userId;

                // Add to user balance
                updateUserBalance($userId, $giftCode['amount'], 'add');

                // Save updated codes
                file_put_contents($giftCodesFile, json_encode($codes, JSON_PRETTY_PRINT));

                return ['success' => true, 'amount' => $giftCode['amount']];
            }
        }

        return ['success' => false, 'message' => 'Invalid gift code.'];
    } else {
        // MySQL implementation
        require_once 'database_functions.php';
        return redeemGiftCodeFromDatabase($code, $userId);
    }
}

// Task submission management functions
function addTaskSubmission($submissionData) {
    if (STORAGE_MODE === 'json') {
        $submissionsFile = DATA_DIR . 'task_submissions.json';
        $submissions = getAllTaskSubmissions();

        $submissionData['submission_id'] = uniqid('sub_', true);
        $submissionData['submitted_at'] = time();
        $submissionData['status'] = 'pending';

        $submissions[] = $submissionData;

        return file_put_contents($submissionsFile, json_encode($submissions, JSON_PRETTY_PRINT)) !== false ? $submissionData['submission_id'] : false;
    } else {
        // MySQL implementation
        require_once 'database_functions.php';
        return addTaskSubmissionToDatabase($submissionData);
    }
}

function getAllTaskSubmissions() {
    if (STORAGE_MODE === 'json') {
        $submissionsFile = DATA_DIR . 'task_submissions.json';
        if (file_exists($submissionsFile)) {
            $submissions = json_decode(file_get_contents($submissionsFile), true);
            return $submissions ?: [];
        }
        return [];
    } else {
        // MySQL implementation
        require_once 'database_functions.php';
        return getTaskSubmissionsFromDatabase();
    }
}

function updateTaskSubmissionStatus($submissionId, $status, $adminNote = '') {
    if (STORAGE_MODE === 'json') {
        $submissionsFile = DATA_DIR . 'task_submissions.json';
        $submissions = getAllTaskSubmissions();

        foreach ($submissions as &$submission) {
            if ($submission['submission_id'] === $submissionId) {
                $submission['status'] = $status;
                $submission['admin_note'] = $adminNote;
                $submission['reviewed_at'] = time();

                // If approved, add reward to user balance
                if ($status === 'approved') {
                    $task = getTaskById($submission['task_id']);
                    if ($task) {
                        updateUserBalance($submission['user_id'], $task['reward_amount'], 'add');
                    }
                }

                break;
            }
        }

        return file_put_contents($submissionsFile, json_encode($submissions, JSON_PRETTY_PRINT)) !== false;
    } else {
        // MySQL implementation
        require_once 'database_functions.php';
        return updateTaskSubmissionStatusInDatabase($submissionId, $status, $adminNote);
    }
}

function getPendingTaskSubmissions() {
    $submissions = getAllTaskSubmissions();
    return array_filter($submissions, function($submission) {
        return $submission['status'] === 'pending';
    });
}

function getUserTaskSubmissions($userId) {
    $submissions = getAllTaskSubmissions();
    return array_filter($submissions, function($submission) use ($userId) {
        return $submission['user_id'] == $userId;
    });
}

// Enhanced broadcast system functions
function broadcastMessage($message, $adminId) {
    $allUsers = getAllUsers();
    $results = [
        'total_users' => count($allUsers),
        'success_count' => 0,
        'failed_count' => 0,
        'blocked_count' => 0,
        'errors' => []
    ];

    // Detect message type
    $messageType = detectMessageType($message);

    // Log broadcast activity
    logBroadcastActivity($adminId, $messageType, count($allUsers));

    foreach ($allUsers as $targetUserId) {
        try {
            $success = false;

            switch ($messageType) {
                case 'text':
                    $success = sendMessage($targetUserId, $message['text'], null, 'HTML');
                    break;

                case 'photo':
                    $caption = isset($message['caption']) ? $message['caption'] : '';
                    $success = sendPhoto($targetUserId, $message['photo'][0]['file_id'], $caption, null, 'HTML');
                    break;

                case 'video':
                    $caption = isset($message['caption']) ? $message['caption'] : '';
                    $success = sendVideo($targetUserId, $message['video']['file_id'], $caption, null, 'HTML');
                    break;

                case 'document':
                    $caption = isset($message['caption']) ? $message['caption'] : '';
                    $success = sendDocument($targetUserId, $message['document']['file_id'], $caption, null, 'HTML');
                    break;

                case 'audio':
                    $caption = isset($message['caption']) ? $message['caption'] : '';
                    $success = sendAudio($targetUserId, $message['audio']['file_id'], $caption, null, 'HTML');
                    break;

                case 'voice':
                    $success = sendVoice($targetUserId, $message['voice']['file_id']);
                    break;

                case 'video_note':
                    $success = sendVideoNote($targetUserId, $message['video_note']['file_id']);
                    break;

                default:
                    $results['errors'][] = "Unsupported message type: {$messageType}";
                    $results['failed_count']++;
                    continue 2;
            }

            if ($success) {
                $results['success_count']++;
            } else {
                $results['failed_count']++;
                $results['errors'][] = "Failed to send to user {$targetUserId}";
            }

            // Rate limiting - small delay between messages
            usleep(50000); // 50ms delay

        } catch (Exception $e) {
            $errorMessage = $e->getMessage();

            // Check if user blocked the bot
            if (strpos($errorMessage, 'blocked') !== false || strpos($errorMessage, 'user is deactivated') !== false) {
                $results['blocked_count']++;
            } else {
                $results['failed_count']++;
                $results['errors'][] = "User {$targetUserId}: {$errorMessage}";
            }
        }
    }

    return $results;
}

function detectMessageType($message) {
    if (isset($message['photo'])) {
        return 'photo';
    } elseif (isset($message['video'])) {
        return 'video';
    } elseif (isset($message['document'])) {
        return 'document';
    } elseif (isset($message['audio'])) {
        return 'audio';
    } elseif (isset($message['voice'])) {
        return 'voice';
    } elseif (isset($message['video_note'])) {
        return 'video_note';
    } elseif (isset($message['text'])) {
        return 'text';
    } else {
        return 'unknown';
    }
}

function logBroadcastActivity($adminId, $messageType, $userCount) {
    $logEntry = [
        'admin_id' => $adminId,
        'message_type' => $messageType,
        'user_count' => $userCount,
        'timestamp' => time(),
        'date' => date('Y-m-d H:i:s')
    ];

    if (STORAGE_MODE === 'json') {
        $logFile = DATA_DIR . 'broadcast_logs.json';
        $logs = [];

        if (file_exists($logFile)) {
            $logs = json_decode(file_get_contents($logFile), true) ?: [];
        }

        $logs[] = $logEntry;

        // Keep only last 100 entries
        if (count($logs) > 100) {
            $logs = array_slice($logs, -100);
        }

        file_put_contents($logFile, json_encode($logs, JSON_PRETTY_PRINT));
    }
    // For MySQL, could implement database logging here
}

// Level Rewards system functions
function getLevelRewardsConfig($adminId = null) {
    // Use global admin ID (0) for level rewards configuration to ensure consistency across all admins
    $globalAdminId = 0;

    // Always fetch fresh data from storage to ensure real-time sync
    if (STORAGE_MODE === 'json') {
        // Force reload from file to get latest changes
        $adminFile = ADMIN_FILE;
        if (file_exists($adminFile)) {
            $adminData = json_decode(file_get_contents($adminFile), true);
            if (isset($adminData[$globalAdminId]['level_rewards_config'])) {
                $config = $adminData[$globalAdminId]['level_rewards_config'];

                // Validate configuration structure
                if (isset($config['referral_requirements']) && isset($config['bonus_amounts']) &&
                    is_array($config['referral_requirements']) && is_array($config['bonus_amounts']) &&
                    count($config['referral_requirements']) === 6 && count($config['bonus_amounts']) === 6) {
                    return $config;
                }
            }
        }
    } else {
        // For MySQL, fetch directly from database using global admin ID
        $adminSettings = getAdminSettings($globalAdminId);
        if (isset($adminSettings['level_rewards_config'])) {
            $config = $adminSettings['level_rewards_config'];

            // Validate configuration structure
            if (isset($config['referral_requirements']) && isset($config['bonus_amounts']) &&
                is_array($config['referral_requirements']) && is_array($config['bonus_amounts']) &&
                count($config['referral_requirements']) === 6 && count($config['bonus_amounts']) === 6) {
                return $config;
            }
        }
    }

    // Return default configuration if none found or invalid
    return [
        'referral_requirements' => [1, 5, 10, 15, 20, 25],
        'bonus_amounts' => [2, 10, 15, 20, 25, 30]
    ];
}

function isLevelRewardsEnabled($adminId = null) {
    // Use global admin ID (0) for level rewards configuration to ensure consistency across all admins
    $globalAdminId = 0;

    // Always fetch fresh data from storage to ensure real-time sync
    if (STORAGE_MODE === 'json') {
        // Force reload from file to get latest changes
        $adminFile = ADMIN_FILE;
        if (file_exists($adminFile)) {
            $adminData = json_decode(file_get_contents($adminFile), true);
            if (isset($adminData[$globalAdminId]['level_rewards_enabled'])) {
                return (bool)$adminData[$globalAdminId]['level_rewards_enabled'];
            }
        }
    } else {
        // For MySQL, fetch directly from database using global admin ID
        $adminSettings = getAdminSettings($globalAdminId);
        if (isset($adminSettings['level_rewards_enabled'])) {
            return (bool)$adminSettings['level_rewards_enabled'];
        }
    }

    // Default to enabled if not found
    return true;
}

function updateLevelRewardsConfig($referralRequirements, $bonusAmounts, $adminId = null) {
    // Use global admin ID (0) for level rewards configuration to ensure consistency across all admins
    $globalAdminId = 0;
    // Validate input arrays
    if (!is_array($referralRequirements) || !is_array($bonusAmounts) ||
        count($referralRequirements) !== 6 || count($bonusAmounts) !== 6) {
        return false;
    }

    // Ensure all values are numeric and positive
    foreach ($referralRequirements as $req) {
        if (!is_numeric($req) || $req < 0) {
            return false;
        }
    }

    foreach ($bonusAmounts as $amount) {
        if (!is_numeric($amount) || $amount < 0) {
            return false;
        }
    }

    $config = [
        'referral_requirements' => array_map('intval', $referralRequirements),
        'bonus_amounts' => array_map('floatval', $bonusAmounts)
    ];

    // Update configuration using global admin ID and force immediate save
    $result = updateAdminSetting('level_rewards_config', $config, $globalAdminId);

    // Force immediate cache clearing for real-time sync
    if ($result) {
        clearLevelRewardsCache();

        // Additional verification for JSON storage
        if (STORAGE_MODE === 'json') {
            // Verify the update was successful by reading back
            $verifyConfig = getLevelRewardsConfig();
            if ($verifyConfig['referral_requirements'] !== $config['referral_requirements'] ||
                $verifyConfig['bonus_amounts'] !== $config['bonus_amounts']) {
                // If verification fails, try to update again
                $result = updateAdminSetting('level_rewards_config', $config, $globalAdminId);
                if ($result) {
                    clearLevelRewardsCache();
                }
            }
        }
    }

    return $result;
}

function toggleLevelRewards($enabled, $adminId = null) {
    // Use global admin ID (0) for level rewards configuration to ensure consistency across all admins
    $globalAdminId = 0;

    $result = updateAdminSetting('level_rewards_enabled', $enabled, $globalAdminId);

    // Force file system sync for immediate availability
    if (STORAGE_MODE === 'json' && $result) {
        clearstatcache();
    }

    return $result;
}

// Clear level rewards cache for immediate updates
function clearLevelRewardsCache() {
    if (STORAGE_MODE === 'json') {
        // Clear file system cache
        clearstatcache();

        // Force garbage collection to clear any PHP internal caches
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }
    }

    // Clear any opcache if enabled
    if (function_exists('opcache_reset')) {
        opcache_reset();
    }
}

function getUserReferralCount($userId) {
    $user = getUser($userId);
    if (!$user) return 0;

    // Count referrals from promotion report
    return count($user['promotion_report'] ?? []);
}

function getUserClaimedLevels($userId) {
    $user = getUser($userId);
    if (!$user) return [];

    return $user['claimed_levels'] ?? [];
}

function getUserEligibleLevels($userId) {
    $referralCount = getUserReferralCount($userId);
    $claimedLevels = getUserClaimedLevels($userId);
    $config = getLevelRewardsConfig();

    $eligibleLevels = [];

    for ($level = 1; $level <= 6; $level++) {
        $requiredReferrals = $config['referral_requirements'][$level - 1];

        if ($referralCount >= $requiredReferrals && !in_array($level, $claimedLevels)) {
            $eligibleLevels[] = $level;
        }
    }

    return $eligibleLevels;
}

function claimLevelBonus($userId, $level) {
    $config = getLevelRewardsConfig();
    $eligibleLevels = getUserEligibleLevels($userId);

    if (!in_array($level, $eligibleLevels)) {
        return ['success' => false, 'message' => 'You are not eligible for this level bonus.'];
    }

    $bonusAmount = $config['bonus_amounts'][$level - 1];

    // Add bonus to user balance
    if (updateUserBalance($userId, $bonusAmount, 'add')) {
        // Mark level as claimed
        $user = getUser($userId);
        $claimedLevels = $user['claimed_levels'] ?? [];
        $claimedLevels[] = $level;

        // Update user's claimed levels
        if (updateUserClaimedLevels($userId, $claimedLevels)) {
            return [
                'success' => true,
                'amount' => $bonusAmount,
                'level' => $level
            ];
        }
    }

    return ['success' => false, 'message' => 'Error processing level bonus claim.'];
}

function updateUserClaimedLevels($userId, $claimedLevels) {
    if (STORAGE_MODE === 'json') {
        $users = readJsonFile(USERS_FILE);

        if (!isset($users[$userId])) {
            return false;
        }

        $users[$userId]['claimed_levels'] = $claimedLevels;

        return writeJsonFile(USERS_FILE, $users);
    } else {
        // MySQL implementation would go here
        require_once 'database_functions.php';
        return updateUserClaimedLevelsInDatabase($userId, $claimedLevels);
    }
}

function getUserCurrentLevel($userId) {
    $referralCount = getUserReferralCount($userId);
    $config = getLevelRewardsConfig();

    $currentLevel = 0;

    for ($level = 1; $level <= 6; $level++) {
        $requiredReferrals = $config['referral_requirements'][$level - 1];

        if ($referralCount >= $requiredReferrals) {
            $currentLevel = $level;
        } else {
            break;
        }
    }

    return $currentLevel;
}

function getNextLevelInfo($userId) {
    $currentLevel = getUserCurrentLevel($userId);
    $referralCount = getUserReferralCount($userId);
    $config = getLevelRewardsConfig();

    if ($currentLevel >= 6) {
        return null; // Max level reached
    }

    $nextLevel = $currentLevel + 1;
    $requiredReferrals = $config['referral_requirements'][$nextLevel - 1];
    $remainingReferrals = $requiredReferrals - $referralCount;

    return [
        'level' => $nextLevel,
        'required_referrals' => $requiredReferrals,
        'remaining_referrals' => max(0, $remainingReferrals),
        'bonus_amount' => $config['bonus_amounts'][$nextLevel - 1]
    ];
}

// Rate limiting function
function checkRateLimit($userId) {
    if (!isRateLimitEnabled()) {
        return true;
    }

    if (!defined('DATA_DIR')) {
        return true; // Skip rate limiting if DATA_DIR not defined
    }

    $rateLimitFile = DATA_DIR . 'rate_limits.json';
    $rateLimits = [];

    if (file_exists($rateLimitFile)) {
        $rateLimits = json_decode(file_get_contents($rateLimitFile), true) ?: [];
    }

    $currentTime = time();
    $oneMinuteAgo = $currentTime - 60;

    // Clean old entries
    if (isset($rateLimits[$userId])) {
        $rateLimits[$userId] = array_filter($rateLimits[$userId], function($timestamp) use ($oneMinuteAgo) {
            return $timestamp > $oneMinuteAgo;
        });
    } else {
        $rateLimits[$userId] = [];
    }

    // Check if user exceeded rate limit
    if (count($rateLimits[$userId]) >= getMaxRequestsPerMinute()) {
        return false;
    }

    // Add current request
    $rateLimits[$userId][] = $currentTime;

    // Save rate limits
    file_put_contents($rateLimitFile, json_encode($rateLimits), LOCK_EX);

    return true;
}

// Navigation utility functions for handling media-to-text transitions
function smartEditMessage($chatId, $messageId, $newText, $newKeyboard = null, $parseMode = null) {
    // First try to edit the message text (works for text-only messages)
    $editResult = editMessageText($chatId, $messageId, $newText, $newKeyboard, $parseMode);

    if ($editResult) {
        return true;
    }

    // If editing fails (likely due to media-to-text transition), delete and send new
    try {
        $deleteResult = deleteMessage($chatId, $messageId);

        if ($deleteResult) {
            // Send new text message
            return sendMessage($chatId, $newText, $newKeyboard, $parseMode);
        }
    } catch (Exception $e) {
        error_log("Error in smartEditMessage: " . $e->getMessage());
    }

    // If delete fails, just send a new message (fallback)
    return sendMessage($chatId, $newText, $newKeyboard, $parseMode);
}

function smartNavigateToTextMessage($chatId, $messageId, $text, $keyboard = null, $parseMode = 'HTML') {
    // Always try to delete the current message first for clean navigation
    try {
        $deleteResult = deleteMessage($chatId, $messageId);

        if ($deleteResult) {
            // Send new text message
            return sendMessage($chatId, $text, $keyboard, $parseMode);
        } else {
            // If delete fails, try to edit (for text-only messages)
            return editMessageText($chatId, $messageId, $text, $keyboard, $parseMode);
        }
    } catch (Exception $e) {
        // Fallback: try to edit first, then send new if that fails
        $editResult = editMessageText($chatId, $messageId, $text, $keyboard, $parseMode);

        if (!$editResult) {
            return sendMessage($chatId, $text, $keyboard, $parseMode);
        }

        return $editResult;
    }
}

function smartNavigateToPhotoMessage($chatId, $messageId, $photoPath, $caption, $keyboard = null, $parseMode = 'HTML') {
    // For photo messages, always delete current and send new
    try {
        deleteMessage($chatId, $messageId);
        return sendPhoto($chatId, $photoPath, $caption, $keyboard, $parseMode);
    } catch (Exception $e) {
        error_log("Error in smartNavigateToPhotoMessage: " . $e->getMessage());
        // Fallback to text message if photo fails
        return sendMessage($chatId, $caption, $keyboard, $parseMode);
    }
}

function isMessageWithMedia($update) {
    if (!isset($update['callback_query']['message'])) {
        return false;
    }

    $message = $update['callback_query']['message'];

    return isset($message['photo']) ||
           isset($message['video']) ||
           isset($message['document']) ||
           isset($message['audio']) ||
           isset($message['voice']) ||
           isset($message['video_note']);
}
?>
