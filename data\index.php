<?php
// Simple landing page for the bot
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Telegram Referral Bot</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }
        h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .emoji {
            font-size: 4em;
            margin: 20px 0;
        }
        .description {
            font-size: 1.2em;
            line-height: 1.6;
            margin: 20px 0;
        }
        .status {
            background: rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .footer {
            margin-top: 40px;
            font-size: 0.9em;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="emoji">🤖</div>
        <h1>Telegram Referral Bot</h1>
        
        <div class="description">
            <p>Welcome to our Telegram Referral Bot!</p>
            <p>Earn money by inviting friends and completing simple tasks.</p>
        </div>
        
        <div class="status">
            <?php
            // Check if bot is properly configured
            if (file_exists('config.php')) {
                require_once 'config.php';
                try {
                    $pdo = getDB();
                    echo "✅ Bot is online and ready!";
                } catch (Exception $e) {
                    echo "⚠️ Bot is being configured...";
                }
            } else {
                echo "🔧 Bot setup in progress...";
            }
            ?>
        </div>
        
        <div class="description">
            <p><strong>How to get started:</strong></p>
            <p>1. Find our bot on Telegram</p>
            <p>2. Send /start command</p>
            <p>3. Join our channel</p>
            <p>4. Start earning money!</p>
        </div>
        
        <div class="footer">
            <p>Powered by PHP • Hosted on Hostinger</p>
        </div>
    </div>
</body>
</html>
