<?php
require_once 'config.php';
require_once 'storage_abstraction.php';

// Wrapper functions that delegate to the storage abstraction layer
// These maintain backward compatibility with existing code

// User management functions
function getUser($userId) {
    return StorageManager::getUser($userId);
}

function createUser($userId, $firstName, $lastName, $username, $referredBy = 'None') {
    return StorageManager::createUser($userId, $firstName, $lastName, $username, $referredBy);
}

function updateUser($userId, $userData) {
    return StorageManager::updateUser($userId, $userData);
}

function updateUserBalance($userId, $amount, $operation = 'add') {
    return StorageManager::updateUserBalance($userId, $amount, $operation);
}

function banUser($userId, $banned = true) {
    return StorageManager::banUser($userId, $banned);
}

function markUserReferred($userId) {
    return StorageManager::markUserReferred($userId);
}

function updateJoiningBonus($userId, $amount) {
    return StorageManager::updateJoiningBonus($userId, $amount);
}

function updateAccountInfo($userId, $field, $value) {
    return StorageManager::updateAccountInfo($userId, $field, $value);
}

function setGiftClaimed($userId, $claimed = true) {
    return StorageManager::setGiftClaimed($userId, $claimed);
}

// Admin functions
function getAdminSettings($adminId = ADMIN_ID) {
    return StorageManager::getAdminSettings($adminId);
}

function updateAdminSetting($field, $value, $adminId = ADMIN_ID) {
    return StorageManager::updateAdminSetting($field, $value, $adminId);
}

// Withdrawal functions
function createWithdrawal($userId, $amount) {
    return StorageManager::createWithdrawal($userId, $amount);
}

function updateWithdrawalStatus($userId, $status) {
    return StorageManager::updateWithdrawalStatus($userId, $status);
}

// Referral functions
function updateReferralReward($referrerId, $referredUserId, $amount) {
    return StorageManager::updateReferralReward($referrerId, $referredUserId, $amount);
}

function getPromotionReports($userId) {
    return StorageManager::getPromotionReports($userId);
}

function getWithdrawalReports($userId) {
    return StorageManager::getWithdrawalReports($userId);
}

// Utility functions
function getAllUsers() {
    return StorageManager::getAllUsers();
}

function getTotalUsers() {
    return StorageManager::getTotalUsers();
}

function updateBotInfo($username, $firstName) {
    return StorageManager::updateBotInfo($username, $firstName);
}

function getBotUsername() {
    return StorageManager::getBotUsername();
}

// Session management functions
function setUserSession($userId, $step, $data = []) {
    return StorageManager::setUserSession($userId, $step, $data);
}

function getUserSession($userId) {
    return StorageManager::getUserSession($userId);
}

function clearUserSession($userId) {
    return StorageManager::clearUserSession($userId);
}

function cleanOldSessions() {
    return StorageManager::cleanOldSessions();
}

// Custom referral link functions
function saveCustomReferralLink($userId, $customParameter) {
    return StorageManager::saveCustomReferralLink($userId, $customParameter);
}

function customParameterExists($customParameter) {
    return StorageManager::customParameterExists($customParameter);
}

function getUserIdByCustomParameter($customParameter) {
    return StorageManager::getUserIdByCustomParameter($customParameter);
}

function updateCustomReferralLink($oldParameter, $newParameter) {
    return StorageManager::updateCustomReferralLink($oldParameter, $newParameter);
}

function removeCustomReferralLink($customParameter) {
    return StorageManager::removeCustomReferralLink($customParameter);
}

function getCustomReferralLinksByUser($userId) {
    return StorageManager::getCustomReferralLinksByUser($userId);
}

function getAllCustomReferralLinks() {
    return StorageManager::getAllCustomReferralLinks();
}
?>
