<?php
require_once 'config.php';
require_once 'core_functions.php';

// Extra Rewards main menu handler
function handleExtraRewards($userId, $chatId, $messageId) {
    // Check maintenance and ban status
    if (isMaintenanceMode()) {
        sendMessage($chatId, MAINTENANCE_TEXT, null, 'HTML');
        return;
    }

    $user = getUser($userId);
    if ($user['banned']) {
        sendMessage($chatId, BANNED_TEXT, null, 'HTML');
        return;
    }

    $message = "🎁 <b>Extra Rewards</b>\n\n";
    $message .= "Earn additional income through tasks and gift codes!\n\n";
    $message .= "Choose an option below:";

    $keyboard = [
        'inline_keyboard' => [
            [
                ['text' => '📋 Task Rewards', 'callback_data' => 'taskRewards']
            ],
            [
                ['text' => '🎫 Redeem Gift Code', 'callback_data' => 'redeemGiftCode']
            ],
            [
                ['text' => '🏆 Level Rewards', 'callback_data' => 'levelRewards']
            ],
            [
                ['text' => '↩️ Back to Wallet', 'callback_data' => 'myWallet']
            ]
        ]
    ];

    // Use smart navigation to handle media-to-text transitions
    smartNavigateToTextMessage($chatId, $messageId, $message, $keyboard, 'HTML');
}

// Task Rewards handler
function handleTaskRewards($userId, $chatId, $messageId) {
    // Check maintenance and ban status
    if (isMaintenanceMode()) {
        sendMessage($chatId, MAINTENANCE_TEXT, null, 'HTML');
        return;
    }

    $user = getUser($userId);
    if ($user['banned']) {
        sendMessage($chatId, BANNED_TEXT, null, 'HTML');
        return;
    }

    $activeTasks = getActiveTasks();

    if (empty($activeTasks)) {
        $message = "📋 <b>Task Rewards</b>\n\n";
        $message .= "❌ No tasks available at the moment. Please check back later.";

        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => '↩️ Back', 'callback_data' => 'extraRewards']
                ]
            ]
        ];

        editMessageText($chatId, $messageId, $message, $keyboard, 'HTML');
        return;
    }

    $message = "📋 <b>Available Tasks</b>\n\n";
    $message .= "Select a task to view details and earn rewards:\n\n";

    $keyboard = ['inline_keyboard' => []];

    foreach ($activeTasks as $task) {
        $taskName = $task['name'];
        $rewardAmount = $task['reward_amount'];

        $keyboard['inline_keyboard'][] = [
            ['text' => "💰 {$taskName} - ₹{$rewardAmount}", 'callback_data' => "viewTask_{$task['task_id']}"]
        ];
    }

    $keyboard['inline_keyboard'][] = [
        ['text' => '↩️ Back', 'callback_data' => 'extraRewards']
    ];

    // Use smart navigation to handle media-to-text transitions
    smartNavigateToTextMessage($chatId, $messageId, $message, $keyboard, 'HTML');
}

// View specific task handler
function handleViewTask($userId, $chatId, $messageId, $taskId) {
    // Check maintenance and ban status
    if (isMaintenanceMode()) {
        sendMessage($chatId, MAINTENANCE_TEXT, null, 'HTML');
        return;
    }

    $user = getUser($userId);
    if ($user['banned']) {
        sendMessage($chatId, BANNED_TEXT, null, 'HTML');
        return;
    }

    $task = getTaskById($taskId);
    if (!$task || $task['status'] !== 'active') {
        sendMessage($chatId, "❌ Task not found or no longer available.");
        return;
    }

    // Check if user has already submitted this task
    $userSubmissions = getUserTaskSubmissions($userId);
    $hasSubmitted = false;
    foreach ($userSubmissions as $submission) {
        if ($submission['task_id'] === $taskId) {
            $hasSubmitted = true;
            break;
        }
    }

    $message = "📋 <b>{$task['name']}</b>\n\n";
    $message .= "📝 <b>Description:</b>\n{$task['description']}\n\n";
    $message .= "💰 <b>Reward:</b> ₹{$task['reward_amount']}\n\n";

    if ($hasSubmitted) {
        $message .= "✅ <b>Status:</b> Already submitted\n";
        $message .= "⏳ Please wait for admin approval.";

        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => '↩️ Back to Tasks', 'callback_data' => 'taskRewards']
                ]
            ]
        ];
    } else {
        $message .= "📸 <b>Instructions:</b>\n";
        $message .= "Complete the task and submit a screenshot as proof.";

        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => '📸 Submit Screenshot', 'callback_data' => "submitTask_{$taskId}"]
                ],
                [
                    ['text' => '↩️ Back to Tasks', 'callback_data' => 'taskRewards']
                ]
            ]
        ];
    }

    // Send media if available
    if (!empty($task['media_url'])) {
        // Use smart navigation for media message
        smartNavigateToPhotoMessage($chatId, $messageId, $task['media_url'], $message, $keyboard, 'HTML');
    } else {
        // Use smart navigation for text message
        smartNavigateToTextMessage($chatId, $messageId, $message, $keyboard, 'HTML');
    }
}

// Submit task handler
function handleSubmitTask($userId, $chatId, $taskId) {
    // Check maintenance and ban status
    if (isMaintenanceMode()) {
        sendMessage($chatId, MAINTENANCE_TEXT, null, 'HTML');
        return;
    }

    $user = getUser($userId);
    if ($user['banned']) {
        sendMessage($chatId, BANNED_TEXT, null, 'HTML');
        return;
    }

    $task = getTaskById($taskId);
    if (!$task || $task['status'] !== 'active') {
        sendMessage($chatId, "❌ Task not found or no longer available.");
        return;
    }

    $message = "📸 <b>Submit Task Proof</b>\n\n";
    $message .= "📋 <b>Task:</b> {$task['name']}\n";
    $message .= "💰 <b>Reward:</b> ₹{$task['reward_amount']}\n\n";
    $message .= "Please upload a screenshot or photo as proof of task completion.\n\n";
    $message .= "Send /cancel to cancel the submission.";

    sendMessage($chatId, $message, null, 'HTML');
    setUserSession($userId, 'submit_task_screenshot', ['task_id' => $taskId]);
}

// Handle task screenshot submission
function handleTaskScreenshotSubmission($userId, $chatId, $message, $sessionData) {
    $taskId = $sessionData['task_id'];
    $task = getTaskById($taskId);

    if (!$task) {
        sendMessage($chatId, "❌ Task not found.");
        clearUserSession($userId);
        return;
    }

    // Check if message contains photo
    if (!isset($message['photo']) && !isset($message['document'])) {
        sendMessage($chatId, "❌ Please send a photo or document as proof. Send /cancel to cancel.");
        return;
    }

    // Get file info
    $fileId = null;
    if (isset($message['photo'])) {
        $photos = $message['photo'];
        $fileId = end($photos)['file_id']; // Get highest resolution photo
    } elseif (isset($message['document'])) {
        $fileId = $message['document']['file_id'];
    }

    // Create submission
    $submissionData = [
        'user_id' => $userId,
        'task_id' => $taskId,
        'file_id' => $fileId,
        'status' => 'pending'
    ];

    $submissionId = addTaskSubmission($submissionData);

    if ($submissionId) {
        // Notify user
        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => '📋 View Tasks', 'callback_data' => 'taskRewards']
                ],
                [
                    ['text' => '💰 My Wallet', 'callback_data' => 'myWallet']
                ]
            ]
        ];

        sendMessage($chatId, "✅ <b>Task Submitted Successfully!</b>\n\n📋 <b>Task:</b> {$task['name']}\n💰 <b>Reward:</b> ₹{$task['reward_amount']}\n\n⏳ Your submission is under review. You will be notified once it's approved.", $keyboard, 'HTML');

        // Notify all admins
        $user = getUser($userId);
        $adminMessage = "📋 <b>New Task Submission</b>\n\n";
        $adminMessage .= "👤 <b>User:</b> {$user['first_name']} (ID: {$userId})\n";
        $adminMessage .= "📋 <b>Task:</b> {$task['name']}\n";
        $adminMessage .= "💰 <b>Reward:</b> ₹{$task['reward_amount']}\n";
        $adminMessage .= "🆔 <b>Submission ID:</b> {$submissionId}";

        $adminKeyboard = [
            'inline_keyboard' => [
                [
                    ['text' => '✅ Approve', 'callback_data' => "approveTask_{$submissionId}"],
                    ['text' => '❌ Reject', 'callback_data' => "rejectTask_{$submissionId}"]
                ]
            ]
        ];

        // Send to specific admin (admin user ID: 1363710641)
        $taskAdminId = 1363710641;

        try {
            // Forward the photo/document to the admin
            if (isset($message['photo']) || isset($message['document'])) {
                $forwardResult = forwardMessage($taskAdminId, $chatId, $message['message_id']);

                // Send admin message with approval buttons
                $messageResult = sendMessage($taskAdminId, $adminMessage, $adminKeyboard, 'HTML');

                // Log if sending fails
                if (!$forwardResult || !$messageResult) {
                    error_log("Failed to send task submission notification to admin {$taskAdminId}");
                }
            } else {
                // If no media, just send the admin message
                $messageResult = sendMessage($taskAdminId, $adminMessage, $adminKeyboard, 'HTML');

                if (!$messageResult) {
                    error_log("Failed to send task submission notification to admin {$taskAdminId}");
                }
            }
        } catch (Exception $e) {
            error_log("Error sending task submission to admin {$taskAdminId}: " . $e->getMessage());
        }
    } else {
        sendMessage($chatId, "❌ Error submitting task. Please try again.");
    }

    clearUserSession($userId);
}

// Redeem gift code handler
function handleRedeemGiftCode($userId, $chatId, $messageId) {
    // Check maintenance and ban status
    if (isMaintenanceMode()) {
        sendMessage($chatId, MAINTENANCE_TEXT, null, 'HTML');
        return;
    }

    $user = getUser($userId);
    if ($user['banned']) {
        sendMessage($chatId, BANNED_TEXT, null, 'HTML');
        return;
    }

    $message = "🎫 <b>Redeem Gift Code</b>\n\n";
    $message .= "Enter your gift code to redeem rewards:\n\n";
    $message .= "Send /cancel to cancel the process.";

    sendMessage($chatId, $message, null, 'HTML');
    setUserSession($userId, 'redeem_gift_code');
}

// Handle gift code redemption
function handleGiftCodeRedemption($userId, $chatId, $text) {
    $result = redeemGiftCode($text, $userId);

    if ($result['success']) {
        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => '💰 My Wallet', 'callback_data' => 'myWallet']
                ],
                [
                    ['text' => '🎁 Extra Rewards', 'callback_data' => 'extraRewards']
                ]
            ]
        ];

        sendMessage($chatId, "🎉 <b>Gift Code Redeemed Successfully!</b>\n\n💰 <b>Amount:</b> ₹{$result['amount']}\n\n✅ The amount has been added to your wallet.", $keyboard, 'HTML');
    } else {
        sendMessage($chatId, "❌ <b>Redemption Failed</b>\n\n{$result['message']}", null, 'HTML');
    }

    clearUserSession($userId);
}

// Level Rewards handler
function handleLevelRewards($userId, $chatId, $messageId) {
    try {
        // Check maintenance and ban status
        if (isMaintenanceMode()) {
            editMessageText($chatId, $messageId, MAINTENANCE_TEXT, null, 'HTML');
            return;
        }

        $user = getUser($userId);
        if (!$user) {
            editMessageText($chatId, $messageId, "❌ User not found. Please restart the bot with /start", null, 'HTML');
            return;
        }

        if ($user['banned']) {
            editMessageText($chatId, $messageId, BANNED_TEXT, null, 'HTML');
            return;
        }

        // Check if level rewards are enabled
        if (!isLevelRewardsEnabled()) {
            $message = "🏆 <b>Level Rewards</b>\n\n";
            $message .= "❌ Level rewards system is currently not available.\n\n";
            $message .= "Please check back later.";

            $keyboard = [
                'inline_keyboard' => [
                    [
                        ['text' => '↩️ Back', 'callback_data' => 'extraRewards']
                    ]
                ]
            ];

            smartNavigateToTextMessage($chatId, $messageId, $message, $keyboard, 'HTML');
            return;
        }

        // Generate level rewards message and keyboard
        $message = generateLevelRewardsMessage($userId);
        $keyboard = generateLevelRewardsKeyboard($userId);

        // Check if levelbonus.jpg exists
        $imagePath = 'levelbonus.jpg';
        if (file_exists($imagePath) && is_readable($imagePath)) {
            try {
                // Delete the original message first
                deleteMessage($chatId, $messageId);

                // Send photo with caption
                $result = sendPhoto($chatId, $imagePath, $message, $keyboard, 'HTML');

                if (!$result) {
                    // If photo sending fails, send as text message
                    sendMessage($chatId, $message, $keyboard, 'HTML');
                }
            } catch (Exception $e) {
                // If photo sending fails, send as text message
                sendMessage($chatId, $message, $keyboard, 'HTML');
            }
        } else {
            // Fallback to text message if image not found or not readable
            smartNavigateToTextMessage($chatId, $messageId, $message, $keyboard, 'HTML');
        }

    } catch (Exception $e) {
        // Error handling - send basic error message
        $errorMessage = "❌ <b>Error Loading Level Rewards</b>\n\n";
        $errorMessage .= "Please try again later or contact support.";

        $errorKeyboard = [
            'inline_keyboard' => [
                [
                    ['text' => '↩️ Back', 'callback_data' => 'extraRewards']
                ]
            ]
        ];

        smartNavigateToTextMessage($chatId, $messageId, $errorMessage, $errorKeyboard, 'HTML');
    }
}

function generateLevelRewardsMessage($userId) {
    try {
        // Always fetch the latest configuration for real-time sync
        $config = getLevelRewardsConfig();
        $referralCount = getUserReferralCount($userId);
        $currentLevel = getUserCurrentLevel($userId);
        $claimedLevels = getUserClaimedLevels($userId);
        $eligibleLevels = getUserEligibleLevels($userId);
        $nextLevelInfo = getNextLevelInfo($userId);

        $message = "🏆 <b>Level Rewards System</b>\n\n";
        $message .= "👤 <b>Your Status:</b>\n";
        $message .= "🔗 <b>Total Referrals:</b> {$referralCount}\n";
        $message .= "🏅 <b>Current Level:</b> " . ($currentLevel > 0 ? "Level {$currentLevel}" : "No Level") . "\n\n";

        $message .= "📊 <b>All Levels:</b>\n\n";

        // Validate configuration structure for real-time sync
        if (!isset($config['referral_requirements']) || !isset($config['bonus_amounts']) ||
            !is_array($config['referral_requirements']) || !is_array($config['bonus_amounts']) ||
            count($config['referral_requirements']) !== 6 || count($config['bonus_amounts']) !== 6) {

            // Force reload configuration if invalid
            clearstatcache(); // Clear file cache
            $config = getLevelRewardsConfig(); // Try again

            // If still invalid, use default configuration
            if (!isset($config['referral_requirements']) || !isset($config['bonus_amounts']) ||
                !is_array($config['referral_requirements']) || !is_array($config['bonus_amounts']) ||
                count($config['referral_requirements']) !== 6 || count($config['bonus_amounts']) !== 6) {

                $config = [
                    'referral_requirements' => [1, 5, 10, 15, 20, 25],
                    'bonus_amounts' => [2, 10, 15, 20, 25, 30]
                ];
            }
        }

        for ($level = 1; $level <= 6; $level++) {
            $requiredReferrals = intval($config['referral_requirements'][$level - 1]);
            $bonusAmount = floatval($config['bonus_amounts'][$level - 1]);

            $status = '';
            if (in_array($level, $claimedLevels)) {
                $status = '✅ Claimed';
            } elseif (in_array($level, $eligibleLevels)) {
                $status = '🎁 Ready to Claim';
            } elseif ($referralCount >= $requiredReferrals) {
                $status = '🔓 Unlocked';
            } else {
                $status = '🔒 Locked';
            }

            $message .= "<b>Level {$level}:</b> {$requiredReferrals} referrals = ₹{$bonusAmount} | {$status}\n";
        }

        if ($nextLevelInfo) {
            $message .= "\n🎯 <b>Next Level Progress:</b>\n";
            $message .= "Level {$nextLevelInfo['level']}: Need {$nextLevelInfo['remaining_referrals']} more referrals for ₹{$nextLevelInfo['bonus_amount']} bonus\n";
        } else {
            $message .= "\n🎉 <b>Congratulations! You've reached the maximum level!</b>\n";
        }

        if (!empty($eligibleLevels)) {
            $message .= "\n💰 <b>You have unclaimed level bonuses available!</b>";
        }

        // Add real-time sync indicator (can be removed in production)
        $message .= "\n\n<i>🔄 Config synced: " . date('H:i:s') . "</i>";

        return $message;

    } catch (Exception $e) {
        // Return a basic error message if something goes wrong
        error_log("Error in generateLevelRewardsMessage: " . $e->getMessage());
        return "🏆 <b>Level Rewards System</b>\n\n❌ Error loading level information. Please try again later.";
    }
}

function generateLevelRewardsKeyboard($userId) {
    try {
        $eligibleLevels = getUserEligibleLevels($userId);

        $keyboard = ['inline_keyboard' => []];

        if (!empty($eligibleLevels)) {
            // Show claim button for the lowest eligible level
            $lowestLevel = min($eligibleLevels);

            // Always fetch the latest configuration for real-time sync
            $config = getLevelRewardsConfig();

            // Validate config structure and ensure it has the required level
            if (isset($config['bonus_amounts']) && is_array($config['bonus_amounts']) &&
                isset($config['bonus_amounts'][$lowestLevel - 1])) {

                $bonusAmount = floatval($config['bonus_amounts'][$lowestLevel - 1]);

                $keyboard['inline_keyboard'][] = [
                    ['text' => "💰 Claim Level {$lowestLevel} Bonus (₹{$bonusAmount})", 'callback_data' => "claimLevel_{$lowestLevel}"]
                ];
            } else {
                // If config is invalid, force reload and try again
                clearstatcache();
                $config = getLevelRewardsConfig();

                if (isset($config['bonus_amounts']) && is_array($config['bonus_amounts']) &&
                    isset($config['bonus_amounts'][$lowestLevel - 1])) {

                    $bonusAmount = floatval($config['bonus_amounts'][$lowestLevel - 1]);

                    $keyboard['inline_keyboard'][] = [
                        ['text' => "💰 Claim Level {$lowestLevel} Bonus (₹{$bonusAmount})", 'callback_data' => "claimLevel_{$lowestLevel}"]
                    ];
                }
            }
        }

        $keyboard['inline_keyboard'][] = [
            ['text' => '↩️ Back to Extra Rewards', 'callback_data' => 'extraRewards']
        ];

        return $keyboard;

    } catch (Exception $e) {
        // Log error for debugging
        error_log("Error in generateLevelRewardsKeyboard: " . $e->getMessage());

        // Return basic keyboard if there's an error
        return [
            'inline_keyboard' => [
                [
                    ['text' => '↩️ Back to Extra Rewards', 'callback_data' => 'extraRewards']
                ]
            ]
        ];
    }
}

// Handle level bonus claim
function handleClaimLevelBonus($userId, $chatId, $level) {
    // Check maintenance and ban status
    if (isMaintenanceMode()) {
        sendMessage($chatId, MAINTENANCE_TEXT, null, 'HTML');
        return;
    }

    $user = getUser($userId);
    if ($user['banned']) {
        sendMessage($chatId, BANNED_TEXT, null, 'HTML');
        return;
    }

    // Check if level rewards are enabled
    if (!isLevelRewardsEnabled()) {
        sendMessage($chatId, "❌ Level rewards system is currently disabled.");
        return;
    }

    $result = claimLevelBonus($userId, $level);

    if ($result['success']) {
        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => '🏆 View Level Rewards', 'callback_data' => 'levelRewards']
                ],
                [
                    ['text' => '💰 My Wallet', 'callback_data' => 'myWallet']
                ]
            ]
        ];

        $message = "🎉 <b>Level Bonus Claimed Successfully!</b>\n\n";
        $message .= "🏅 <b>Level:</b> {$result['level']}\n";
        $message .= "💰 <b>Bonus Amount:</b> ₹{$result['amount']}\n\n";
        $message .= "✅ The bonus has been added to your wallet!";

        sendMessage($chatId, $message, $keyboard, 'HTML');

        // Check if user has more eligible levels with real-time config
        $eligibleLevels = getUserEligibleLevels($userId);
        if (!empty($eligibleLevels)) {
            $nextLevel = min($eligibleLevels);

            // Always fetch the latest configuration for real-time sync
            $config = getLevelRewardsConfig();

            // Validate config structure
            if (isset($config['bonus_amounts']) && is_array($config['bonus_amounts']) &&
                isset($config['bonus_amounts'][$nextLevel - 1])) {

                $nextBonus = floatval($config['bonus_amounts'][$nextLevel - 1]);

                $followUpMessage = "🎁 <b>Great news!</b> You have another level bonus ready to claim!\n\n";
                $followUpMessage .= "Level {$nextLevel} bonus: ₹{$nextBonus}";

                sendMessage($chatId, $followUpMessage, null, 'HTML');
            }
        }
    } else {
        sendMessage($chatId, "❌ <b>Claim Failed</b>\n\n{$result['message']}", null, 'HTML');
    }
}
?>
