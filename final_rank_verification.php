<?php
/**
 * Final Verification Test for /rank Command
 * Complete end-to-end testing of the rank command implementation
 */

echo "🚀 Final /rank Command Verification\n";
echo "===================================\n\n";

// Test results tracking
$testResults = [];
$totalTests = 0;
$passedTests = 0;

function runTest($testName, $testFunction) {
    global $testResults, $totalTests, $passedTests;
    
    $totalTests++;
    echo "🧪 {$testName}... ";
    
    try {
        $result = $testFunction();
        if ($result) {
            echo "✅ PASSED\n";
            $testResults[$testName] = 'PASSED';
            $passedTests++;
        } else {
            echo "❌ FAILED\n";
            $testResults[$testName] = 'FAILED';
        }
    } catch (Exception $e) {
        echo "❌ ERROR: " . $e->getMessage() . "\n";
        $testResults[$testName] = 'ERROR: ' . $e->getMessage();
    }
}

// Mock sendMessage function to capture output
$capturedMessages = [];
function sendMessage($chatId, $message, $keyboard = null, $parseMode = null) {
    global $capturedMessages;
    $capturedMessages[] = [
        'chatId' => $chatId,
        'message' => $message,
        'keyboard' => $keyboard,
        'parseMode' => $parseMode
    ];
    return true;
}

// Test 1: File inclusion and basic setup
function testFileInclusion() {
    try {
        require_once 'config.php';
        require_once 'admin_handlers.php';
        return true;
    } catch (Exception $e) {
        return false;
    }
}

// Test 2: Admin verification for all configured admins
function testAllAdminAccess() {
    global $capturedMessages;
    
    $adminIds = ADMIN_IDS;
    $testChatId = 12345;
    
    foreach ($adminIds as $adminId) {
        $capturedMessages = [];
        handleRankCommand($adminId, $testChatId);
        
        if (empty($capturedMessages)) {
            return false;
        }
        
        $message = $capturedMessages[0]['message'];
        if (strpos($message, 'Access Denied') !== false) {
            return false;
        }
    }
    
    return true;
}

// Test 3: Non-admin rejection
function testNonAdminRejection() {
    global $capturedMessages;
    
    $nonAdminIds = [999999999, 111111111, 555555555];
    $testChatId = 12345;
    
    foreach ($nonAdminIds as $nonAdminId) {
        $capturedMessages = [];
        handleRankCommand($nonAdminId, $testChatId);
        
        if (empty($capturedMessages)) {
            return false;
        }
        
        $message = $capturedMessages[0]['message'];
        if (strpos($message, 'Access Denied') === false) {
            return false;
        }
    }
    
    return true;
}

// Test 4: Data retrieval functionality
function testDataRetrieval() {
    try {
        $topUsers = getTopUsersByWithdrawals(5);
        
        if (!is_array($topUsers)) {
            return false;
        }
        
        // If we have users, check data structure
        if (!empty($topUsers)) {
            $firstUser = $topUsers[0];
            $requiredFields = ['user_id', 'first_name', 'successful_withdraw', 'total_referrals'];
            
            foreach ($requiredFields as $field) {
                if (!isset($firstUser[$field])) {
                    return false;
                }
            }
        }
        
        return true;
    } catch (Exception $e) {
        return false;
    }
}

// Test 5: Message format validation
function testMessageFormat() {
    global $capturedMessages;
    
    $adminId = ADMIN_IDS[0];
    $testChatId = 12345;
    
    $capturedMessages = [];
    handleRankCommand($adminId, $testChatId);
    
    if (empty($capturedMessages)) {
        return false;
    }
    
    $message = $capturedMessages[0]['message'];
    $parseMode = $capturedMessages[0]['parseMode'];
    
    // Check parse mode
    if ($parseMode !== 'HTML') {
        return false;
    }
    
    // Check required components
    $requiredComponents = [
        'TOP WITHDRAWAL RANKINGS',
        'SUMMARY STATISTICS',
        'Generated:'
    ];
    
    foreach ($requiredComponents as $component) {
        if (strpos($message, $component) === false) {
            return false;
        }
    }
    
    return true;
}

// Test 6: Performance test
function testPerformance() {
    $adminId = ADMIN_IDS[0];
    $testChatId = 12345;
    
    $startTime = microtime(true);
    
    // Run command 3 times
    for ($i = 0; $i < 3; $i++) {
        handleRankCommand($adminId, $testChatId);
    }
    
    $endTime = microtime(true);
    $averageTime = (($endTime - $startTime) / 3) * 1000; // milliseconds
    
    // Performance should be under 5 seconds per execution
    return $averageTime < 5000;
}

// Test 7: Edge cases
function testEdgeCases() {
    try {
        // Test different limits
        $result1 = getTopUsersByWithdrawals(1);
        $result15 = getTopUsersByWithdrawals(15);
        $result100 = getTopUsersByWithdrawals(100);
        
        // All should return arrays
        return is_array($result1) && is_array($result15) && is_array($result100);
    } catch (Exception $e) {
        return false;
    }
}

// Test 8: Storage compatibility
function testStorageCompatibility() {
    $currentMode = STORAGE_MODE;
    
    if ($currentMode === 'json') {
        if (!file_exists(USERS_FILE) || !is_readable(USERS_FILE)) {
            return false;
        }
        
        $users = readJsonFile(USERS_FILE);
        return is_array($users);
    } else if ($currentMode === 'mysql') {
        try {
            $pdo = getDB();
            $stmt = $pdo->query("SELECT COUNT(*) as total FROM users");
            return $stmt !== false;
        } catch (Exception $e) {
            return false;
        }
    }
    
    return false;
}

// Test 9: Webhook integration
function testWebhookIntegration() {
    // Check if webhook.php includes the rank command
    $webhookContent = file_get_contents('webhook.php');
    
    return strpos($webhookContent, '/rank') !== false && 
           strpos($webhookContent, 'handleRankCommand') !== false;
}

// Test 10: Function existence
function testFunctionExistence() {
    $requiredFunctions = [
        'handleRankCommand',
        'getRankEmoji',
        'getTopUsersByWithdrawals',
        'getTopUsersByWithdrawalsJson',
        'isAdmin',
        'sendMessage'
    ];
    
    foreach ($requiredFunctions as $function) {
        if (!function_exists($function)) {
            return false;
        }
    }
    
    return true;
}

// Run all tests
echo "Running comprehensive verification tests...\n\n";

runTest("File Inclusion", "testFileInclusion");
runTest("Function Existence", "testFunctionExistence");
runTest("All Admin Access", "testAllAdminAccess");
runTest("Non-Admin Rejection", "testNonAdminRejection");
runTest("Data Retrieval", "testDataRetrieval");
runTest("Message Format", "testMessageFormat");
runTest("Performance", "testPerformance");
runTest("Edge Cases", "testEdgeCases");
runTest("Storage Compatibility", "testStorageCompatibility");
runTest("Webhook Integration", "testWebhookIntegration");

// Display detailed results
echo "\n" . str_repeat("=", 50) . "\n";
echo "📊 DETAILED TEST RESULTS\n";
echo str_repeat("=", 50) . "\n";

foreach ($testResults as $testName => $result) {
    $status = ($result === 'PASSED') ? '✅' : '❌';
    echo "{$status} {$testName}: {$result}\n";
}

echo "\n📈 OVERALL RESULTS:\n";
echo "Total Tests: {$totalTests}\n";
echo "Passed: {$passedTests}\n";
echo "Failed: " . ($totalTests - $passedTests) . "\n";
echo "Success Rate: " . round(($passedTests / $totalTests) * 100, 1) . "%\n";

// Final assessment
if ($passedTests === $totalTests) {
    echo "\n🎉 VERIFICATION COMPLETE - ALL TESTS PASSED!\n";
    echo "✅ The /rank command is fully functional and ready for use.\n\n";
    
    echo "📋 IMPLEMENTATION SUMMARY:\n";
    echo "- ✅ Admin access control working for all configured admins\n";
    echo "- ✅ Non-admin users properly rejected\n";
    echo "- ✅ Data retrieval functioning correctly\n";
    echo "- ✅ Message formatting and HTML parsing working\n";
    echo "- ✅ Performance is acceptable\n";
    echo "- ✅ Edge cases handled properly\n";
    echo "- ✅ Storage system compatibility verified\n";
    echo "- ✅ Webhook integration complete\n\n";
    
    echo "🚀 READY FOR PRODUCTION USE!\n";
    echo "Admin IDs that can use /rank: " . implode(', ', ADMIN_IDS) . "\n";
    echo "Storage mode: " . STORAGE_MODE . "\n";
    echo "Command: Send '/rank' to the bot in a private chat\n";
    
} else {
    echo "\n⚠️  VERIFICATION INCOMPLETE - SOME TESTS FAILED\n";
    echo "Please review the failed tests above and fix any issues.\n";
    
    $failedTests = array_filter($testResults, function($result) {
        return $result !== 'PASSED';
    });
    
    if (!empty($failedTests)) {
        echo "\n❌ Failed Tests:\n";
        foreach ($failedTests as $testName => $result) {
            echo "- {$testName}: {$result}\n";
        }
    }
}

echo "\n📞 SUPPORT:\n";
echo "If you encounter any issues:\n";
echo "1. Check the error logs in data/debug.log\n";
echo "2. Verify admin IDs in config.php\n";
echo "3. Ensure proper file permissions\n";
echo "4. Test with a simple message first\n";
?>
