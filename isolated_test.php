<?php
/**
 * Isolated Test for /rank Command
 * Step-by-step testing to identify issues
 */

echo "🔍 Isolated /rank Command Test\n";
echo "==============================\n\n";

// Step 1: Test config.php inclusion
echo "Step 1: Testing config.php inclusion...\n";
try {
    require_once 'config.php';
    echo "✅ config.php loaded successfully\n";
    echo "- STORAGE_MODE: " . (defined('STORAGE_MODE') ? STORAGE_MODE : 'NOT DEFINED') . "\n";
    echo "- ADMIN_IDS: " . (defined('ADMIN_IDS') ? implode(', ', ADMIN_IDS) : 'NOT DEFINED') . "\n";
    echo "- USERS_FILE: " . (defined('USERS_FILE') ? USERS_FILE : 'NOT DEFINED') . "\n";
} catch (Exception $e) {
    echo "❌ Error loading config.php: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n";

// Step 2: Test core functions availability
echo "Step 2: Testing core functions availability...\n";
$coreFunctions = ['isAdmin', 'sendMessage', 'readJsonFile', 'getDB'];
foreach ($coreFunctions as $func) {
    if (function_exists($func)) {
        echo "✅ {$func}() is available\n";
    } else {
        echo "❌ {$func}() is NOT available\n";
    }
}

echo "\n";

// Step 3: Test admin_handlers.php inclusion
echo "Step 3: Testing admin_handlers.php inclusion...\n";
try {
    require_once 'admin_handlers.php';
    echo "✅ admin_handlers.php loaded successfully\n";
} catch (Exception $e) {
    echo "❌ Error loading admin_handlers.php: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n";

// Step 4: Test rank-specific functions
echo "Step 4: Testing rank-specific functions...\n";
$rankFunctions = ['handleRankCommand', 'getRankEmoji', 'getTopUsersByWithdrawals', 'getTopUsersByWithdrawalsJson'];
foreach ($rankFunctions as $func) {
    if (function_exists($func)) {
        echo "✅ {$func}() is available\n";
    } else {
        echo "❌ {$func}() is NOT available\n";
    }
}

echo "\n";

// Step 5: Test basic functionality
echo "Step 5: Testing basic functionality...\n";

// Test isAdmin function
echo "Testing isAdmin function:\n";
foreach (ADMIN_IDS as $adminId) {
    $result = isAdmin($adminId);
    echo "- isAdmin({$adminId}): " . ($result ? 'true' : 'false') . "\n";
}

$nonAdmin = 999999999;
$result = isAdmin($nonAdmin);
echo "- isAdmin({$nonAdmin}): " . ($result ? 'true' : 'false') . " (should be false)\n";

echo "\n";

// Test getRankEmoji function
echo "Testing getRankEmoji function:\n";
for ($i = 1; $i <= 5; $i++) {
    $emoji = getRankEmoji($i);
    echo "- Rank {$i}: {$emoji}\n";
}

echo "\n";

// Step 6: Test data access
echo "Step 6: Testing data access...\n";
if (STORAGE_MODE === 'json') {
    if (file_exists(USERS_FILE)) {
        echo "✅ Users file exists: " . USERS_FILE . "\n";
        
        $fileSize = filesize(USERS_FILE);
        echo "📊 File size: " . number_format($fileSize) . " bytes\n";
        
        try {
            $users = readJsonFile(USERS_FILE);
            if (is_array($users)) {
                echo "✅ JSON file parsed successfully\n";
                echo "📊 Total users: " . count($users) . "\n";
                
                // Count users with withdrawals
                $usersWithWithdrawals = 0;
                foreach ($users as $userData) {
                    if (($userData['successful_withdraw'] ?? 0) > 0) {
                        $usersWithWithdrawals++;
                    }
                }
                echo "📊 Users with withdrawals: {$usersWithWithdrawals}\n";
            } else {
                echo "❌ Failed to parse JSON file\n";
            }
        } catch (Exception $e) {
            echo "❌ Error reading JSON file: " . $e->getMessage() . "\n";
        }
    } else {
        echo "❌ Users file does not exist: " . USERS_FILE . "\n";
    }
} else {
    echo "MySQL mode detected - testing database connection...\n";
    try {
        $pdo = getDB();
        echo "✅ Database connection successful\n";
    } catch (Exception $e) {
        echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    }
}

echo "\n";

// Step 7: Test getTopUsersByWithdrawals function
echo "Step 7: Testing getTopUsersByWithdrawals function...\n";
try {
    $topUsers = getTopUsersByWithdrawals(3);
    echo "✅ getTopUsersByWithdrawals() executed successfully\n";
    echo "📊 Retrieved " . count($topUsers) . " users\n";
    
    if (!empty($topUsers)) {
        echo "📋 Top users preview:\n";
        foreach ($topUsers as $index => $user) {
            $rank = $index + 1;
            echo "  #{$rank} - {$user['first_name']} (ID: {$user['user_id']}) - ₹{$user['successful_withdraw']}\n";
        }
    }
} catch (Exception $e) {
    echo "❌ Error in getTopUsersByWithdrawals(): " . $e->getMessage() . "\n";
}

echo "\n";

// Step 8: Test mock sendMessage and handleRankCommand
echo "Step 8: Testing handleRankCommand with mock sendMessage...\n";

// Mock sendMessage function
$mockMessages = [];
function sendMessage($chatId, $message, $keyboard = null, $parseMode = null) {
    global $mockMessages;
    $mockMessages[] = [
        'chatId' => $chatId,
        'message' => $message,
        'parseMode' => $parseMode
    ];
    return true;
}

// Test with admin user
$adminId = ADMIN_IDS[0];
$testChatId = 12345;

echo "Testing with admin ID {$adminId}...\n";
try {
    $mockMessages = [];
    handleRankCommand($adminId, $testChatId);
    
    if (!empty($mockMessages)) {
        $message = $mockMessages[0]['message'];
        echo "✅ Command executed successfully\n";
        echo "📊 Message length: " . strlen($message) . " characters\n";
        echo "📋 Parse mode: " . ($mockMessages[0]['parseMode'] ?? 'none') . "\n";
        
        // Check for key components
        if (strpos($message, 'TOP WITHDRAWAL RANKINGS') !== false) {
            echo "✅ Contains ranking header\n";
        } else {
            echo "❌ Missing ranking header\n";
        }
        
        if (strpos($message, 'SUMMARY STATISTICS') !== false) {
            echo "✅ Contains summary statistics\n";
        } else {
            echo "❌ Missing summary statistics\n";
        }
        
        // Show first 200 characters of message
        echo "📋 Message preview:\n";
        echo substr($message, 0, 200) . "...\n";
        
    } else {
        echo "❌ No message was generated\n";
    }
} catch (Exception $e) {
    echo "❌ Error in handleRankCommand(): " . $e->getMessage() . "\n";
}

echo "\n";

// Test with non-admin user
echo "Testing with non-admin ID 999999999...\n";
try {
    $mockMessages = [];
    handleRankCommand(999999999, $testChatId);
    
    if (!empty($mockMessages)) {
        $message = $mockMessages[0]['message'];
        if (strpos($message, 'Access Denied') !== false) {
            echo "✅ Non-admin properly rejected\n";
        } else {
            echo "❌ Non-admin was not properly rejected\n";
            echo "Message: " . substr($message, 0, 100) . "...\n";
        }
    } else {
        echo "❌ No message was generated for non-admin\n";
    }
} catch (Exception $e) {
    echo "❌ Error testing non-admin: " . $e->getMessage() . "\n";
}

echo "\n🎉 Isolated test completed!\n";
echo "\n📋 Summary:\n";
echo "- Configuration loading: Working\n";
echo "- Function availability: Tested\n";
echo "- Data access: Tested\n";
echo "- Command execution: Tested\n";
echo "- Access control: Tested\n";
echo "\nIf all steps passed, the /rank command should be working correctly!\n";
?>
