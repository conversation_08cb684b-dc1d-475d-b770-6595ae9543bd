<?php
require_once 'config.php';
require_once 'storage_abstraction.php';

// Data migration script between JSON and MySQL storage modes
// WARNING: This script should be run carefully and with backups

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Migration Tool</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .container { background: #f5f5f5; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        button.danger { background: #dc3545; }
        button.danger:hover { background: #c82333; }
        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔄 Data Migration Tool</h1>
    
    <?php
    $action = $_GET['action'] ?? '';
    $message = '';
    $messageType = '';
    
    if ($action === 'export_json') {
        try {
            if (STORAGE_MODE !== 'mysql') {
                throw new Exception("Current storage mode is not MySQL");
            }
            
            $exportData = exportFromMySQL();
            $filename = 'export_' . date('Y-m-d_H-i-s') . '.json';
            file_put_contents($filename, json_encode($exportData, JSON_PRETTY_PRINT));
            
            $message = "✅ Data exported to {$filename}";
            $messageType = 'success';
        } catch (Exception $e) {
            $message = "❌ Export failed: " . $e->getMessage();
            $messageType = 'error';
        }
    }
    
    if ($action === 'import_json') {
        try {
            if (STORAGE_MODE !== 'json') {
                throw new Exception("Current storage mode is not JSON");
            }
            
            $filename = $_POST['import_file'] ?? '';
            if (!$filename || !file_exists($filename)) {
                throw new Exception("Import file not found");
            }
            
            $importData = json_decode(file_get_contents($filename), true);
            if (!$importData) {
                throw new Exception("Invalid JSON file");
            }
            
            importToJSON($importData);
            
            $message = "✅ Data imported successfully from {$filename}";
            $messageType = 'success';
        } catch (Exception $e) {
            $message = "❌ Import failed: " . $e->getMessage();
            $messageType = 'error';
        }
    }
    
    if ($action === 'backup_current') {
        try {
            $backupData = [];
            
            if (STORAGE_MODE === 'json') {
                $backupData = [
                    'users' => readJsonFile(USERS_FILE),
                    'admin_settings' => readJsonFile(ADMIN_FILE),
                    'sessions' => readJsonFile(SESSIONS_FILE),
                    'bot_info' => readJsonFile(BOT_INFO_FILE)
                ];
            } else {
                $backupData = exportFromMySQL();
            }
            
            $filename = 'backup_' . STORAGE_MODE . '_' . date('Y-m-d_H-i-s') . '.json';
            file_put_contents($filename, json_encode($backupData, JSON_PRETTY_PRINT));
            
            $message = "✅ Backup created: {$filename}";
            $messageType = 'success';
        } catch (Exception $e) {
            $message = "❌ Backup failed: " . $e->getMessage();
            $messageType = 'error';
        }
    }
    
    if ($message) {
        echo "<div class='container {$messageType}'>{$message}</div>";
    }
    
    function exportFromMySQL() {
        $pdo = getDB();
        
        // Export users
        $stmt = $pdo->query("
            SELECT u.*, ua.name, ua.ifsc, ua.email, ua.account_number, ua.mobile_number 
            FROM users u 
            LEFT JOIN user_accounts ua ON u.user_id = ua.user_id
        ");
        $users = $stmt->fetchAll();
        
        // Export promotion reports
        $stmt = $pdo->query("SELECT * FROM promotion_reports");
        $promotions = $stmt->fetchAll();
        
        // Export withdrawal reports
        $stmt = $pdo->query("SELECT * FROM withdrawal_reports");
        $withdrawals = $stmt->fetchAll();
        
        // Export admin settings
        $stmt = $pdo->query("SELECT * FROM admin_settings");
        $adminSettings = $stmt->fetchAll();
        
        // Export bot info
        $stmt = $pdo->query("SELECT * FROM bot_info");
        $botInfo = $stmt->fetch();
        
        return [
            'users' => $users,
            'promotion_reports' => $promotions,
            'withdrawal_reports' => $withdrawals,
            'admin_settings' => $adminSettings,
            'bot_info' => $botInfo,
            'export_date' => date('Y-m-d H:i:s'),
            'storage_mode' => 'mysql'
        ];
    }
    
    function importToJSON($data) {
        // Convert MySQL data to JSON format
        $jsonUsers = [];
        
        foreach ($data['users'] as $user) {
            $userId = $user['user_id'];
            
            // Get user's promotion reports
            $userPromotions = [];
            foreach ($data['promotion_reports'] as $promo) {
                if ($promo['referrer_id'] == $userId) {
                    $userPromotions[] = [
                        'referred_user_name' => $promo['referred_user_name'],
                        'referred_user_id' => $promo['referred_user_id'],
                        'amount_got' => $promo['amount_got']
                    ];
                }
            }
            
            // Get user's withdrawal reports
            $userWithdrawals = [];
            foreach ($data['withdrawal_reports'] as $withdrawal) {
                if ($withdrawal['user_id'] == $userId) {
                    $userWithdrawals[] = [
                        'amount' => $withdrawal['amount'],
                        'date' => $withdrawal['date'],
                        'status' => $withdrawal['status']
                    ];
                }
            }
            
            $jsonUsers[$userId] = [
                'user_id' => $user['user_id'],
                'first_name' => $user['first_name'],
                'username' => $user['username'],
                'banned' => $user['banned'],
                'referred' => $user['referred'],
                'referred_by' => $user['referred_by'],
                'joining_bonus_got' => $user['joining_bonus_got'],
                'referral_link' => $user['referral_link'],
                'balance' => $user['balance'],
                'successful_withdraw' => $user['successful_withdraw'],
                'withdraw_under_review' => $user['withdraw_under_review'],
                'gift_claimed' => $user['gift_claimed'],
                'account_info' => [
                    'name' => $user['name'] ?? '',
                    'ifsc' => $user['ifsc'] ?? '',
                    'email' => $user['email'] ?? '',
                    'account_number' => $user['account_number'] ?? '',
                    'mobile_number' => $user['mobile_number'] ?? ''
                ],
                'promotion_report' => $userPromotions,
                'withdrawal_report' => $userWithdrawals
            ];
        }
        
        // Convert admin settings
        $jsonAdminSettings = [];
        foreach ($data['admin_settings'] as $admin) {
            $jsonAdminSettings[$admin['admin_id']] = [
                'main_channel' => $admin['main_channel'],
                'private_logs_channel' => $admin['private_logs_channel'],
                'maintenance_status' => $admin['maintenance_status'],
                'otp_website_api_key' => $admin['otp_website_api_key'],
                'per_refer_amount' => $admin['per_refer_amount'],
                'joining_bonus_amount' => $admin['joining_bonus_amount'],
                'gift_channel' => $admin['gift_channel'],
                'gift_amount' => $admin['gift_amount']
            ];
        }
        
        // Convert bot info
        $jsonBotInfo = [
            'username' => $data['bot_info']['username'] ?? '',
            'first_name' => $data['bot_info']['first_name'] ?? ''
        ];
        
        // Write JSON files
        writeJsonFile(USERS_FILE, $jsonUsers);
        writeJsonFile(ADMIN_FILE, $jsonAdminSettings);
        writeJsonFile(BOT_INFO_FILE, $jsonBotInfo);
        writeJsonFile(SESSIONS_FILE, []); // Empty sessions
    }
    ?>
    
    <div class="container warning">
        <h2>⚠️ Important Warning</h2>
        <p><strong>Always backup your data before migration!</strong></p>
        <p>This tool helps migrate data between storage modes but should be used carefully.</p>
    </div>
    
    <div class="step">
        <h2>📊 Current Configuration</h2>
        <p><strong>Storage Mode:</strong> <?php echo strtoupper(STORAGE_MODE); ?></p>
        <?php if (STORAGE_MODE === 'mysql'): ?>
        <p><strong>Database:</strong> <?php echo DB_NAME; ?></p>
        <?php else: ?>
        <p><strong>Data Directory:</strong> <?php echo DATA_DIR; ?></p>
        <?php endif; ?>
    </div>
    
    <div class="step">
        <h2>💾 Backup Current Data</h2>
        <p>Create a backup of your current data before making any changes.</p>
        <a href="?action=backup_current"><button>Create Backup</button></a>
    </div>
    
    <?php if (STORAGE_MODE === 'mysql'): ?>
    <div class="step">
        <h2>📤 Export from MySQL</h2>
        <p>Export data from MySQL database to JSON format.</p>
        <a href="?action=export_json"><button>Export to JSON</button></a>
    </div>
    <?php endif; ?>
    
    <?php if (STORAGE_MODE === 'json'): ?>
    <div class="step">
        <h2>📥 Import to JSON</h2>
        <p>Import data from a previously exported JSON file.</p>
        <form method="POST" action="?action=import_json">
            <input type="text" name="import_file" placeholder="export_2024-01-01_12-00-00.json" required>
            <button type="submit">Import from JSON</button>
        </form>
    </div>
    <?php endif; ?>
    
    <div class="step">
        <h2>🔄 Migration Steps</h2>
        <h3>From JSON to MySQL:</h3>
        <ol>
            <li>Backup current JSON data</li>
            <li>Setup MySQL database (import database.sql)</li>
            <li>Update config.php with database credentials</li>
            <li>Change STORAGE_MODE to 'mysql'</li>
            <li>Manually import data if needed</li>
        </ol>
        
        <h3>From MySQL to JSON:</h3>
        <ol>
            <li>Export data from MySQL</li>
            <li>Change STORAGE_MODE to 'json'</li>
            <li>Import the exported data</li>
            <li>Test the bot functionality</li>
        </ol>
    </div>
    
    <div class="container info">
        <h3>📝 Notes</h3>
        <ul>
            <li>Migration doesn't happen automatically when changing storage mode</li>
            <li>Always test thoroughly after migration</li>
            <li>Keep backups of both storage formats</li>
            <li>Sessions are not migrated (they expire anyway)</li>
        </ul>
    </div>
    
</body>
</html>
