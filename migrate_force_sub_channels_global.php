<?php
/**
 * Migration script to convert admin-specific force subscription channels to global configuration
 * This fixes the multi-admin inconsistency issue by making force subscription channels global
 */

require_once 'config.php';
require_once 'core_functions.php';
require_once 'storage_abstraction.php';

echo "=== Force Subscription Channels Global Configuration Migration ===\n\n";

// Check current storage mode
echo "Storage mode: " . STORAGE_MODE . "\n\n";

if (STORAGE_MODE === 'json') {
    migrateJsonStorage();
} else {
    migrateMysqlStorage();
}

function migrateJsonStorage() {
    $adminFile = ADMIN_FILE;
    
    if (!file_exists($adminFile)) {
        echo "❌ Admin settings file not found: {$adminFile}\n";
        echo "Creating default global configuration...\n";
        createDefaultGlobalConfig();
        return;
    }
    
    $adminData = json_decode(file_get_contents($adminFile), true);
    if ($adminData === null) {
        echo "❌ Invalid JSON in admin settings file\n";
        return;
    }
    
    echo "📊 Current admin settings found:\n";
    
    // Find all admins with force subscription channels
    $allChannels = [];
    $sourceAdmins = [];
    
    foreach ($adminData as $adminId => $settings) {
        if ($adminId == 0) continue; // Skip global admin
        
        if (isset($settings['force_sub_channels']) && !empty($settings['force_sub_channels'])) {
            echo "- Admin {$adminId}: Has " . count($settings['force_sub_channels']) . " force subscription channels\n";
            $sourceAdmins[] = $adminId;
            
            foreach ($settings['force_sub_channels'] as $channel) {
                // Check for duplicates
                $exists = false;
                foreach ($allChannels as $existingChannel) {
                    if ($existingChannel['id'] === $channel['id']) {
                        $exists = true;
                        break;
                    }
                }
                
                if (!$exists) {
                    $allChannels[] = $channel;
                    echo "  → Channel: {$channel['title']} ({$channel['id']})\n";
                }
            }
        }
    }
    
    // Check if global config already exists
    if (isset($adminData[0]['force_sub_channels'])) {
        echo "\n✅ Global force subscription channels configuration already exists\n";
        $globalChannels = $adminData[0]['force_sub_channels'];
        echo "Current global channels: " . count($globalChannels) . "\n";
        
        foreach ($globalChannels as $channel) {
            echo "  - {$channel['title']} ({$channel['id']})\n";
        }
        
        // Ask if user wants to update
        echo "\nDo you want to merge with existing admin channels? (y/n): ";
        $handle = fopen("php://stdin", "r");
        $input = trim(fgets($handle));
        fclose($handle);
        
        if (strtolower($input) !== 'y') {
            echo "Migration cancelled.\n";
            return;
        }
        
        // Merge with existing global channels
        foreach ($allChannels as $channel) {
            $exists = false;
            foreach ($globalChannels as $existingChannel) {
                if ($existingChannel['id'] === $channel['id']) {
                    $exists = true;
                    break;
                }
            }
            if (!$exists) {
                $globalChannels[] = $channel;
            }
        }
        $allChannels = $globalChannels;
    }
    
    if (empty($allChannels)) {
        echo "\n⚠️ No force subscription channels found\n";
        echo "Creating empty global configuration...\n";
    } else {
        echo "\n📋 Migrating " . count($allChannels) . " unique channels to global configuration:\n";
        foreach ($allChannels as $channel) {
            echo "  - {$channel['title']} ({$channel['id']})\n";
        }
    }
    
    // Create global admin entry if it doesn't exist
    if (!isset($adminData[0])) {
        $adminData[0] = [];
    }
    
    // Set global configuration
    $adminData[0]['force_sub_channels'] = $allChannels;
    
    // Save updated configuration
    if (file_put_contents($adminFile, json_encode($adminData, JSON_PRETTY_PRINT), LOCK_EX)) {
        echo "\n✅ Successfully migrated to global force subscription channels configuration!\n";
        echo "\n📊 New global configuration:\n";
        echo "Total channels: " . count($allChannels) . "\n";
        
        foreach ($allChannels as $index => $channel) {
            $number = $index + 1;
            echo "  {$number}. {$channel['title']} ({$channel['id']})\n";
        }
        
        echo "\n🔧 Next steps:\n";
        echo "1. All admins will now see the same force subscription channels\n";
        echo "2. Changes made by any admin will be visible to all other admins\n";
        echo "3. Test the force subscription channel management with multiple admin accounts\n";
        echo "4. Consider removing individual admin channel configurations (optional)\n";
        
        // Ask if user wants to clean up individual admin configurations
        echo "\nDo you want to remove individual admin channel configurations? (y/n): ";
        $handle = fopen("php://stdin", "r");
        $input = trim(fgets($handle));
        fclose($handle);
        
        if (strtolower($input) === 'y') {
            foreach ($sourceAdmins as $adminId) {
                unset($adminData[$adminId]['force_sub_channels']);
            }
            
            if (file_put_contents($adminFile, json_encode($adminData, JSON_PRETTY_PRINT), LOCK_EX)) {
                echo "✅ Cleaned up individual admin configurations\n";
            } else {
                echo "❌ Failed to clean up individual admin configurations\n";
            }
        }
        
    } else {
        echo "\n❌ Failed to save migrated configuration\n";
    }
}

function migrateMysqlStorage() {
    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
            DB_USER,
            DB_PASS,
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );
        
        echo "✅ Connected to MySQL database\n\n";
        
        // Check if global config exists
        $stmt = $pdo->prepare("SELECT force_sub_channels FROM admin_settings WHERE admin_id = 0");
        $stmt->execute();
        $globalConfig = $stmt->fetch();
        
        if ($globalConfig && !empty($globalConfig['force_sub_channels'])) {
            echo "✅ Global force subscription channels configuration already exists\n";
            $channels = json_decode($globalConfig['force_sub_channels'], true);
            echo "Current global channels: " . count($channels) . "\n";
            
            echo "\nDo you want to merge with existing admin channels? (y/n): ";
            $handle = fopen("php://stdin", "r");
            $input = trim(fgets($handle));
            fclose($handle);
            
            if (strtolower($input) !== 'y') {
                echo "Migration cancelled.\n";
                return;
            }
        }
        
        // Find all admin configurations
        $stmt = $pdo->prepare("SELECT admin_id, force_sub_channels FROM admin_settings WHERE admin_id != 0 AND force_sub_channels IS NOT NULL AND force_sub_channels != ''");
        $stmt->execute();
        $adminConfigs = $stmt->fetchAll();
        
        $allChannels = [];
        if ($globalConfig && !empty($globalConfig['force_sub_channels'])) {
            $allChannels = json_decode($globalConfig['force_sub_channels'], true) ?: [];
        }
        
        foreach ($adminConfigs as $admin) {
            $channels = json_decode($admin['force_sub_channels'], true);
            if ($channels && is_array($channels)) {
                echo "- Admin {$admin['admin_id']}: Has " . count($channels) . " channels\n";
                
                foreach ($channels as $channel) {
                    // Check for duplicates
                    $exists = false;
                    foreach ($allChannels as $existingChannel) {
                        if ($existingChannel['id'] === $channel['id']) {
                            $exists = true;
                            break;
                        }
                    }
                    
                    if (!$exists) {
                        $allChannels[] = $channel;
                        echo "  → Channel: {$channel['title']} ({$channel['id']})\n";
                    }
                }
            }
        }
        
        if (empty($allChannels)) {
            echo "\n⚠️ No force subscription channels found\n";
            echo "Creating empty global configuration...\n";
        } else {
            echo "\n📋 Migrating " . count($allChannels) . " unique channels to global configuration\n";
        }
        
        // Insert or update global configuration
        $channelsJson = json_encode($allChannels);
        $stmt = $pdo->prepare("
            INSERT INTO admin_settings (admin_id, force_sub_channels) 
            VALUES (0, ?)
            ON DUPLICATE KEY UPDATE 
            force_sub_channels = VALUES(force_sub_channels)
        ");
        
        if ($stmt->execute([$channelsJson])) {
            echo "\n✅ Successfully migrated to global force subscription channels configuration!\n";
            echo "\n📊 New global configuration:\n";
            echo "Total channels: " . count($allChannels) . "\n";
            
            foreach ($allChannels as $index => $channel) {
                $number = $index + 1;
                echo "  {$number}. {$channel['title']} ({$channel['id']})\n";
            }
            
            echo "\n🔧 Next steps:\n";
            echo "1. All admins will now see the same force subscription channels\n";
            echo "2. Changes made by any admin will be visible to all other admins\n";
            echo "3. Test the force subscription channel management with multiple admin accounts\n";
        } else {
            echo "\n❌ Failed to save migrated configuration\n";
        }
        
    } catch (PDOException $e) {
        echo "❌ Database error: " . $e->getMessage() . "\n";
    }
}

function createDefaultGlobalConfig() {
    $defaultChannels = [];
    
    if (StorageManager::updateAdminSetting('force_sub_channels', $defaultChannels, 0)) {
        echo "✅ Created default global force subscription channels configuration\n";
    } else {
        echo "❌ Failed to create default global configuration\n";
    }
}

echo "\n=== Migration Complete ===\n";
echo "The force subscription channels system now uses global configuration.\n";
echo "All admins will see consistent channel settings.\n\n";
?>
