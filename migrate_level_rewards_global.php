<?php
/**
 * Migration script to convert admin-specific level rewards configuration to global configuration
 * This fixes the multi-admin inconsistency issue by making level rewards configuration global
 */

require_once 'config.php';
require_once 'core_functions.php';
require_once 'storage_abstraction.php';

echo "=== Level Rewards Global Configuration Migration ===\n\n";

// Check current storage mode
echo "Storage mode: " . STORAGE_MODE . "\n\n";

if (STORAGE_MODE === 'json') {
    migrateJsonStorage();
} else {
    migrateMysqlStorage();
}

function migrateJsonStorage() {
    $adminFile = ADMIN_FILE;
    
    if (!file_exists($adminFile)) {
        echo "❌ Admin settings file not found: {$adminFile}\n";
        echo "Creating default global configuration...\n";
        createDefaultGlobalConfig();
        return;
    }
    
    $adminData = json_decode(file_get_contents($adminFile), true);
    if ($adminData === null) {
        echo "❌ Invalid JSON in admin settings file\n";
        return;
    }
    
    echo "📊 Current admin settings found:\n";
    
    // Find the first admin with level rewards configuration
    $sourceConfig = null;
    $sourceAdminId = null;
    
    foreach ($adminData as $adminId => $settings) {
        if ($adminId == 0) continue; // Skip global admin
        
        if (isset($settings['level_rewards_config'])) {
            echo "- Admin {$adminId}: Has level rewards config\n";
            if ($sourceConfig === null) {
                $sourceConfig = $settings['level_rewards_config'];
                $sourceAdminId = $adminId;
                echo "  → Using this as source configuration\n";
            }
        }
        
        if (isset($settings['level_rewards_enabled'])) {
            echo "- Admin {$adminId}: Level rewards enabled = " . ($settings['level_rewards_enabled'] ? 'true' : 'false') . "\n";
        }
    }
    
    // Check if global config already exists
    if (isset($adminData[0]['level_rewards_config'])) {
        echo "\n✅ Global level rewards configuration already exists\n";
        $globalConfig = $adminData[0]['level_rewards_config'];
        echo "Current global config:\n";
        displayConfig($globalConfig);
        
        // Ask if user wants to update
        echo "\nDo you want to update the global configuration? (y/n): ";
        $handle = fopen("php://stdin", "r");
        $input = trim(fgets($handle));
        fclose($handle);
        
        if (strtolower($input) !== 'y') {
            echo "Migration cancelled.\n";
            return;
        }
    }
    
    // Use source config or create default
    if ($sourceConfig === null) {
        echo "\n⚠️ No existing level rewards configuration found\n";
        echo "Creating default global configuration...\n";
        $sourceConfig = [
            'referral_requirements' => [1, 5, 10, 15, 20, 25],
            'bonus_amounts' => [2, 10, 15, 20, 25, 30]
        ];
    } else {
        echo "\n📋 Migrating configuration from admin {$sourceAdminId}:\n";
        displayConfig($sourceConfig);
    }
    
    // Create global admin entry if it doesn't exist
    if (!isset($adminData[0])) {
        $adminData[0] = [];
    }
    
    // Set global configuration
    $adminData[0]['level_rewards_config'] = $sourceConfig;
    $adminData[0]['level_rewards_enabled'] = true;
    
    // Save updated configuration
    if (file_put_contents($adminFile, json_encode($adminData, JSON_PRETTY_PRINT), LOCK_EX)) {
        echo "\n✅ Successfully migrated to global level rewards configuration!\n";
        echo "\n📊 New global configuration:\n";
        displayConfig($sourceConfig);
        
        echo "\n🔧 Next steps:\n";
        echo "1. All admins will now see the same level rewards configuration\n";
        echo "2. Changes made by any admin will be visible to all other admins\n";
        echo "3. Test the level rewards configuration with multiple admin accounts\n";
        echo "4. The 'Back to Admin Panel' button should now work correctly\n";
        
    } else {
        echo "\n❌ Failed to save migrated configuration\n";
    }
}

function migrateMysqlStorage() {
    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
            DB_USER,
            DB_PASS,
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );
        
        echo "✅ Connected to MySQL database\n\n";
        
        // Check if global config exists
        $stmt = $pdo->prepare("SELECT level_rewards_config, level_rewards_enabled FROM admin_settings WHERE admin_id = 0");
        $stmt->execute();
        $globalConfig = $stmt->fetch();
        
        if ($globalConfig && !empty($globalConfig['level_rewards_config'])) {
            echo "✅ Global level rewards configuration already exists\n";
            $config = json_decode($globalConfig['level_rewards_config'], true);
            displayConfig($config);
            
            echo "\nDo you want to update the global configuration? (y/n): ";
            $handle = fopen("php://stdin", "r");
            $input = trim(fgets($handle));
            fclose($handle);
            
            if (strtolower($input) !== 'y') {
                echo "Migration cancelled.\n";
                return;
            }
        }
        
        // Find source configuration from existing admins
        $stmt = $pdo->prepare("SELECT admin_id, level_rewards_config FROM admin_settings WHERE admin_id != 0 AND level_rewards_config IS NOT NULL AND level_rewards_config != ''");
        $stmt->execute();
        $adminConfigs = $stmt->fetchAll();
        
        $sourceConfig = null;
        $sourceAdminId = null;
        
        foreach ($adminConfigs as $admin) {
            $config = json_decode($admin['level_rewards_config'], true);
            if ($config && isset($config['referral_requirements']) && isset($config['bonus_amounts'])) {
                echo "- Admin {$admin['admin_id']}: Has level rewards config\n";
                if ($sourceConfig === null) {
                    $sourceConfig = $config;
                    $sourceAdminId = $admin['admin_id'];
                    echo "  → Using this as source configuration\n";
                }
            }
        }
        
        // Use source config or create default
        if ($sourceConfig === null) {
            echo "\n⚠️ No existing level rewards configuration found\n";
            echo "Creating default global configuration...\n";
            $sourceConfig = [
                'referral_requirements' => [1, 5, 10, 15, 20, 25],
                'bonus_amounts' => [2, 10, 15, 20, 25, 30]
            ];
        } else {
            echo "\n📋 Migrating configuration from admin {$sourceAdminId}:\n";
            displayConfig($sourceConfig);
        }
        
        // Insert or update global configuration
        $configJson = json_encode($sourceConfig);
        $stmt = $pdo->prepare("
            INSERT INTO admin_settings (admin_id, level_rewards_config, level_rewards_enabled) 
            VALUES (0, ?, 1)
            ON DUPLICATE KEY UPDATE 
            level_rewards_config = VALUES(level_rewards_config),
            level_rewards_enabled = VALUES(level_rewards_enabled)
        ");
        
        if ($stmt->execute([$configJson])) {
            echo "\n✅ Successfully migrated to global level rewards configuration!\n";
            echo "\n📊 New global configuration:\n";
            displayConfig($sourceConfig);
            
            echo "\n🔧 Next steps:\n";
            echo "1. All admins will now see the same level rewards configuration\n";
            echo "2. Changes made by any admin will be visible to all other admins\n";
            echo "3. Test the level rewards configuration with multiple admin accounts\n";
            echo "4. The 'Back to Admin Panel' button should now work correctly\n";
        } else {
            echo "\n❌ Failed to save migrated configuration\n";
        }
        
    } catch (PDOException $e) {
        echo "❌ Database error: " . $e->getMessage() . "\n";
    }
}

function createDefaultGlobalConfig() {
    $defaultConfig = [
        'referral_requirements' => [1, 5, 10, 15, 20, 25],
        'bonus_amounts' => [2, 10, 15, 20, 25, 30]
    ];
    
    if (updateAdminSetting('level_rewards_config', $defaultConfig, 0) && 
        updateAdminSetting('level_rewards_enabled', true, 0)) {
        echo "✅ Created default global level rewards configuration\n";
        displayConfig($defaultConfig);
    } else {
        echo "❌ Failed to create default global configuration\n";
    }
}

function displayConfig($config) {
    if (!isset($config['referral_requirements']) || !isset($config['bonus_amounts'])) {
        echo "❌ Invalid configuration format\n";
        return;
    }
    
    for ($level = 1; $level <= 6; $level++) {
        $referrals = $config['referral_requirements'][$level - 1] ?? 0;
        $bonus = $config['bonus_amounts'][$level - 1] ?? 0;
        echo "  Level {$level}: {$referrals} referrals = ₹{$bonus}\n";
    }
}

echo "\n=== Migration Complete ===\n";
echo "The level rewards system now uses global configuration.\n";
echo "All admins will see consistent level rewards settings.\n";
echo "The 'Back to Admin Panel' button has been fixed.\n\n";
?>
