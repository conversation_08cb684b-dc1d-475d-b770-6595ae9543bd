<?php
/**
 * Migration script to add range fields to existing databases
 * Run this script once to add the new range fields to your existing database
 */

require_once 'config.php';

// Only run if using MySQL storage
if (STORAGE_MODE !== 'mysql') {
    echo "This migration is only for MySQL storage mode.\n";
    echo "Current storage mode: " . STORAGE_MODE . "\n";
    exit(1);
}

try {
    // Connect to database
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );

    echo "Connected to database successfully.\n";

    // Check if the new columns already exist
    $stmt = $pdo->prepare("SHOW COLUMNS FROM admin_settings LIKE 'per_refer_amount_range'");
    $stmt->execute();
    $rangeColumnExists = $stmt->fetch();

    $stmt = $pdo->prepare("SHOW COLUMNS FROM admin_settings LIKE 'joining_bonus_amount_range'");
    $stmt->execute();
    $bonusRangeColumnExists = $stmt->fetch();

    if ($rangeColumnExists && $bonusRangeColumnExists) {
        echo "Range columns already exist. No migration needed.\n";
        exit(0);
    }

    echo "Adding new range columns to admin_settings table...\n";

    // Add the new columns
    if (!$rangeColumnExists) {
        $pdo->exec("ALTER TABLE admin_settings ADD COLUMN per_refer_amount_range VARCHAR(20) DEFAULT '20-50' AFTER joining_bonus_amount");
        echo "Added per_refer_amount_range column.\n";
    }

    if (!$bonusRangeColumnExists) {
        $pdo->exec("ALTER TABLE admin_settings ADD COLUMN joining_bonus_amount_range VARCHAR(20) DEFAULT '20-50' AFTER per_refer_amount_range");
        echo "Added joining_bonus_amount_range column.\n";
    }

    // Migrate existing data: convert single amounts to ranges
    echo "Migrating existing data...\n";

    $stmt = $pdo->prepare("SELECT admin_id, per_refer_amount, joining_bonus_amount FROM admin_settings");
    $stmt->execute();
    $adminSettings = $stmt->fetchAll();

    foreach ($adminSettings as $admin) {
        $adminId = $admin['admin_id'];
        $perReferAmount = $admin['per_refer_amount'];
        $joiningBonusAmount = $admin['joining_bonus_amount'];

        // Create ranges from existing single values
        $perReferRange = "20-" . max(20, $perReferAmount);
        $joiningBonusRange = "20-" . max(20, $joiningBonusAmount);

        // Update the ranges
        $updateStmt = $pdo->prepare("
            UPDATE admin_settings 
            SET per_refer_amount_range = ?, joining_bonus_amount_range = ? 
            WHERE admin_id = ?
        ");
        $updateStmt->execute([$perReferRange, $joiningBonusRange, $adminId]);

        echo "Migrated admin {$adminId}: refer range = {$perReferRange}, bonus range = {$joiningBonusRange}\n";
    }

    echo "Migration completed successfully!\n";
    echo "\nNext steps:\n";
    echo "1. Test the new range functionality in your admin panel\n";
    echo "2. Update your bot settings via the web admin panel at /web_admin/settings.php\n";
    echo "3. Verify that referral rewards now use the configured ranges\n";

} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
