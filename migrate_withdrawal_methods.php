<?php
/**
 * Migration script to add withdrawal method support to existing users
 * This script adds the new withdrawal_method and usdt_address fields to existing user accounts
 */

require_once 'config.php';
require_once 'core_functions.php';

echo "🔄 Starting withdrawal methods migration...\n\n";

if (STORAGE_MODE === 'json') {
    migrateJsonUsers();
} else {
    migrateMysqlUsers();
}

function migrateJsonUsers() {
    echo "📁 Migrating JSON storage users...\n";
    
    $users = readJsonFile(USERS_FILE);
    $migrated = 0;
    $skipped = 0;
    
    foreach ($users as $userId => &$user) {
        $needsUpdate = false;
        
        // Ensure account_info exists
        if (!isset($user['account_info'])) {
            $user['account_info'] = [
                'name' => '',
                'ifsc' => '',
                'email' => '',
                'account_number' => '',
                'mobile_number' => '',
                'usdt_address' => '',
                'withdrawal_method' => 'bank'
            ];
            $needsUpdate = true;
        } else {
            // Add missing fields
            if (!isset($user['account_info']['usdt_address'])) {
                $user['account_info']['usdt_address'] = '';
                $needsUpdate = true;
            }
            
            if (!isset($user['account_info']['withdrawal_method'])) {
                $user['account_info']['withdrawal_method'] = 'bank';
                $needsUpdate = true;
            }
        }

        // Add last_name field for backward compatibility
        if (!isset($user['last_name'])) {
            $user['last_name'] = '';
            $needsUpdate = true;
        }
        
        if ($needsUpdate) {
            $migrated++;
            echo "✅ Migrated user: {$userId} ({$user['first_name']})\n";
        } else {
            $skipped++;
        }
    }
    
    if ($migrated > 0) {
        if (writeJsonFile(USERS_FILE, $users)) {
            echo "\n✅ Successfully migrated {$migrated} users\n";
            echo "⏭️ Skipped {$skipped} users (already up to date)\n";
        } else {
            echo "\n❌ Error writing updated users file\n";
        }
    } else {
        echo "\n✅ All users are already up to date\n";
    }
}

function migrateMysqlUsers() {
    echo "🗄️ Migrating MySQL database users...\n";
    
    try {
        $pdo = getDB();
        
        // Check if columns already exist
        $stmt = $pdo->query("SHOW COLUMNS FROM user_accounts LIKE 'usdt_address'");
        $usdtExists = $stmt->rowCount() > 0;
        
        $stmt = $pdo->query("SHOW COLUMNS FROM user_accounts LIKE 'withdrawal_method'");
        $methodExists = $stmt->rowCount() > 0;
        
        if (!$usdtExists) {
            echo "➕ Adding usdt_address column...\n";
            $pdo->exec("ALTER TABLE user_accounts ADD COLUMN usdt_address VARCHAR(42) DEFAULT ''");
        }
        
        if (!$methodExists) {
            echo "➕ Adding withdrawal_method column...\n";
            $pdo->exec("ALTER TABLE user_accounts ADD COLUMN withdrawal_method ENUM('bank', 'usdt') DEFAULT 'bank'");
        }
        
        // Ensure all users have account records
        $stmt = $pdo->query("
            INSERT INTO user_accounts (user_id, withdrawal_method) 
            SELECT user_id, 'bank' 
            FROM users 
            WHERE user_id NOT IN (SELECT user_id FROM user_accounts)
        ");
        
        $newRecords = $stmt->rowCount();
        
        echo "✅ Migration completed successfully\n";
        if ($newRecords > 0) {
            echo "➕ Created {$newRecords} new account records\n";
        }
        
        if ($usdtExists && $methodExists) {
            echo "ℹ️ Database was already up to date\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Migration failed: " . $e->getMessage() . "\n";
    }
}

echo "\n🎉 Migration completed!\n";
echo "\n📋 Summary of changes:\n";
echo "• Added 'usdt_address' field for USDT BEP-20 wallet addresses\n";
echo "• Added 'withdrawal_method' field (bank/usdt) with 'bank' as default\n";
echo "• All existing users default to 'bank' withdrawal method\n";
echo "• Users can now switch between bank and USDT withdrawal methods\n\n";
echo "✅ Your bot now supports dual withdrawal methods!\n";
?>
