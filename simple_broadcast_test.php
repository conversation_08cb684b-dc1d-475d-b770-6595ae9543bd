<?php
require_once 'config.php';
require_once 'core_functions.php';
require_once 'storage_abstraction.php';
require_once 'user_account_handlers.php';

echo "Testing broadcast system...\n";

// Test 1: Check if functions exist
echo "1. Checking functions...\n";
if (function_exists('getAllUsers')) {
    echo "   ✅ getAllUsers exists\n";
} else {
    echo "   ❌ getAllUsers missing\n";
}

if (function_exists('broadcastMessage')) {
    echo "   ✅ broadcastMessage exists\n";
} else {
    echo "   ❌ broadcastMessage missing\n";
}

if (function_exists('handleBroadcastTextStep2')) {
    echo "   ✅ handleBroadcastTextStep2 exists\n";
} else {
    echo "   ❌ handleBroadcastTextStep2 missing\n";
}

// Test 2: Get users
echo "\n2. Testing user retrieval...\n";
try {
    $users = getAllUsers();
    echo "   ✅ Found " . count($users) . " users\n";
    
    if (count($users) > 0) {
        echo "   📝 Sample users: " . implode(', ', array_slice($users, 0, 3)) . "\n";
    }
} catch (Exception $e) {
    echo "   ❌ Error: " . $e->getMessage() . "\n";
}

// Test 3: Check storage mode
echo "\n3. Storage configuration...\n";
echo "   📁 Storage mode: " . STORAGE_MODE . "\n";

if (STORAGE_MODE === 'json') {
    echo "   📂 Users file: " . (file_exists(USERS_FILE) ? 'exists' : 'missing') . "\n";
}

echo "\nTest completed!\n";
?>
