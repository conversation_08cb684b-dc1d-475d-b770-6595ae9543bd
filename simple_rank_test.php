<?php
/**
 * Simple Test for /rank Command
 * Basic functionality verification
 */

echo "🧪 Simple /rank Command Test\n";
echo "============================\n\n";

// Test 1: Check if files can be included
echo "Test 1: File Inclusion\n";
try {
    require_once 'config.php';
    echo "✅ config.php loaded\n";
    
    require_once 'core_functions.php';
    echo "✅ core_functions.php loaded\n";
    
    require_once 'admin_handlers.php';
    echo "✅ admin_handlers.php loaded\n";
} catch (Exception $e) {
    echo "❌ Error loading files: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n";

// Test 2: Check if constants are defined
echo "Test 2: Configuration Check\n";
if (defined('ADMIN_IDS')) {
    echo "✅ ADMIN_IDS defined: " . implode(', ', ADMIN_IDS) . "\n";
} else {
    echo "❌ ADMIN_IDS not defined\n";
}

if (defined('STORAGE_MODE')) {
    echo "✅ STORAGE_MODE defined: " . STORAGE_MODE . "\n";
} else {
    echo "❌ STORAGE_MODE not defined\n";
}

if (defined('USERS_FILE')) {
    echo "✅ USERS_FILE defined: " . USERS_FILE . "\n";
} else {
    echo "❌ USERS_FILE not defined\n";
}

echo "\n";

// Test 3: Check if functions exist
echo "Test 3: Function Existence Check\n";
$requiredFunctions = [
    'isAdmin',
    'handleRankCommand',
    'getRankEmoji',
    'getTopUsersByWithdrawals',
    'getTopUsersByWithdrawalsJson',
    'readJsonFile'
];

foreach ($requiredFunctions as $function) {
    if (function_exists($function)) {
        echo "✅ {$function}() exists\n";
    } else {
        echo "❌ {$function}() missing\n";
    }
}

echo "\n";

// Test 4: Test isAdmin function
echo "Test 4: Admin Function Test\n";
$adminIds = ADMIN_IDS;
foreach ($adminIds as $adminId) {
    if (isAdmin($adminId)) {
        echo "✅ isAdmin({$adminId}) = true\n";
    } else {
        echo "❌ isAdmin({$adminId}) = false\n";
    }
}

// Test non-admin
if (!isAdmin(999999999)) {
    echo "✅ isAdmin(999999999) = false (correct)\n";
} else {
    echo "❌ isAdmin(999999999) = true (incorrect)\n";
}

echo "\n";

// Test 5: Test getRankEmoji function
echo "Test 5: Rank Emoji Test\n";
for ($i = 1; $i <= 6; $i++) {
    $emoji = getRankEmoji($i);
    echo "Rank {$i}: {$emoji}\n";
}

echo "\n";

// Test 6: Test data file access
echo "Test 6: Data File Access\n";
if (STORAGE_MODE === 'json') {
    if (file_exists(USERS_FILE)) {
        echo "✅ Users file exists\n";
        
        if (is_readable(USERS_FILE)) {
            echo "✅ Users file is readable\n";
            
            $fileSize = filesize(USERS_FILE);
            echo "📊 File size: " . number_format($fileSize) . " bytes\n";
            
            // Try to read a small sample
            try {
                $users = readJsonFile(USERS_FILE);
                if (is_array($users)) {
                    echo "✅ JSON file parsed successfully\n";
                    echo "📊 Total users: " . count($users) . "\n";
                    
                    // Count users with withdrawals
                    $usersWithWithdrawals = 0;
                    foreach ($users as $userData) {
                        if (($userData['successful_withdraw'] ?? 0) > 0) {
                            $usersWithWithdrawals++;
                        }
                    }
                    echo "📊 Users with withdrawals: {$usersWithWithdrawals}\n";
                } else {
                    echo "❌ JSON file parsing failed\n";
                }
            } catch (Exception $e) {
                echo "❌ Error reading JSON: " . $e->getMessage() . "\n";
            }
        } else {
            echo "❌ Users file is not readable\n";
        }
    } else {
        echo "❌ Users file does not exist: " . USERS_FILE . "\n";
    }
} else {
    echo "MySQL mode detected - skipping file test\n";
}

echo "\n";

// Test 7: Test getTopUsersByWithdrawals function
echo "Test 7: Data Retrieval Test\n";
try {
    $topUsers = getTopUsersByWithdrawals(5);
    echo "✅ getTopUsersByWithdrawals() executed\n";
    echo "📊 Retrieved " . count($topUsers) . " users\n";
    
    if (!empty($topUsers)) {
        echo "📋 Sample user data:\n";
        $sample = $topUsers[0];
        foreach ($sample as $key => $value) {
            $displayValue = is_array($value) ? '[array]' : $value;
            echo "  - {$key}: {$displayValue}\n";
        }
    }
} catch (Exception $e) {
    echo "❌ Error in getTopUsersByWithdrawals(): " . $e->getMessage() . "\n";
}

echo "\n";

// Test 8: Mock sendMessage and test handleRankCommand
echo "Test 8: Mock Command Test\n";

// Mock sendMessage function
$mockMessages = [];
function sendMessage($chatId, $message, $keyboard = null, $parseMode = null) {
    global $mockMessages;
    $mockMessages[] = [
        'chatId' => $chatId,
        'message' => $message,
        'parseMode' => $parseMode
    ];
    echo "📤 Mock message sent to chat {$chatId}\n";
    return true;
}

// Test with admin user
$adminId = ADMIN_IDS[0];
$testChatId = 12345;

echo "Testing handleRankCommand with admin ID {$adminId}...\n";
try {
    handleRankCommand($adminId, $testChatId);
    
    if (!empty($mockMessages)) {
        $lastMessage = end($mockMessages);
        echo "✅ Command executed successfully\n";
        echo "📊 Message length: " . strlen($lastMessage['message']) . " characters\n";
        echo "📋 Parse mode: " . ($lastMessage['parseMode'] ?? 'none') . "\n";
        
        // Check for key components
        $message = $lastMessage['message'];
        if (strpos($message, 'TOP WITHDRAWAL RANKINGS') !== false) {
            echo "✅ Contains ranking header\n";
        }
        if (strpos($message, 'SUMMARY STATISTICS') !== false) {
            echo "✅ Contains summary statistics\n";
        }
    } else {
        echo "❌ No message was sent\n";
    }
} catch (Exception $e) {
    echo "❌ Error in handleRankCommand(): " . $e->getMessage() . "\n";
}

echo "\n";

// Test with non-admin user
echo "Testing handleRankCommand with non-admin ID 999999999...\n";
$mockMessages = []; // Reset
try {
    handleRankCommand(999999999, $testChatId);
    
    if (!empty($mockMessages)) {
        $lastMessage = end($mockMessages);
        if (strpos($lastMessage['message'], 'Access Denied') !== false) {
            echo "✅ Non-admin properly rejected\n";
        } else {
            echo "❌ Non-admin was not rejected\n";
        }
    } else {
        echo "❌ No message was sent for non-admin\n";
    }
} catch (Exception $e) {
    echo "❌ Error testing non-admin: " . $e->getMessage() . "\n";
}

echo "\n🎉 Simple test completed!\n";
?>
