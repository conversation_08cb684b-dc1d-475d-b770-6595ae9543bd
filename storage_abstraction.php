<?php
require_once 'config.php';

// Initialize storage on first load
initializeStorage();

// Storage abstraction layer - routes to appropriate storage method
class StorageManager {

    // User management functions
    public static function getUser($userId) {
        if (STORAGE_MODE === 'json') {
            return self::getUserJson($userId);
        } else {
            return self::getUserMysql($userId);
        }
    }

    public static function createUser($userId, $firstName, $lastName, $username, $referredBy = 'None') {
        if (STORAGE_MODE === 'json') {
            return self::createUserJson($userId, $firstName, $lastName, $username, $referredBy);
        } else {
            return self::createUserMysql($userId, $firstName, $lastName, $username, $referredBy);
        }
    }

    public static function updateUser($userId, $userData) {
        if (STORAGE_MODE === 'json') {
            return self::updateUser<PERSON>son($userId, $userData);
        } else {
            return self::updateUserMysql($userId, $userData);
        }
    }

    public static function updateUserBalance($userId, $amount, $operation = 'add') {
        if (STORAGE_MODE === 'json') {
            return self::updateUserBalanceJson($userId, $amount, $operation);
        } else {
            return self::updateUserBalanceMysql($userId, $amount, $operation);
        }
    }

    public static function banUser($userId, $banned = true) {
        if (STORAGE_MODE === 'json') {
            return self::banUserJson($userId, $banned);
        } else {
            return self::banUserMysql($userId, $banned);
        }
    }

    public static function markUserReferred($userId) {
        if (STORAGE_MODE === 'json') {
            return self::markUserReferredJson($userId);
        } else {
            return self::markUserReferredMysql($userId);
        }
    }

    public static function updateJoiningBonus($userId, $amount) {
        if (STORAGE_MODE === 'json') {
            return self::updateJoiningBonusJson($userId, $amount);
        } else {
            return self::updateJoiningBonusMysql($userId, $amount);
        }
    }

    public static function updateAccountInfo($userId, $field, $value) {
        if (STORAGE_MODE === 'json') {
            return self::updateAccountInfoJson($userId, $field, $value);
        } else {
            return self::updateAccountInfoMysql($userId, $field, $value);
        }
    }

    public static function setGiftClaimed($userId, $claimed = true) {
        if (STORAGE_MODE === 'json') {
            return self::setGiftClaimedJson($userId, $claimed);
        } else {
            return self::setGiftClaimedMysql($userId, $claimed);
        }
    }

    // Admin functions
    public static function getAdminSettings($adminId = ADMIN_ID) {
        if (STORAGE_MODE === 'json') {
            $settings = self::getAdminSettingsJson($adminId);

            // Ensure gift broadcast storage (admin_id = 0) always exists
            if ($adminId == 0 && !isset($settings['gift_broadcast_id'])) {
                $settings['gift_broadcast_id'] = '';
                self::updateAdminSettingJson('gift_broadcast_id', '', 0);
            }

            return $settings;
        } else {
            return self::getAdminSettingsMysql($adminId);
        }
    }

    public static function updateAdminSetting($field, $value, $adminId = ADMIN_ID) {
        if (STORAGE_MODE === 'json') {
            return self::updateAdminSettingJson($field, $value, $adminId);
        } else {
            return self::updateAdminSettingMysql($field, $value, $adminId);
        }
    }

    // Withdrawal functions
    public static function createWithdrawal($userId, $amount) {
        if (STORAGE_MODE === 'json') {
            return self::createWithdrawalJson($userId, $amount);
        } else {
            return self::createWithdrawalMysql($userId, $amount);
        }
    }

    public static function updateWithdrawalStatus($userId, $status) {
        if (STORAGE_MODE === 'json') {
            return self::updateWithdrawalStatusJson($userId, $status);
        } else {
            return self::updateWithdrawalStatusMysql($userId, $status);
        }
    }

    // Referral functions
    public static function updateReferralReward($referrerId, $referredUserId, $amount) {
        if (STORAGE_MODE === 'json') {
            return self::updateReferralRewardJson($referrerId, $referredUserId, $amount);
        } else {
            return self::updateReferralRewardMysql($referrerId, $referredUserId, $amount);
        }
    }

    public static function getPromotionReports($userId) {
        if (STORAGE_MODE === 'json') {
            return self::getPromotionReportsJson($userId);
        } else {
            return self::getPromotionReportsMysql($userId);
        }
    }

    public static function getWithdrawalReports($userId) {
        if (STORAGE_MODE === 'json') {
            return self::getWithdrawalReportsJson($userId);
        } else {
            return self::getWithdrawalReportsMysql($userId);
        }
    }

    // Utility functions
    public static function getAllUsers() {
        if (STORAGE_MODE === 'json') {
            return self::getAllUsersJson();
        } else {
            return self::getAllUsersMysql();
        }
    }

    public static function getTotalUsers() {
        if (STORAGE_MODE === 'json') {
            return self::getTotalUsersJson();
        } else {
            return self::getTotalUsersMysql();
        }
    }

    public static function updateBotInfo($username, $firstName) {
        if (STORAGE_MODE === 'json') {
            return self::updateBotInfoJson($username, $firstName);
        } else {
            return self::updateBotInfoMysql($username, $firstName);
        }
    }

    public static function getBotUsername() {
        if (STORAGE_MODE === 'json') {
            return self::getBotUsernameJson();
        } else {
            return self::getBotUsernameMysql();
        }
    }

    // Session management
    public static function setUserSession($userId, $step, $data = []) {
        if (STORAGE_MODE === 'json') {
            return self::setUserSessionJson($userId, $step, $data);
        } else {
            return self::setUserSessionMysql($userId, $step, $data);
        }
    }

    public static function getUserSession($userId) {
        if (STORAGE_MODE === 'json') {
            return self::getUserSessionJson($userId);
        } else {
            return self::getUserSessionMysql($userId);
        }
    }

    public static function clearUserSession($userId) {
        if (STORAGE_MODE === 'json') {
            return self::clearUserSessionJson($userId);
        } else {
            return self::clearUserSessionMysql($userId);
        }
    }

    public static function cleanOldSessions() {
        if (STORAGE_MODE === 'json') {
            return self::cleanOldSessionsJson();
        } else {
            return self::cleanOldSessionsMysql();
        }
    }

    // Custom referral link functions
    public static function saveCustomReferralLink($userId, $customParameter) {
        if (STORAGE_MODE === 'json') {
            return self::saveCustomReferralLinkJson($userId, $customParameter);
        } else {
            return self::saveCustomReferralLinkMysql($userId, $customParameter);
        }
    }

    public static function customParameterExists($customParameter) {
        if (STORAGE_MODE === 'json') {
            return self::customParameterExistsJson($customParameter);
        } else {
            return self::customParameterExistsMysql($customParameter);
        }
    }

    public static function getUserIdByCustomParameter($customParameter) {
        if (STORAGE_MODE === 'json') {
            return self::getUserIdByCustomParameterJson($customParameter);
        } else {
            return self::getUserIdByCustomParameterMysql($customParameter);
        }
    }

    public static function updateCustomReferralLink($oldParameter, $newParameter) {
        if (STORAGE_MODE === 'json') {
            return self::updateCustomReferralLinkJson($oldParameter, $newParameter);
        } else {
            return self::updateCustomReferralLinkMysql($oldParameter, $newParameter);
        }
    }

    public static function removeCustomReferralLink($customParameter) {
        if (STORAGE_MODE === 'json') {
            return self::removeCustomReferralLinkJson($customParameter);
        } else {
            return self::removeCustomReferralLinkMysql($customParameter);
        }
    }

    public static function getCustomReferralLinksByUser($userId) {
        if (STORAGE_MODE === 'json') {
            return self::getCustomReferralLinksByUserJson($userId);
        } else {
            return self::getCustomReferralLinksByUserMysql($userId);
        }
    }

    public static function getAllCustomReferralLinks() {
        if (STORAGE_MODE === 'json') {
            return self::getAllCustomReferralLinksJson();
        } else {
            return self::getAllCustomReferralLinksMysql();
        }
    }

    // JSON Implementation Methods
    private static function getUserJson($userId) {
        $users = readJsonFile(USERS_FILE);

        if (!isset($users[$userId])) {
            return null;
        }

        $user = $users[$userId];

        // Add account info fields for compatibility
        $user['name'] = $user['account_info']['name'] ?? '';
        $user['ifsc'] = $user['account_info']['ifsc'] ?? '';
        $user['email'] = $user['account_info']['email'] ?? '';
        $user['account_number'] = $user['account_info']['account_number'] ?? '';
        $user['mobile_number'] = $user['account_info']['mobile_number'] ?? '';
        // Support both old and new field names for backward compatibility
        $user['usdt_address'] = $user['account_info']['usdt_address'] ?? '';
        $user['binance_id'] = $user['account_info']['binance_id'] ?? $user['account_info']['usdt_address'] ?? '';
        $user['withdrawal_method'] = $user['account_info']['withdrawal_method'] ?? 'bank';

        // Ensure last_name exists for backward compatibility
        $user['last_name'] = $user['last_name'] ?? '';

        return $user;
    }

    private static function createUserJson($userId, $firstName, $lastName, $username, $referredBy = 'None') {
        $users = readJsonFile(USERS_FILE);

        // Get bot info for referral link
        $botInfo = getBotInfo();
        $botUsername = $botInfo['username'] ?? '';
        $referralLink = "https://t.me/{$botUsername}?start={$userId}";

        $users[$userId] = [
            'user_id' => $userId,
            'first_name' => $firstName,
            'last_name' => $lastName,
            'username' => $username,
            'banned' => false,
            'referred' => false,
            'referred_by' => $referredBy,
            'joining_bonus_got' => 0,
            'referral_link' => $referralLink,
            'balance' => 0,
            'successful_withdraw' => 0,
            'withdraw_under_review' => 0,
            'gift_claimed' => false,
            'claimed_levels' => [], // Track which level bonuses user has claimed
            'account_info' => [
                'name' => '',
                'ifsc' => '',
                'email' => '',
                'account_number' => '',
                'mobile_number' => '',
                'usdt_address' => '', // Keep for backward compatibility
                'binance_id' => '',
                'withdrawal_method' => 'bank' // Default to bank method for backward compatibility
            ],
            'promotion_report' => [],
            'withdrawal_report' => []
        ];

        // Add to referrer's promotion report if referred
        if ($referredBy !== 'None' && isValidTelegramId($referredBy) && isset($users[$referredBy])) {
            $users[$referredBy]['promotion_report'][] = [
                'referred_user_name' => $firstName,
                'referred_user_id' => $userId,
                'amount_got' => 0
            ];
        }

        return writeJsonFile(USERS_FILE, $users);
    }

    private static function updateUserJson($userId, $userData) {
        $users = readJsonFile(USERS_FILE);

        if (!isset($users[$userId])) {
            return false;
        }

        // Update user data
        $users[$userId] = array_merge($users[$userId], $userData);

        return writeJsonFile(USERS_FILE, $users);
    }

    private static function updateUserBalanceJson($userId, $amount, $operation = 'add') {
        $users = readJsonFile(USERS_FILE);

        if (!isset($users[$userId])) {
            return false;
        }

        if ($operation === 'add') {
            $users[$userId]['balance'] += $amount;
        } else {
            if ($users[$userId]['balance'] < $amount) {
                return false; // Insufficient balance
            }
            $users[$userId]['balance'] -= $amount;
        }

        return writeJsonFile(USERS_FILE, $users);
    }

    // MySQL Implementation Methods (delegate to existing functions)
    private static function getUserMysql($userId) {
        require_once 'database_functions.php';
        return getUser($userId);
    }

    private static function createUserMysql($userId, $firstName, $lastName, $username, $referredBy = 'None') {
        require_once 'database_functions.php';
        return createUser($userId, $firstName, $lastName, $username, $referredBy);
    }

    private static function updateUserMysql($userId, $userData) {
        // For MySQL, we'll need to implement this properly
        // For now, return false as MySQL implementation is not complete
        return false;
    }

    private static function updateUserBalanceMysql($userId, $amount, $operation = 'add') {
        require_once 'database_functions.php';
        return updateUserBalance($userId, $amount, $operation);
    }

    // Additional JSON implementations
    private static function banUserJson($userId, $banned = true) {
        $users = readJsonFile(USERS_FILE);

        if (!isset($users[$userId])) {
            return false;
        }

        $users[$userId]['banned'] = $banned;
        return writeJsonFile(USERS_FILE, $users);
    }

    private static function markUserReferredJson($userId) {
        $users = readJsonFile(USERS_FILE);

        if (!isset($users[$userId])) {
            return false;
        }

        $users[$userId]['referred'] = true;
        return writeJsonFile(USERS_FILE, $users);
    }

    private static function updateJoiningBonusJson($userId, $amount) {
        $users = readJsonFile(USERS_FILE);

        if (!isset($users[$userId])) {
            return false;
        }

        $users[$userId]['joining_bonus_got'] += $amount;
        $users[$userId]['balance'] += $amount;
        return writeJsonFile(USERS_FILE, $users);
    }

    private static function updateAccountInfoJson($userId, $field, $value) {
        $users = readJsonFile(USERS_FILE);

        if (!isset($users[$userId])) {
            return false;
        }

        $allowedFields = ['name', 'ifsc', 'email', 'account_number', 'mobile_number', 'usdt_address', 'binance_id', 'withdrawal_method'];
        if (!in_array($field, $allowedFields)) {
            return false;
        }

        $users[$userId]['account_info'][$field] = $value;
        return writeJsonFile(USERS_FILE, $users);
    }

    private static function setGiftClaimedJson($userId, $claimed = true) {
        $users = readJsonFile(USERS_FILE);

        if (!isset($users[$userId])) {
            return false;
        }

        $users[$userId]['gift_claimed'] = $claimed;
        return writeJsonFile(USERS_FILE, $users);
    }

    private static function getAdminSettingsJson($adminId = ADMIN_ID) {
        error_log("[AdminSettingsDebug] getAdminSettingsJson: Attempting to read ADMIN_FILE for adminId {$adminId}. Path: " . ADMIN_FILE);
        $adminSettings = readJsonFile(ADMIN_FILE);

        if ($adminSettings === null || $adminSettings === false) {
            error_log("[AdminSettingsDebug] getAdminSettingsJson: readJsonFile returned null or false for ADMIN_FILE. Assuming empty settings.");
            $adminSettings = []; // Treat as empty if read failed
        }

        if (!isset($adminSettings[$adminId])) {
            error_log("[AdminSettingsDebug] getAdminSettingsJson: No settings found for adminId {$adminId}. Creating default set and writing to ADMIN_FILE.");
            // Create default settings
            $defaultAdminData = [
                'main_channel' => '',
                'private_logs_channel' => '',
                'maintenance_status' => 'Off',
                'otp_website_api_key' => '',
                'per_refer_amount' => 0,
                'joining_bonus_amount' => 0,
                'per_refer_amount_range' => '20-50', // Default range for referral rewards
                'joining_bonus_amount_range' => '20-50', // Default range for joining bonus
                'gift_channel' => '', // Default gift_channel is empty
                'gift_amount' => 0,
                'gift_broadcast_id' => '', // For gift broadcast tracking
                'force_sub_channels' => [], // Array of force subscription channels
                'level_rewards_enabled' => true, // Enable level rewards by default
                'level_rewards_config' => [
                    'referral_requirements' => [1, 5, 10, 15, 20, 25],
                    'bonus_amounts' => [2, 10, 15, 20, 25, 30]
                ],
                'withdrawal_enabled' => true, // Enable withdrawals by default
                'withdrawal_tax_type' => 'none', // Tax type: 'none', 'fixed', 'percentage'
                'withdrawal_tax_amount' => 0 // Tax amount (fixed amount or percentage)
            ];
            $adminSettings[$adminId] = $defaultAdminData;
            $writeResult = writeJsonFile(ADMIN_FILE, $adminSettings);
            if (!$writeResult) {
                error_log("[AdminSettingsDebug] getAdminSettingsJson: FAILED to write ADMIN_FILE while creating default settings for adminId {$adminId}.");
            } else {
                error_log("[AdminSettingsDebug] getAdminSettingsJson: Successfully wrote ADMIN_FILE with default settings for adminId {$adminId}.");
            }
        } else {
            error_log("[AdminSettingsDebug] getAdminSettingsJson: Found existing settings for adminId {$adminId}. Gift channel is: '" . ($adminSettings[$adminId]['gift_channel'] ?? 'not_set') . "'");
        }

        return $adminSettings[$adminId];
    }

    private static function updateAdminSettingJson($field, $value, $adminId = ADMIN_ID) {
        $adminSettings = readJsonFile(ADMIN_FILE);

        $allowedFields = [
            'main_channel', 'private_logs_channel', 'maintenance_status',
            'otp_website_api_key', 'per_refer_amount', 'joining_bonus_amount',
            'per_refer_amount_range', 'joining_bonus_amount_range',
            'gift_channel', 'gift_amount', 'gift_broadcast_id', 'force_sub_channels',
            'gift_channel_type', 'gift_channel_id', 'gift_invite_link', 'gift_channel_title',
            'level_rewards_enabled', 'level_rewards_config',
            'withdrawal_enabled', 'withdrawal_tax_type', 'withdrawal_tax_amount'
        ];

        if (!in_array($field, $allowedFields)) {
            return false;
        }

        if (!isset($adminSettings[$adminId])) {
            $adminSettings[$adminId] = []; // Initialize if this admin ID has no settings yet
            error_log("[AdminSettingsDebug] updateAdminSettingJson: Initialized settings for adminId {$adminId} as it was not set.");
        }

        $oldValue = $adminSettings[$adminId][$field] ?? 'not_set';
        $adminSettings[$adminId][$field] = $value;
        error_log("[AdminSettingsDebug] updateAdminSettingJson: For adminId {$adminId}, setting '{$field}' from '{$oldValue}' to '{$value}'. Attempting to write ADMIN_FILE.");
        $writeResult = writeJsonFile(ADMIN_FILE, $adminSettings);
        if (!$writeResult) {
            error_log("[AdminSettingsDebug] updateAdminSettingJson: FAILED to write ADMIN_FILE for adminId {$adminId} after updating '{$field}'.");
        } else {
            error_log("[AdminSettingsDebug] updateAdminSettingJson: Successfully wrote ADMIN_FILE for adminId {$adminId} after updating '{$field}'.");
        }
        return $writeResult;
    }

    private static function createWithdrawalJson($userId, $amount) {
        $users = readJsonFile(USERS_FILE);

        if (!isset($users[$userId])) {
            return false;
        }

        // Check balance
        if ($users[$userId]['balance'] < $amount) {
            return false;
        }

        // Deduct from balance and add to under review
        $users[$userId]['balance'] -= $amount;
        $users[$userId]['withdraw_under_review'] += $amount;

        // Add withdrawal record
        $users[$userId]['withdrawal_report'][] = [
            'amount' => $amount,
            'date' => getCurrentDate(),
            'status' => 'Under review'
        ];

        return writeJsonFile(USERS_FILE, $users);
    }

    private static function updateWithdrawalStatusJson($userId, $status) {
        $users = readJsonFile(USERS_FILE);

        if (!isset($users[$userId])) {
            return false;
        }

        // Find withdrawal under review
        $withdrawalReports = &$users[$userId]['withdrawal_report'];
        $withdrawalIndex = -1;

        for ($i = count($withdrawalReports) - 1; $i >= 0; $i--) {
            if ($withdrawalReports[$i]['status'] === 'Under review') {
                $withdrawalIndex = $i;
                break;
            }
        }

        if ($withdrawalIndex === -1) {
            return false; // No withdrawal under review
        }

        $withdrawal = $withdrawalReports[$withdrawalIndex];
        $withdrawalReports[$withdrawalIndex]['status'] = $status;

        // Update user balances
        if ($status === 'Passed') {
            $users[$userId]['withdraw_under_review'] -= $withdrawal['amount'];
            $users[$userId]['successful_withdraw'] += $withdrawal['amount'];
        } elseif ($status === 'Failed') {
            $users[$userId]['withdraw_under_review'] -= $withdrawal['amount'];
            // Restore balance on failed withdrawal
            $users[$userId]['balance'] += $withdrawal['amount'];
        }

        writeJsonFile(USERS_FILE, $users);
        return $withdrawal;
    }

    private static function updateReferralRewardJson($referrerId, $referredUserId, $amount) {
        $users = readJsonFile(USERS_FILE);

        if (!isset($users[$referrerId])) {
            error_log("[ReferralRewardDebug] Referrer {$referrerId} not found in users file");
            return false;
        }

        // Update referrer balance
        $users[$referrerId]['balance'] += $amount;
        error_log("[ReferralRewardDebug] Updated referrer {$referrerId} balance by {$amount}. New balance: {$users[$referrerId]['balance']}");

        // Update promotion report - find the specific referral entry
        $reportUpdated = false;
        if (isset($users[$referrerId]['promotion_report'])) {
            foreach ($users[$referrerId]['promotion_report'] as &$report) {
                if ($report['referred_user_id'] == $referredUserId) {
                    $report['amount_got'] += $amount;
                    $reportUpdated = true;
                    error_log("[ReferralRewardDebug] Updated promotion report for referrer {$referrerId}, referred user {$referredUserId}. New amount: {$report['amount_got']}");
                    break;
                }
            }
        }

        if (!$reportUpdated) {
            error_log("[ReferralRewardDebug] WARNING: Promotion report entry not found for referrer {$referrerId}, referred user {$referredUserId}");
            // Initialize promotion report if it doesn't exist
            if (!isset($users[$referrerId]['promotion_report'])) {
                $users[$referrerId]['promotion_report'] = [];
            }
            // Add the missing entry
            $users[$referrerId]['promotion_report'][] = [
                'referred_user_name' => $users[$referredUserId]['first_name'] ?? 'Unknown',
                'referred_user_id' => $referredUserId,
                'amount_got' => $amount
            ];
            error_log("[ReferralRewardDebug] Created new promotion report entry for referrer {$referrerId}, referred user {$referredUserId}");
        }

        $result = writeJsonFile(USERS_FILE, $users);
        error_log("[ReferralRewardDebug] File write result: " . ($result ? 'success' : 'failed'));
        return $result;
    }

    private static function getPromotionReportsJson($userId) {
        $users = readJsonFile(USERS_FILE);

        if (!isset($users[$userId])) {
            return [];
        }

        return $users[$userId]['promotion_report'] ?? [];
    }

    private static function getWithdrawalReportsJson($userId) {
        $users = readJsonFile(USERS_FILE);

        if (!isset($users[$userId])) {
            return [];
        }

        return $users[$userId]['withdrawal_report'] ?? [];
    }

    private static function getAllUsersJson() {
        error_log("[GetAllUsersDebug] Attempting to read users from: " . USERS_FILE);
        $users = readJsonFile(USERS_FILE);

        if ($users === null || $users === false) { // readJsonFile might return false on error, or null if json_decode fails
            error_log("[GetAllUsersDebug] readJsonFile returned null, false, or unreadable content for USERS_FILE. Path: " . USERS_FILE);
            return [];
        }
        if (empty($users)) {
            error_log("[GetAllUsersDebug] USERS_FILE is empty or contains no valid user entries. Path: " . USERS_FILE);
            return [];
        }
        error_log("[GetAllUsersDebug] Raw users data from USERS_FILE: " . substr(json_encode($users), 0, 1000)); // Log first 1000 chars of users data

        $userIds = [];
        $bannedCount = 0;
        $activeCount = 0;

        // Ensure we process users in a consistent order and avoid duplicates
        $processedUserIds = [];

        foreach ($users as $userId => $user) {
            // Skip if already processed (safety check)
            if (in_array($userId, $processedUserIds)) {
                error_log("[GetAllUsersDebug] Duplicate user ID detected: {$userId}, skipping");
                continue;
            }

            $processedUserIds[] = $userId;

            // Validate user data structure
            if (!is_array($user) || !isset($user['user_id'])) {
                error_log("[GetAllUsersDebug] Invalid user data for ID {$userId}, skipping");
                continue;
            }

            if (isset($user['banned']) && $user['banned']) {
                $bannedCount++;
                error_log("[GetAllUsersDebug] User {$userId} is banned, skipping from broadcast");
            } else {
                // Assuming if 'banned' is not set or is false, user is active for broadcast
                $userIds[] = (string)$userId; // Ensure user ID is string for consistency
                $activeCount++;
            }
        }

        // Remove any potential duplicates and ensure unique list
        $userIds = array_unique($userIds);
        $finalCount = count($userIds);

        error_log("[GetAllUsersDebug] Finished processing users. Active users for broadcast: {$finalCount}. Banned users skipped: {$bannedCount}.");
        error_log("[GetAllUsersDebug] Final user IDs list: " . implode(', ', array_slice($userIds, 0, 10)) . ($finalCount > 10 ? '... and ' . ($finalCount - 10) . ' more' : ''));

        return array_values($userIds); // Re-index array to ensure clean numeric indices
    }

    private static function getTotalUsersJson() {
        $users = readJsonFile(USERS_FILE);
        return count($users);
    }

    private static function updateBotInfoJson($username, $firstName) {
        $botInfo = [
            'username' => $username,
            'first_name' => $firstName
        ];

        return writeJsonFile(BOT_INFO_FILE, $botInfo);
    }

    private static function getBotUsernameJson() {
        $botInfo = readJsonFile(BOT_INFO_FILE);

        if (empty($botInfo['username'])) {
            // Fetch from Telegram API and cache
            $telegramBotInfo = getBotInfo();
            if ($telegramBotInfo) {
                self::updateBotInfoJson($telegramBotInfo['username'], $telegramBotInfo['first_name']);
                return $telegramBotInfo['username'];
            }
        }

        return $botInfo['username'] ?? '';
    }

    // Session management JSON implementations
    private static function setUserSessionJson($userId, $step, $data = []) {
        $sessions = readJsonFile(SESSIONS_FILE);

        $sessions[$userId] = [
            'step' => $step,
            'data' => $data,
            'created_at' => time()
        ];

        return writeJsonFile(SESSIONS_FILE, $sessions);
    }

    private static function getUserSessionJson($userId) {
        $sessions = readJsonFile(SESSIONS_FILE);

        if (!isset($sessions[$userId])) {
            return null;
        }

        return [
            'step' => $sessions[$userId]['step'],
            'data' => $sessions[$userId]['data'] ?? []
        ];
    }

    private static function clearUserSessionJson($userId) {
        $sessions = readJsonFile(SESSIONS_FILE);

        if (isset($sessions[$userId])) {
            unset($sessions[$userId]);
            return writeJsonFile(SESSIONS_FILE, $sessions);
        }

        return true;
    }

    private static function cleanOldSessionsJson() {
        $sessions = readJsonFile(SESSIONS_FILE);
        $oneHourAgo = time() - 3600;
        $cleaned = false;

        foreach ($sessions as $userId => $session) {
            if (isset($session['created_at']) && $session['created_at'] < $oneHourAgo) {
                unset($sessions[$userId]);
                $cleaned = true;
            }
        }

        if ($cleaned) {
            return writeJsonFile(SESSIONS_FILE, $sessions);
        }

        return true;
    }

    // MySQL implementations (delegate to existing functions)
    private static function banUserMysql($userId, $banned = true) {
        require_once 'database_functions.php';
        return banUser($userId, $banned);
    }

    private static function markUserReferredMysql($userId) {
        require_once 'database_functions.php';
        return markUserReferred($userId);
    }

    private static function updateJoiningBonusMysql($userId, $amount) {
        require_once 'database_functions.php';
        return updateJoiningBonus($userId, $amount);
    }

    private static function updateAccountInfoMysql($userId, $field, $value) {
        require_once 'database_functions.php';
        return updateAccountInfo($userId, $field, $value);
    }

    private static function setGiftClaimedMysql($userId, $claimed = true) {
        require_once 'database_functions.php';
        return setGiftClaimed($userId, $claimed);
    }

    private static function getAdminSettingsMysql($adminId = ADMIN_ID) {
        try {
            $pdo = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                DB_USER,
                DB_PASS,
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );

            $stmt = $pdo->prepare("SELECT * FROM admin_settings WHERE admin_id = ?");
            $stmt->execute([$adminId]);
            $settings = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$settings) {
                error_log("[AdminSettingsDebug] getAdminSettingsMysql: No settings found for admin {$adminId}");
                return [];
            }

            // Decode JSON fields
            if (isset($settings['force_sub_channels']) && !empty($settings['force_sub_channels'])) {
                $decoded = json_decode($settings['force_sub_channels'], true);
                $settings['force_sub_channels'] = $decoded ?: [];
                error_log("[AdminSettingsDebug] getAdminSettingsMysql: Decoded force_sub_channels for admin {$adminId}: " . count($settings['force_sub_channels']) . " channels");
            } else {
                $settings['force_sub_channels'] = [];
            }

            if (isset($settings['level_rewards_config']) && !empty($settings['level_rewards_config'])) {
                $decoded = json_decode($settings['level_rewards_config'], true);
                $settings['level_rewards_config'] = $decoded ?: [];
            } else {
                $settings['level_rewards_config'] = [];
            }

            return $settings;

        } catch (PDOException $e) {
            error_log("[AdminSettingsDebug] getAdminSettingsMysql: Database error for admin {$adminId}: " . $e->getMessage());
            return [];
        }
    }

    private static function updateAdminSettingMysql($field, $value, $adminId = ADMIN_ID) {
        $allowedFields = [
            'main_channel', 'private_logs_channel', 'maintenance_status',
            'otp_website_api_key', 'per_refer_amount', 'joining_bonus_amount',
            'per_refer_amount_range', 'joining_bonus_amount_range',
            'gift_channel', 'gift_amount', 'gift_broadcast_id', 'force_sub_channels',
            'level_rewards_enabled', 'level_rewards_config',
            'withdrawal_enabled', 'withdrawal_tax_type', 'withdrawal_tax_amount'
        ];

        if (!in_array($field, $allowedFields)) {
            error_log("[AdminSettingsDebug] updateAdminSettingMysql: Field '{$field}' not allowed");
            return false;
        }

        try {
            $pdo = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                DB_USER,
                DB_PASS,
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );

            // Special handling for JSON fields
            if ($field === 'force_sub_channels' || $field === 'level_rewards_config') {
                $value = is_array($value) ? json_encode($value) : $value;
                error_log("[AdminSettingsDebug] updateAdminSettingMysql: JSON field '{$field}' encoded to: {$value}");
            }

            // Check if admin settings exist
            $stmt = $pdo->prepare("SELECT admin_id FROM admin_settings WHERE admin_id = ?");
            $stmt->execute([$adminId]);
            $exists = $stmt->fetch();

            if ($exists) {
                // Update existing record
                $stmt = $pdo->prepare("UPDATE admin_settings SET {$field} = ? WHERE admin_id = ?");
                $result = $stmt->execute([$value, $adminId]);
                error_log("[AdminSettingsDebug] updateAdminSettingMysql: Updated admin {$adminId}, field '{$field}' = '{$value}', result: " . ($result ? 'success' : 'failed'));
            } else {
                // Insert new record
                $stmt = $pdo->prepare("INSERT INTO admin_settings (admin_id, {$field}) VALUES (?, ?)");
                $result = $stmt->execute([$adminId, $value]);
                error_log("[AdminSettingsDebug] updateAdminSettingMysql: Inserted admin {$adminId}, field '{$field}' = '{$value}', result: " . ($result ? 'success' : 'failed'));
            }

            return $result;

        } catch (PDOException $e) {
            error_log("[AdminSettingsDebug] updateAdminSettingMysql: Database error for admin {$adminId}, field '{$field}': " . $e->getMessage());
            return false;
        }
    }

    private static function createWithdrawalMysql($userId, $amount) {
        require_once 'database_functions.php';
        return createWithdrawal($userId, $amount);
    }

    private static function updateWithdrawalStatusMysql($userId, $status) {
        require_once 'database_functions.php';
        return updateWithdrawalStatus($userId, $status);
    }

    private static function updateReferralRewardMysql($referrerId, $referredUserId, $amount) {
        require_once 'database_functions.php';
        return updateReferralReward($referrerId, $referredUserId, $amount);
    }

    private static function getPromotionReportsMysql($userId) {
        require_once 'database_functions.php';
        return getPromotionReports($userId);
    }

    private static function getWithdrawalReportsMysql($userId) {
        require_once 'database_functions.php';
        return getWithdrawalReports($userId);
    }

    private static function getAllUsersMysql() {
        try {
            $db = getDB();
            if (!$db) {
                error_log("[GetAllUsersMySQL] Database connection failed");
                return [];
            }

            // Get all non-banned users for broadcast
            $stmt = $db->prepare("SELECT user_id FROM users WHERE banned = FALSE ORDER BY user_id");
            $stmt->execute();

            $userIds = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $userIds[] = (string)$row['user_id']; // Ensure string consistency
            }

            error_log("[GetAllUsersMySQL] Retrieved " . count($userIds) . " active users for broadcast");
            return $userIds;

        } catch (Exception $e) {
            error_log("[GetAllUsersMySQL] Error: " . $e->getMessage());
            return [];
        }
    }

    private static function getTotalUsersMysql() {
        require_once 'database_functions.php';
        return getTotalUsers();
    }

    private static function updateBotInfoMysql($username, $firstName) {
        require_once 'database_functions.php';
        return updateBotInfo($username, $firstName);
    }

    private static function getBotUsernameMysql() {
        require_once 'database_functions.php';
        return getBotUsername();
    }

    private static function setUserSessionMysql($userId, $step, $data = []) {
        require_once 'config.php';
        $pdo = getDB();
        $stmt = $pdo->prepare("
            INSERT INTO user_sessions (user_id, step, data, created_at)
            VALUES (?, ?, ?, NOW())
            ON DUPLICATE KEY UPDATE step = VALUES(step), data = VALUES(data), created_at = NOW()
        ");
        return $stmt->execute([$userId, $step, json_encode($data)]);
    }

    private static function getUserSessionMysql($userId) {
        require_once 'config.php';
        $pdo = getDB();
        $stmt = $pdo->prepare("SELECT step, data FROM user_sessions WHERE user_id = ?");
        $stmt->execute([$userId]);
        $result = $stmt->fetch();

        if ($result) {
            return [
                'step' => $result['step'],
                'data' => json_decode($result['data'], true) ?: []
            ];
        }

        return null;
    }

    private static function clearUserSessionMysql($userId) {
        require_once 'config.php';
        $pdo = getDB();
        $stmt = $pdo->prepare("DELETE FROM user_sessions WHERE user_id = ?");
        return $stmt->execute([$userId]);
    }

    private static function cleanOldSessionsMysql() {
        require_once 'config.php';
        $pdo = getDB();
        $stmt = $pdo->prepare("DELETE FROM user_sessions WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 HOUR)");
        return $stmt->execute();
    }

    // Custom referral link JSON implementations
    private static function saveCustomReferralLinkJson($userId, $customParameter) {
        $customLinks = readJsonFile(DATA_DIR . 'custom_referral_links.json');

        $customLinks[$customParameter] = [
            'user_id' => $userId,
            'custom_parameter' => $customParameter,
            'created_at' => date('Y-m-d H:i:s')
        ];

        return writeJsonFile(DATA_DIR . 'custom_referral_links.json', $customLinks);
    }

    private static function customParameterExistsJson($customParameter) {
        $customLinks = readJsonFile(DATA_DIR . 'custom_referral_links.json');
        return isset($customLinks[$customParameter]);
    }

    private static function getUserIdByCustomParameterJson($customParameter) {
        $customLinks = readJsonFile(DATA_DIR . 'custom_referral_links.json');
        return $customLinks[$customParameter]['user_id'] ?? null;
    }

    private static function updateCustomReferralLinkJson($oldParameter, $newParameter) {
        $customLinks = readJsonFile(DATA_DIR . 'custom_referral_links.json');

        if (!isset($customLinks[$oldParameter])) {
            return false;
        }

        $linkData = $customLinks[$oldParameter];
        $linkData['custom_parameter'] = $newParameter;
        $linkData['updated_at'] = date('Y-m-d H:i:s');

        unset($customLinks[$oldParameter]);
        $customLinks[$newParameter] = $linkData;

        return writeJsonFile(DATA_DIR . 'custom_referral_links.json', $customLinks);
    }

    private static function removeCustomReferralLinkJson($customParameter) {
        $customLinks = readJsonFile(DATA_DIR . 'custom_referral_links.json');

        if (!isset($customLinks[$customParameter])) {
            return false;
        }

        unset($customLinks[$customParameter]);
        return writeJsonFile(DATA_DIR . 'custom_referral_links.json', $customLinks);
    }

    private static function getCustomReferralLinksByUserJson($userId) {
        $customLinks = readJsonFile(DATA_DIR . 'custom_referral_links.json');
        $userLinks = [];

        foreach ($customLinks as $parameter => $linkData) {
            if ($linkData['user_id'] == $userId) {
                $userLinks[] = $linkData;
            }
        }

        return $userLinks;
    }

    private static function getAllCustomReferralLinksJson() {
        $customLinks = readJsonFile(DATA_DIR . 'custom_referral_links.json');
        return array_values($customLinks);
    }

    // Custom referral link MySQL implementations (placeholder)
    private static function saveCustomReferralLinkMysql($userId, $customParameter) {
        // TODO: Implement MySQL version
        return false;
    }

    private static function customParameterExistsMysql($customParameter) {
        // TODO: Implement MySQL version
        return false;
    }

    private static function getUserIdByCustomParameterMysql($customParameter) {
        // TODO: Implement MySQL version
        return null;
    }

    private static function updateCustomReferralLinkMysql($oldParameter, $newParameter) {
        // TODO: Implement MySQL version
        return false;
    }

    private static function removeCustomReferralLinkMysql($customParameter) {
        // TODO: Implement MySQL version
        return false;
    }

    private static function getCustomReferralLinksByUserMysql($userId) {
        // TODO: Implement MySQL version
        return [];
    }

    private static function getAllCustomReferralLinksMysql() {
        // TODO: Implement MySQL version
        return [];
    }
}
?>
