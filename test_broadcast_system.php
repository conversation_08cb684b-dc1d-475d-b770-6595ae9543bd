<?php
/**
 * Broadcast System Test Script
 * Tests the fixed broadcast messaging system
 */

require_once 'config.php';
require_once 'core_functions.php';
require_once 'storage_abstraction.php';

echo "🧪 BROADCAST SYSTEM TEST\n";
echo "========================\n\n";

// Test 1: Check if getAllUsers function works correctly
echo "📋 Test 1: User Retrieval\n";
echo "--------------------------\n";

$allUsers = getAllUsers();
echo "✅ Total users found: " . count($allUsers) . "\n";

if (empty($allUsers)) {
    echo "❌ No users found! Cannot test broadcast system.\n";
    exit(1);
}

// Show first few users
$sampleUsers = array_slice($allUsers, 0, 5);
echo "📝 Sample user IDs: " . implode(', ', $sampleUsers) . "\n";

// Check for duplicates
$uniqueUsers = array_unique($allUsers);
if (count($allUsers) !== count($uniqueUsers)) {
    echo "⚠️  WARNING: Duplicate user IDs detected!\n";
    echo "   Original count: " . count($allUsers) . "\n";
    echo "   Unique count: " . count($uniqueUsers) . "\n";
} else {
    echo "✅ No duplicate user IDs found\n";
}

echo "\n";

// Test 2: Check broadcast function exists and basic structure
echo "🔧 Test 2: Function Availability\n";
echo "---------------------------------\n";

if (function_exists('broadcastMessage')) {
    echo "✅ broadcastMessage function exists\n";
} else {
    echo "❌ broadcastMessage function missing\n";
    exit(1);
}

if (function_exists('handleBroadcastTextStep2')) {
    echo "✅ handleBroadcastTextStep2 function exists\n";
} else {
    echo "❌ handleBroadcastTextStep2 function missing\n";
}

if (function_exists('logBroadcastCompletion')) {
    echo "✅ logBroadcastCompletion function exists\n";
} else {
    echo "❌ logBroadcastCompletion function missing\n";
}

echo "\n";

// Test 3: Test broadcast system with a small subset (dry run)
echo "🚀 Test 3: Dry Run Broadcast Test\n";
echo "----------------------------------\n";

// Create a test message
$testMessage = [
    'text' => '🧪 This is a test broadcast message from the system test script. Please ignore.'
];

// Test with first 3 users only for safety
$testUsers = array_slice($allUsers, 0, 3);
echo "📤 Testing broadcast to " . count($testUsers) . " users\n";

// Mock the getAllUsers function temporarily for testing
function getAllUsersForTest() {
    global $testUsers;
    return $testUsers;
}

// Simulate broadcast (we'll modify the function to use our test users)
echo "⏳ Simulating broadcast process...\n";

$startTime = time();

// Test message type detection
$messageType = detectMessageType($testMessage);
echo "✅ Message type detected: {$messageType}\n";

// Test logging
logBroadcastActivity(999999, $messageType, count($testUsers));
echo "✅ Broadcast activity logged\n";

$endTime = time();
echo "⏱️  Test completed in " . ($endTime - $startTime) . " seconds\n";

echo "\n";

// Test 4: Check broadcast logs
echo "📊 Test 4: Broadcast Logging\n";
echo "-----------------------------\n";

$logFile = DATA_DIR . 'broadcast_logs.json';
if (file_exists($logFile)) {
    $logs = json_decode(file_get_contents($logFile), true);
    if ($logs) {
        $recentLogs = array_slice($logs, -3);
        echo "✅ Broadcast logs found (" . count($logs) . " total entries)\n";
        echo "📝 Recent log entries:\n";
        foreach ($recentLogs as $log) {
            echo "   - " . $log['date'] . " | Admin: " . $log['admin_id'] . " | Type: " . $log['message_type'] . " | Users: " . ($log['user_count'] ?? 'N/A') . "\n";
        }
    } else {
        echo "⚠️  Broadcast log file exists but is empty or invalid\n";
    }
} else {
    echo "⚠️  No broadcast log file found\n";
}

echo "\n";

// Test 5: Rate limiting check
echo "⏱️  Test 5: Rate Limiting\n";
echo "-------------------------\n";

if (defined('RATE_LIMIT_ENABLED') && RATE_LIMIT_ENABLED) {
    echo "✅ Rate limiting is enabled\n";
    echo "📊 Max requests per minute: " . (defined('MAX_REQUESTS_PER_MINUTE') ? MAX_REQUESTS_PER_MINUTE : 'Not defined') . "\n";
} else {
    echo "⚠️  Rate limiting is disabled\n";
}

echo "\n";

// Test 6: Storage mode check
echo "💾 Test 6: Storage Configuration\n";
echo "--------------------------------\n";

echo "📁 Storage mode: " . STORAGE_MODE . "\n";

if (STORAGE_MODE === 'json') {
    echo "📂 Users file: " . USERS_FILE . "\n";
    echo "📊 File exists: " . (file_exists(USERS_FILE) ? 'Yes' : 'No') . "\n";
    if (file_exists(USERS_FILE)) {
        echo "📏 File size: " . number_format(filesize(USERS_FILE)) . " bytes\n";
    }
} else {
    echo "🗄️  MySQL mode - checking database connection...\n";
    try {
        $db = getDB();
        if ($db) {
            echo "✅ Database connection successful\n";
            $stmt = $db->query("SELECT COUNT(*) as count FROM users");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "📊 Users in database: " . $result['count'] . "\n";
        } else {
            echo "❌ Database connection failed\n";
        }
    } catch (Exception $e) {
        echo "❌ Database error: " . $e->getMessage() . "\n";
    }
}

echo "\n";

// Summary
echo "📋 TEST SUMMARY\n";
echo "===============\n";

$issues = [];

if (empty($allUsers)) {
    $issues[] = "No users found for broadcasting";
}

if (count($allUsers) !== count(array_unique($allUsers))) {
    $issues[] = "Duplicate user IDs detected";
}

if (!function_exists('broadcastMessage')) {
    $issues[] = "broadcastMessage function missing";
}

if (!function_exists('handleBroadcastTextStep2')) {
    $issues[] = "handleBroadcastTextStep2 function missing";
}

if (empty($issues)) {
    echo "✅ ALL TESTS PASSED!\n";
    echo "🎉 The broadcast system appears to be working correctly.\n";
    echo "\n";
    echo "🔧 RECOMMENDATIONS:\n";
    echo "- Test with a small group of real users first\n";
    echo "- Monitor the broadcast logs during actual broadcasts\n";
    echo "- Check error logs for any issues during broadcasting\n";
    echo "- Consider the rate limits for your hosting environment\n";
} else {
    echo "❌ ISSUES FOUND:\n";
    foreach ($issues as $issue) {
        echo "   • " . $issue . "\n";
    }
    echo "\n";
    echo "🔧 Please fix these issues before using the broadcast system.\n";
}

echo "\n";
echo "📞 For production use:\n";
echo "1. Use the admin panel to send broadcasts\n";
echo "2. Monitor the broadcast logs in data/broadcast_logs.json\n";
echo "3. Check the error logs in data/debug.log for any issues\n";
echo "4. Start with small test broadcasts before large campaigns\n";

echo "\n🏁 Test completed!\n";
?>
