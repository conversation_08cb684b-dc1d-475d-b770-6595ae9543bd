<?php
/**
 * Enhanced Broadcast System Test Script
 * Tests the new session-based broadcast system with cancellation and progress tracking
 */

require_once 'config.php';
require_once 'core_functions.php';
require_once 'storage_abstraction.php';
require_once 'user_account_handlers.php';

echo "🚀 ENHANCED BROADCAST SYSTEM TEST\n";
echo "==================================\n\n";

// Test 1: Check if all new functions exist
echo "🔧 Test 1: Function Availability\n";
echo "---------------------------------\n";

$requiredFunctions = [
    'initializeBroadcastSession',
    'updateBroadcastSession', 
    'isBroadcastCancelled',
    'cancelBroadcast',
    'getActiveBroadcast',
    'cleanupBroadcastSession',
    'sendBroadcastProgress',
    'sendBroadcastCompletion',
    'calculateEstimatedTime',
    'formatTime',
    'createProgressBar'
];

$missingFunctions = [];
foreach ($requiredFunctions as $func) {
    if (function_exists($func)) {
        echo "✅ {$func}\n";
    } else {
        echo "❌ {$func} - MISSING\n";
        $missingFunctions[] = $func;
    }
}

if (!empty($missingFunctions)) {
    echo "\n❌ Missing functions detected. Cannot proceed with tests.\n";
    exit(1);
}

echo "\n";

// Test 2: Test session management functions
echo "📋 Test 2: Session Management\n";
echo "-----------------------------\n";

$testAdminId = '999999';
$testMessage = ['text' => 'Test broadcast message'];

// Test session initialization
echo "🔄 Testing session initialization...\n";
$broadcastId = initializeBroadcastSession($testAdminId, $testMessage);
if ($broadcastId) {
    echo "✅ Session initialized: " . substr($broadcastId, -8) . "\n";
} else {
    echo "❌ Session initialization failed\n";
    exit(1);
}

// Test active broadcast detection
echo "🔍 Testing active broadcast detection...\n";
$activeBroadcast = getActiveBroadcast($testAdminId);
if ($activeBroadcast === $broadcastId) {
    echo "✅ Active broadcast detected correctly\n";
} else {
    echo "❌ Active broadcast detection failed\n";
}

// Test session update
echo "📝 Testing session update...\n";
$testResults = [
    'status' => 'running',
    'success_count' => 10,
    'failed_count' => 2,
    'total_users' => 100
];
updateBroadcastSession($broadcastId, $testResults);
echo "✅ Session updated\n";

// Test cancellation
echo "🚫 Testing broadcast cancellation...\n";
$cancelResult = cancelBroadcast($broadcastId, $testAdminId);
if ($cancelResult) {
    echo "✅ Broadcast cancelled successfully\n";
} else {
    echo "❌ Broadcast cancellation failed\n";
}

// Test cancellation detection
echo "🔍 Testing cancellation detection...\n";
$isCancelled = isBroadcastCancelled($broadcastId);
if ($isCancelled) {
    echo "✅ Cancellation detected correctly\n";
} else {
    echo "❌ Cancellation detection failed\n";
}

// Test cleanup
echo "🧹 Testing session cleanup...\n";
cleanupBroadcastSession($broadcastId);
$activeAfterCleanup = getActiveBroadcast($testAdminId);
if ($activeAfterCleanup === null) {
    echo "✅ Session cleaned up successfully\n";
} else {
    echo "❌ Session cleanup failed\n";
}

echo "\n";

// Test 3: Test utility functions
echo "🛠️  Test 3: Utility Functions\n";
echo "-----------------------------\n";

// Test time formatting
echo "⏰ Testing time formatting...\n";
$timeTests = [30, 90, 3661, 7322];
foreach ($timeTests as $seconds) {
    $formatted = formatTime($seconds);
    echo "   {$seconds}s → {$formatted}\n";
}
echo "✅ Time formatting working\n";

// Test ETA calculation
echo "📊 Testing ETA calculation...\n";
$startTime = time() - 60; // Started 1 minute ago
$eta = calculateEstimatedTime($startTime, 100, 1000);
echo "   ETA for 100/1000 users: " . formatTime($eta) . "\n";
echo "✅ ETA calculation working\n";

// Test progress bar
echo "📈 Testing progress bar...\n";
$progressTests = [0, 25, 50, 75, 100];
foreach ($progressTests as $percent) {
    $bar = createProgressBar($percent);
    echo "   {$percent}%: {$bar}\n";
}
echo "✅ Progress bar working\n";

echo "\n";

// Test 4: Test broadcast system integration
echo "🎯 Test 4: Broadcast System Integration\n";
echo "---------------------------------------\n";

// Get user count
$allUsers = getAllUsers();
$userCount = count($allUsers);
echo "📊 Total users in system: {$userCount}\n";

if ($userCount === 0) {
    echo "⚠️  No users found - cannot test broadcast integration\n";
} else {
    echo "✅ User data available for broadcast testing\n";
    
    // Test duplicate prevention
    $uniqueUsers = array_unique($allUsers);
    if (count($allUsers) === count($uniqueUsers)) {
        echo "✅ No duplicate users detected\n";
    } else {
        echo "⚠️  Duplicate users detected: " . (count($allUsers) - count($uniqueUsers)) . " duplicates\n";
    }
    
    // Show sample users
    $sampleUsers = array_slice($allUsers, 0, 3);
    echo "📝 Sample user IDs: " . implode(', ', $sampleUsers) . "\n";
}

echo "\n";

// Test 5: Test file system and permissions
echo "📁 Test 5: File System\n";
echo "----------------------\n";

$dataDir = DATA_DIR;
echo "📂 Data directory: {$dataDir}\n";

if (!is_dir($dataDir)) {
    echo "❌ Data directory does not exist\n";
} elseif (!is_writable($dataDir)) {
    echo "❌ Data directory is not writable\n";
} else {
    echo "✅ Data directory is accessible and writable\n";
}

// Test session file creation
$sessionFile = $dataDir . 'broadcast_sessions.json';
if (file_exists($sessionFile)) {
    echo "📄 Session file exists: " . number_format(filesize($sessionFile)) . " bytes\n";
    echo "✅ Session file accessible\n";
} else {
    echo "ℹ️  Session file will be created on first broadcast\n";
}

echo "\n";

// Test 6: Test admin functions integration
echo "👑 Test 6: Admin Integration\n";
echo "----------------------------\n";

// Test admin check (using a test admin ID)
$testAdminIds = ['2027123358', '8153676253']; // Known admin IDs from config
$adminFound = false;

foreach ($testAdminIds as $adminId) {
    if (function_exists('isAdmin') && isAdmin($adminId)) {
        echo "✅ Admin function working for ID: {$adminId}\n";
        $adminFound = true;
        break;
    }
}

if (!$adminFound) {
    echo "⚠️  No admin IDs found or isAdmin function not working\n";
}

echo "\n";

// Summary
echo "📋 TEST SUMMARY\n";
echo "===============\n";

$issues = [];

if (!empty($missingFunctions)) {
    $issues[] = "Missing functions: " . implode(', ', $missingFunctions);
}

if (!is_dir($dataDir) || !is_writable($dataDir)) {
    $issues[] = "Data directory not accessible or writable";
}

if ($userCount === 0) {
    $issues[] = "No users found for broadcast testing";
}

if (empty($issues)) {
    echo "✅ ALL TESTS PASSED!\n";
    echo "🎉 The enhanced broadcast system is ready for production use.\n";
    echo "\n";
    echo "🔧 NEW FEATURES AVAILABLE:\n";
    echo "- ✅ Session-based broadcast tracking\n";
    echo "- ✅ Real-time progress updates every 10 users\n";
    echo "- ✅ Admin cancellation with /cancel command\n";
    echo "- ✅ Duplicate user prevention\n";
    echo "- ✅ Estimated time remaining calculation\n";
    echo "- ✅ Visual progress bars\n";
    echo "- ✅ Comprehensive completion statistics\n";
    echo "- ✅ Robust error handling and logging\n";
    echo "\n";
    echo "📱 USAGE:\n";
    echo "1. Start a broadcast as usual through admin panel\n";
    echo "2. Receive real-time progress updates during broadcast\n";
    echo "3. Send /cancel anytime to stop an active broadcast\n";
    echo "4. Get detailed completion statistics when finished\n";
    echo "\n";
    echo "🛡️  SAFETY FEATURES:\n";
    echo "- Only one broadcast per admin at a time\n";
    echo "- Graceful cancellation without data loss\n";
    echo "- Duplicate prevention with robust tracking\n";
    echo "- Session cleanup after completion/cancellation\n";
    
} else {
    echo "❌ ISSUES FOUND:\n";
    foreach ($issues as $issue) {
        echo "   • " . $issue . "\n";
    }
    echo "\n";
    echo "🔧 Please fix these issues before using the enhanced broadcast system.\n";
}

echo "\n🏁 Enhanced broadcast system test completed!\n";
?>
