<?php
/**
 * Test script for the /rank command
 * This script tests the rank command functionality without sending actual Telegram messages
 */

require_once 'config.php';
require_once 'core_functions.php';
require_once 'admin_handlers.php';

echo "🧪 Testing /rank Command Functionality\n";
echo "=====================================\n\n";

// Test 1: Check if admin authentication works
echo "Test 1: Admin Authentication\n";
$testAdminId = ADMIN_IDS[0]; // Use first admin ID
$testNonAdminId = 999999999; // Non-admin ID

if (isAdmin($testAdminId)) {
    echo "✅ Admin authentication works for ID: {$testAdminId}\n";
} else {
    echo "❌ Admin authentication failed for ID: {$testAdminId}\n";
}

if (!isAdmin($testNonAdminId)) {
    echo "✅ Non-admin correctly rejected for ID: {$testNonAdminId}\n";
} else {
    echo "❌ Non-admin incorrectly accepted for ID: {$testNonAdminId}\n";
}

echo "\n";

// Test 2: Check data retrieval functions
echo "Test 2: Data Retrieval Functions\n";

try {
    $topUsers = getTopUsersByWithdrawals(5);
    echo "✅ getTopUsersByWithdrawals() executed successfully\n";
    echo "📊 Found " . count($topUsers) . " users with withdrawals\n";
    
    if (!empty($topUsers)) {
        echo "📋 Sample user data structure:\n";
        $sampleUser = $topUsers[0];
        foreach ($sampleUser as $key => $value) {
            $displayValue = is_array($value) ? '[array]' : $value;
            echo "   - {$key}: {$displayValue}\n";
        }
    }
} catch (Exception $e) {
    echo "❌ Error in getTopUsersByWithdrawals(): " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Test rank emoji function
echo "Test 3: Rank Emoji Function\n";
for ($i = 1; $i <= 10; $i++) {
    $emoji = getRankEmoji($i);
    echo "Rank {$i}: {$emoji}\n";
}

echo "\n";

// Test 4: Test the complete rank command logic (without sending message)
echo "Test 4: Complete Rank Command Logic\n";

// Mock the sendMessage function for testing
function sendMessage($chatId, $message, $keyboard = null, $parseMode = null) {
    echo "📤 Message would be sent to chat {$chatId}:\n";
    echo "---\n";
    echo $message . "\n";
    echo "---\n";
    return true;
}

// Test with admin user
echo "Testing with admin user:\n";
handleRankCommand($testAdminId, 12345);

echo "\nTesting with non-admin user:\n";
handleRankCommand($testNonAdminId, 12345);

echo "\n";

// Test 5: Storage mode verification
echo "Test 5: Storage Mode Verification\n";
echo "Current storage mode: " . STORAGE_MODE . "\n";

if (STORAGE_MODE === 'json') {
    if (file_exists(USERS_FILE)) {
        $fileSize = filesize(USERS_FILE);
        echo "✅ Users file exists: " . USERS_FILE . " (Size: " . number_format($fileSize) . " bytes)\n";
        
        // Check if file is readable
        if (is_readable(USERS_FILE)) {
            echo "✅ Users file is readable\n";
            
            // Try to read a small sample
            $users = readJsonFile(USERS_FILE);
            if ($users !== false && is_array($users)) {
                echo "✅ Users file contains valid JSON with " . count($users) . " users\n";
                
                // Count users with withdrawals
                $usersWithWithdrawals = 0;
                foreach ($users as $userData) {
                    if (($userData['successful_withdraw'] ?? 0) > 0) {
                        $usersWithWithdrawals++;
                    }
                }
                echo "📊 Users with successful withdrawals: {$usersWithWithdrawals}\n";
            } else {
                echo "❌ Users file contains invalid JSON or is empty\n";
            }
        } else {
            echo "❌ Users file is not readable\n";
        }
    } else {
        echo "❌ Users file does not exist: " . USERS_FILE . "\n";
    }
} else {
    echo "MySQL mode detected - testing database connection...\n";
    try {
        $pdo = getDB();
        echo "✅ Database connection successful\n";
        
        // Test user count
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM users");
        $result = $stmt->fetch();
        echo "📊 Total users in database: " . $result['total'] . "\n";
        
        // Test users with withdrawals
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM users WHERE successful_withdraw > 0");
        $result = $stmt->fetch();
        echo "📊 Users with withdrawals: " . $result['total'] . "\n";
        
    } catch (Exception $e) {
        echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    }
}

echo "\n";

// Test 6: Performance test
echo "Test 6: Performance Test\n";
$startTime = microtime(true);

try {
    $topUsers = getTopUsersByWithdrawals(15);
    $endTime = microtime(true);
    $executionTime = round(($endTime - $startTime) * 1000, 2);
    
    echo "✅ Rank data retrieval completed in {$executionTime}ms\n";
    echo "📊 Retrieved " . count($topUsers) . " users\n";
    
    if ($executionTime > 1000) {
        echo "⚠️  Warning: Execution time is high (>{$executionTime}ms). Consider optimization.\n";
    } else {
        echo "✅ Performance is good (<1000ms)\n";
    }
    
} catch (Exception $e) {
    echo "❌ Performance test failed: " . $e->getMessage() . "\n";
}

echo "\n";

echo "🎉 Test completed!\n";
echo "================\n";
echo "Summary:\n";
echo "- Admin authentication: Tested\n";
echo "- Data retrieval: Tested\n";
echo "- Rank command logic: Tested\n";
echo "- Storage verification: Tested\n";
echo "- Performance: Tested\n";
echo "\nTo use the command in Telegram:\n";
echo "1. Send '/rank' to the bot in a private chat\n";
echo "2. Only admin users (IDs: " . implode(', ', ADMIN_IDS) . ") can use this command\n";
echo "3. The command will show top 15 users by withdrawal amounts\n";
?>
