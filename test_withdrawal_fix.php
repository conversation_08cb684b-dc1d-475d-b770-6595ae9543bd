<?php
/**
 * Simple test to verify withdrawal callback functionality
 */

echo "🧪 Testing Withdrawal Callback Fix\n";
echo "==================================\n\n";

// Include required files
require_once 'config.php';
require_once 'core_functions.php';
require_once 'withdrawal_handlers.php';

// Test 1: Check if functions exist
echo "1. Checking function existence...\n";

$functions = [
    'handleWithdrawalApprovalCallback',
    'getUpdatedWithdrawalMessage', 
    'answerCallbackQuery',
    'editMessageText',
    'getCurrentDate',
    'isAdmin',
    'getUser',
    'updateWithdrawalStatus',
    'sendMessage'
];

$missing = [];
foreach ($functions as $func) {
    if (!function_exists($func)) {
        $missing[] = $func;
    }
}

if (empty($missing)) {
    echo "   ✅ All required functions exist\n";
} else {
    echo "   ❌ Missing functions: " . implode(', ', $missing) . "\n";
}

// Test 2: Check admin IDs
echo "\n2. Checking admin configuration...\n";
echo "   Admin IDs: " . implode(', ', ADMIN_IDS) . "\n";
echo "   Primary Admin: " . ADMIN_ID . "\n";

// Test 3: Test callback data parsing
echo "\n3. Testing callback data parsing...\n";

$testCallbackData = [
    'approve_withdrawal_123456789',
    'reject_withdrawal_987654321'
];

foreach ($testCallbackData as $data) {
    if (strpos($data, 'approve_withdrawal_') === 0) {
        $userId = str_replace('approve_withdrawal_', '', $data);
        echo "   ✅ Approve callback parsed: User ID {$userId}\n";
    } elseif (strpos($data, 'reject_withdrawal_') === 0) {
        $userId = str_replace('reject_withdrawal_', '', $data);
        echo "   ✅ Reject callback parsed: User ID {$userId}\n";
    }
}

// Test 4: Test webhook routing logic
echo "\n4. Testing webhook routing logic...\n";

$testData = 'approve_withdrawal_123456789';
if (strpos($testData, 'approve_withdrawal_') === 0) {
    echo "   ✅ Webhook would route to approval handler\n";
} else {
    echo "   ❌ Webhook routing failed\n";
}

// Test 5: Test admin check
echo "\n5. Testing admin verification...\n";
$testAdminId = **********; // The admin ID we're sending notifications to
if (isAdmin($testAdminId)) {
    echo "   ✅ Admin ID {$testAdminId} is recognized as admin\n";
} else {
    echo "   ❌ Admin ID {$testAdminId} is NOT recognized as admin\n";
}

echo "\n6. Testing date function...\n";
$currentDate = getCurrentDate();
echo "   Current date: {$currentDate}\n";

echo "\n==================================\n";
echo "✅ Basic functionality test complete!\n";
echo "\nIf all tests passed, the withdrawal callback system should work.\n";
echo "The issue might be in the webhook configuration or Telegram setup.\n";

?>
