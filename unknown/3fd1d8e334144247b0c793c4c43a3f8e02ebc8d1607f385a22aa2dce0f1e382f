<?php
/**
 * Migration script to initialize withdrawal tax and control settings
 * This script sets up the global withdrawal configuration for existing installations
 */

require_once 'config.php';
require_once 'core_functions.php';
require_once 'storage_abstraction.php';

echo "=== Withdrawal Tax & Control Settings Migration ===\n\n";

// Check current storage mode
echo "Storage mode: " . STORAGE_MODE . "\n\n";

if (STORAGE_MODE === 'json') {
    migrateJsonStorage();
} else {
    migrateMysqlStorage();
}

function migrateJsonStorage() {
    $adminFile = ADMIN_FILE;
    
    if (!file_exists($adminFile)) {
        echo "❌ Admin settings file not found: {$adminFile}\n";
        echo "Creating default admin settings file...\n";
        createDefaultAdminSettings();
        return;
    }
    
    $adminData = json_decode(file_get_contents($adminFile), true);
    if ($adminData === null) {
        echo "❌ Invalid JSON in admin settings file\n";
        return;
    }
    
    echo "📊 Current admin settings found:\n";
    
    // Check if global withdrawal settings already exist
    if (isset($adminData[0]['withdrawal_enabled']) && 
        isset($adminData[0]['withdrawal_tax_type']) && 
        isset($adminData[0]['withdrawal_tax_amount'])) {
        echo "✅ Global withdrawal settings already exist\n";
        echo "Current settings:\n";
        echo "  Withdrawal Enabled: " . ($adminData[0]['withdrawal_enabled'] ? 'Yes' : 'No') . "\n";
        echo "  Tax Type: {$adminData[0]['withdrawal_tax_type']}\n";
        echo "  Tax Amount: {$adminData[0]['withdrawal_tax_amount']}\n\n";
        
        echo "Do you want to update these settings? (y/n): ";
        $handle = fopen("php://stdin", "r");
        $input = trim(fgets($handle));
        fclose($handle);
        
        if (strtolower($input) !== 'y') {
            echo "Migration cancelled.\n";
            return;
        }
    }
    
    // Create global admin entry if it doesn't exist
    if (!isset($adminData[0])) {
        $adminData[0] = [];
        echo "📝 Creating global admin configuration (admin_id = 0)\n";
    }
    
    // Set default withdrawal settings
    $defaultSettings = [
        'withdrawal_enabled' => true,
        'withdrawal_tax_type' => 'none',
        'withdrawal_tax_amount' => 0
    ];
    
    echo "🔧 Setting up withdrawal configuration:\n";
    echo "  Withdrawal Enabled: Yes (default)\n";
    echo "  Tax Type: None (default)\n";
    echo "  Tax Amount: 0 (default)\n\n";
    
    // Ask user for custom settings
    echo "Do you want to configure custom withdrawal settings? (y/n): ";
    $handle = fopen("php://stdin", "r");
    $input = trim(fgets($handle));
    fclose($handle);
    
    if (strtolower($input) === 'y') {
        $customSettings = getCustomSettings();
        if ($customSettings) {
            $defaultSettings = $customSettings;
        }
    }
    
    // Apply settings
    $adminData[0]['withdrawal_enabled'] = $defaultSettings['withdrawal_enabled'];
    $adminData[0]['withdrawal_tax_type'] = $defaultSettings['withdrawal_tax_type'];
    $adminData[0]['withdrawal_tax_amount'] = $defaultSettings['withdrawal_tax_amount'];
    
    // Save updated configuration
    if (file_put_contents($adminFile, json_encode($adminData, JSON_PRETTY_PRINT), LOCK_EX)) {
        echo "\n✅ Successfully migrated withdrawal settings!\n";
        echo "\n📊 New global configuration:\n";
        echo "  Withdrawal Enabled: " . ($defaultSettings['withdrawal_enabled'] ? 'Yes' : 'No') . "\n";
        echo "  Tax Type: {$defaultSettings['withdrawal_tax_type']}\n";
        echo "  Tax Amount: {$defaultSettings['withdrawal_tax_amount']}\n";
        
        echo "\n🔧 Next steps:\n";
        echo "1. All admins will now see the same withdrawal settings\n";
        echo "2. Changes made by any admin will be visible to all other admins\n";
        echo "3. Test the withdrawal tax system with the test script\n";
        echo "4. Configure withdrawal settings through the admin panel\n";
        
    } else {
        echo "\n❌ Failed to save migrated configuration\n";
    }
}

function migrateMysqlStorage() {
    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
            DB_USER,
            DB_PASS,
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );
        
        echo "✅ Connected to MySQL database\n\n";
        
        // Check if global withdrawal settings exist
        $stmt = $pdo->prepare("SELECT withdrawal_enabled, withdrawal_tax_type, withdrawal_tax_amount FROM admin_settings WHERE admin_id = 0");
        $stmt->execute();
        $globalConfig = $stmt->fetch();
        
        if ($globalConfig && 
            isset($globalConfig['withdrawal_enabled']) && 
            isset($globalConfig['withdrawal_tax_type']) && 
            isset($globalConfig['withdrawal_tax_amount'])) {
            echo "✅ Global withdrawal settings already exist\n";
            echo "Current settings:\n";
            echo "  Withdrawal Enabled: " . ($globalConfig['withdrawal_enabled'] ? 'Yes' : 'No') . "\n";
            echo "  Tax Type: {$globalConfig['withdrawal_tax_type']}\n";
            echo "  Tax Amount: {$globalConfig['withdrawal_tax_amount']}\n\n";
            
            echo "Do you want to update these settings? (y/n): ";
            $handle = fopen("php://stdin", "r");
            $input = trim(fgets($handle));
            fclose($handle);
            
            if (strtolower($input) !== 'y') {
                echo "Migration cancelled.\n";
                return;
            }
        }
        
        // Set default withdrawal settings
        $defaultSettings = [
            'withdrawal_enabled' => true,
            'withdrawal_tax_type' => 'none',
            'withdrawal_tax_amount' => 0
        ];
        
        echo "🔧 Setting up withdrawal configuration:\n";
        echo "  Withdrawal Enabled: Yes (default)\n";
        echo "  Tax Type: None (default)\n";
        echo "  Tax Amount: 0 (default)\n\n";
        
        // Ask user for custom settings
        echo "Do you want to configure custom withdrawal settings? (y/n): ";
        $handle = fopen("php://stdin", "r");
        $input = trim(fgets($handle));
        fclose($handle);
        
        if (strtolower($input) === 'y') {
            $customSettings = getCustomSettings();
            if ($customSettings) {
                $defaultSettings = $customSettings;
            }
        }
        
        // Insert or update global configuration
        $stmt = $pdo->prepare("
            INSERT INTO admin_settings (admin_id, withdrawal_enabled, withdrawal_tax_type, withdrawal_tax_amount) 
            VALUES (0, ?, ?, ?)
            ON DUPLICATE KEY UPDATE 
            withdrawal_enabled = VALUES(withdrawal_enabled),
            withdrawal_tax_type = VALUES(withdrawal_tax_type),
            withdrawal_tax_amount = VALUES(withdrawal_tax_amount)
        ");
        
        if ($stmt->execute([
            $defaultSettings['withdrawal_enabled'] ? 1 : 0,
            $defaultSettings['withdrawal_tax_type'],
            $defaultSettings['withdrawal_tax_amount']
        ])) {
            echo "\n✅ Successfully migrated withdrawal settings!\n";
            echo "\n📊 New global configuration:\n";
            echo "  Withdrawal Enabled: " . ($defaultSettings['withdrawal_enabled'] ? 'Yes' : 'No') . "\n";
            echo "  Tax Type: {$defaultSettings['withdrawal_tax_type']}\n";
            echo "  Tax Amount: {$defaultSettings['withdrawal_tax_amount']}\n";
            
            echo "\n🔧 Next steps:\n";
            echo "1. All admins will now see the same withdrawal settings\n";
            echo "2. Changes made by any admin will be visible to all other admins\n";
            echo "3. Test the withdrawal tax system with the test script\n";
            echo "4. Configure withdrawal settings through the admin panel\n";
        } else {
            echo "\n❌ Failed to save migrated configuration\n";
        }
        
    } catch (PDOException $e) {
        echo "❌ Database error: " . $e->getMessage() . "\n";
    }
}

function getCustomSettings() {
    echo "\n🔧 Custom Withdrawal Settings Configuration\n";
    echo "==========================================\n";
    
    // Ask for withdrawal enabled/disabled
    echo "1. Enable withdrawals by default? (y/n): ";
    $handle = fopen("php://stdin", "r");
    $enabledInput = trim(fgets($handle));
    fclose($handle);
    
    $enabled = strtolower($enabledInput) === 'y';
    
    if (!$enabled) {
        echo "   Withdrawals will be disabled by default.\n";
        return [
            'withdrawal_enabled' => false,
            'withdrawal_tax_type' => 'none',
            'withdrawal_tax_amount' => 0
        ];
    }
    
    // Ask for tax type
    echo "\n2. Select tax type:\n";
    echo "   1. No tax (default)\n";
    echo "   2. Fixed amount per withdrawal\n";
    echo "   3. Percentage of withdrawal amount\n";
    echo "   Enter choice (1-3): ";
    
    $handle = fopen("php://stdin", "r");
    $taxChoice = trim(fgets($handle));
    fclose($handle);
    
    $taxType = 'none';
    $taxAmount = 0;
    
    switch ($taxChoice) {
        case '2':
            $taxType = 'fixed';
            echo "\n   Enter fixed tax amount (e.g., 10 for ₹10): ";
            $handle = fopen("php://stdin", "r");
            $amountInput = trim(fgets($handle));
            fclose($handle);
            $taxAmount = floatval($amountInput);
            
            if ($taxAmount < 0) {
                echo "   Invalid amount. Using 0.\n";
                $taxAmount = 0;
            }
            break;
            
        case '3':
            $taxType = 'percentage';
            echo "\n   Enter percentage (e.g., 5 for 5%): ";
            $handle = fopen("php://stdin", "r");
            $percentInput = trim(fgets($handle));
            fclose($handle);
            $taxAmount = floatval($percentInput);
            
            if ($taxAmount < 0 || $taxAmount >= 100) {
                echo "   Invalid percentage. Using 0.\n";
                $taxAmount = 0;
                $taxType = 'none';
            }
            break;
            
        default:
            echo "   Using no tax (default).\n";
            break;
    }
    
    echo "\n📋 Configuration Summary:\n";
    echo "  Withdrawal Enabled: " . ($enabled ? 'Yes' : 'No') . "\n";
    echo "  Tax Type: {$taxType}\n";
    echo "  Tax Amount: {$taxAmount}\n";
    
    echo "\nConfirm these settings? (y/n): ";
    $handle = fopen("php://stdin", "r");
    $confirmInput = trim(fgets($handle));
    fclose($handle);
    
    if (strtolower($confirmInput) !== 'y') {
        echo "Configuration cancelled. Using defaults.\n";
        return null;
    }
    
    return [
        'withdrawal_enabled' => $enabled,
        'withdrawal_tax_type' => $taxType,
        'withdrawal_tax_amount' => $taxAmount
    ];
}

function createDefaultAdminSettings() {
    $defaultData = [
        0 => [
            'withdrawal_enabled' => true,
            'withdrawal_tax_type' => 'none',
            'withdrawal_tax_amount' => 0
        ]
    ];
    
    if (file_put_contents(ADMIN_FILE, json_encode($defaultData, JSON_PRETTY_PRINT), LOCK_EX)) {
        echo "✅ Created default admin settings file with withdrawal configuration\n";
    } else {
        echo "❌ Failed to create default admin settings file\n";
    }
}

echo "\n=== Migration Complete ===\n";
echo "The withdrawal tax and control system is now configured.\n";
echo "All admins will see consistent withdrawal settings.\n\n";

echo "🧪 To test the system, run: php test_withdrawal_tax_system.php\n";
echo "🔧 To configure settings, use the admin panel in Telegram\n\n";
?>
