<?php
date_default_timezone_set('Asia/Kolkata');
// ========================================
// DEBUGGING: Force error logging to a file
// ========================================
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/data/debug.log');
error_reporting(E_ALL); // Ensure all errors are reported
ini_set('display_errors', 0); // Turn off displaying errors to browser/output, log them instead
// ========================================

// ========================================
// TELEGRAM REFERRAL BOT CONFIGURATION
// ========================================
// Update these settings for your bot

// ========================================
// STORAGE CONFIGURATION
// ========================================
define('STORAGE_MODE', 'json'); // 'json' or 'mysql'

// ========================================
// DATABASE CONFIGURATION (MySQL mode only)
// ========================================
define('DB_HOST', 'srv1234.hstgr.io');
define('DB_NAME', 'u342933442_referbot');
define('DB_USER', 'u342933442_referbot');
define('DB_PASS', '@m]1IZ1Pk');

// ========================================
// BOT CONFIGURATION
// ========================================
define('BOT_TOKEN', '8152909781:AAGR8ik9Bf7lFrY-gUrahHGpR4nRHU2iEpM');
define('BOT_USERNAME', 'InstantoPayBot'); // Without @ symbol

// Admin Configuration - Multiple admins supported
define('ADMIN_ID', 2027123358); // Primary admin (for backward compatibility)
define('ADMIN_IDS', [1363710641, 2027123358, 8153676253]); // All admin IDs

// Channel Configuration
define('MAIN_CHANNEL', 'InstantoPay'); // Without @ symbol
define('PRIVATE_LOGS_CHANNEL', '@YourPrivateChannel'); // With @ or channel ID

// Bot Settings
define('MAINTENANCE_MODE', false); // true = On, false = Off
define('PER_REFER_AMOUNT', 50); // Maximum amount per referral (₹20-₹50 range)
define('JOINING_BONUS_AMOUNT', 50); // Maximum joining bonus (₹20-₹50 range)

// User-facing display amounts (what users see vs actual configured ranges)
define('USER_DISPLAY_REFERRAL_MAX', '100'); // Generic max shown to users for referrals
define('USER_DISPLAY_BONUS_MAX', '100'); // Generic max shown to users for joining bonus

// OTP Configuration
define('OTP_API_KEY', 'fb508ef074ee78a0e58c68be06d8a2eb'); // From renflair.in

// Gift/Bonus Configuration
define('GIFT_CHANNEL', 'YourGiftChannel'); // Without @ symbol
define('GIFT_AMOUNT', 25); // Gift bonus amount

// Bot Messages Configuration
define('WELCOME_MESSAGE_TEMPLATE', '🎁 Make Money Easily! Get upto ₹100!

🔺 <a href="https://t.me/{channel}">Click & Join Our Channel</a>

🔷 Must Join Our Channels Before Clicking On [💰GETS dfsdafw53232 MONEY💰]');

define('INVITATION_MESSAGE_TEMPLATE', '🎉 Invite your friends to get money!

Per Invite You Get Upto ₹100

🔗your invitation link(👇️Click to copy)

<code>✨Join me and get upto ₹100
{referral_link}</code>');

// Withdrawal Configuration
define('MIN_WITHDRAWAL_AMOUNT', 100); // Minimum withdrawal amount
define('WITHDRAWAL_AMOUNTS', [100, 200, 400, 600, 800, 1000]); // Available withdrawal amounts

// Security Configuration
define('RATE_LIMIT_ENABLED', true); // Enable rate limiting
define('MAX_REQUESTS_PER_MINUTE', 30); // Max requests per user per minute

// JSON Storage Configuration (only used when STORAGE_MODE = 'json')
define('DATA_DIR', __DIR__ . '/data/');
define('USERS_FILE', DATA_DIR . 'users.json');
define('ADMIN_FILE', DATA_DIR . 'admin_settings.json');
define('SESSIONS_FILE', DATA_DIR . 'user_sessions.json');
define('BOT_INFO_FILE', DATA_DIR . 'bot_info.json');

// API URLs
define('TELEGRAM_API_URL', 'https://api.telegram.org/bot' . BOT_TOKEN . '/');
define('OTP_API_URL', 'https://sms.renflair.in/V1.php');

// Error reporting (disable in production)
// error_reporting(E_ALL); // Already set above for debugging
// ini_set('display_errors', 1); // Disabled for debugging to prefer file logging

// All functions are now in core_functions.php

// ========================================
// LOAD CORE FUNCTIONS
// ========================================
require_once __DIR__ . '/core_functions.php';
?>
