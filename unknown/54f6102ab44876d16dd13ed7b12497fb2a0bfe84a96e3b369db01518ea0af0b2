<?php
require_once 'config.php';
require_once 'database_functions.php';

// User account setting handlers
function handleSetAccountField($userId, $chatId, $field) {
    // Check maintenance and ban status
    $adminSettings = getAdminSettings();
    if (isMaintenanceMode()) {
        sendMessage($chatId, MAINTENANCE_TEXT, null, 'HTML');
        return;
    }

    $user = getUser($userId);
    if ($user['banned']) {
        sendMessage($chatId, BANNED_TEXT, null, 'HTML');
        return;
    }

    switch ($field) {
        case 'Name':
            sendMessage($chatId, "👤Please enter your Name.\n⚠️Name must match the name on your bank account.\n👇️Enter and send in the input box below.\n\nSend /cancel for cancelling the process.");
            setUserSession($userId, 'set_account_name');
            break;
        case 'IFSC':
            sendMessage($chatId, "ℹ️Please enter your IFSC code.\n💡IFSC is an 11-character code unique to each bank branch, which can be found on your bank statement.\n👇️Enter and send in the input box below.\n\nSend /cancel to cancel the process.");
            setUserSession($userId, 'set_account_ifsc');
            break;
        case 'Email':
            sendMessage($chatId, "📧Please enter your Email.\n👇️Enter and send in the input box below.\n\nSend /cancel to cancel the process.");
            setUserSession($userId, 'set_account_email');
            break;
        case 'AccountNumber':
            sendMessage($chatId, "💳️Account Number: Enter your IMPS ID.\n💡You can find your IMPS transaction ID on your bank statement.\n👇️Enter and send in the input box below.\n\nSend /cancel to cancel the process.");
            setUserSession($userId, 'set_account_number');
            break;
        case 'MobileNumber':
            sendMessage($chatId, "📱Please enter your Mobile Number.\nWe will send you OTP code to verify.\n\nSend /cancel to cancel the process.");
            setUserSession($userId, 'set_mobile_number');
            break;
        case 'USDTAddress':
            sendMessage($chatId, "₿ Please enter your Binance ID.\n\n💡 Your Binance ID is your unique identifier on Binance (e.g., your email or phone number used for Binance account).\n⚠️ Make sure the Binance ID is correct as incorrect IDs may result in failed transfers!\n\nSend /cancel to cancel the process.");
            setUserSession($userId, 'set_binance_id');
            break;
    }
}

function handleSetAccountNameStep2($userId, $chatId, $text) {
    if (updateAccountInfo($userId, 'name', $text)) {
        showAccountInfoMenu($userId, $chatId);
    } else {
        sendMessage($chatId, "Error updating name.");
    }
    clearUserSession($userId);
}

function handleSetAccountIFSCStep2($userId, $chatId, $text) {
    if (updateAccountInfo($userId, 'ifsc', $text)) {
        showAccountInfoMenu($userId, $chatId);
    } else {
        sendMessage($chatId, "Error updating IFSC.");
    }
    clearUserSession($userId);
}

function handleSetAccountEmailStep2($userId, $chatId, $text) {
    if (updateAccountInfo($userId, 'email', $text)) {
        showAccountInfoMenu($userId, $chatId);
    } else {
        sendMessage($chatId, "Error updating email.");
    }
    clearUserSession($userId);
}

function handleSetAccountNumberStep2($userId, $chatId, $text) {
    if (!is_numeric($text)) {
        sendMessage($chatId, "<i>❌Invalid IMPS ID. Please enter a valid IMPS ID.</i>", null, 'HTML');
        clearUserSession($userId);
        return;
    }

    if (updateAccountInfo($userId, 'account_number', $text)) {
        showAccountInfoMenu($userId, $chatId);
    } else {
        sendMessage($chatId, "Error updating account number.");
    }
    clearUserSession($userId);
}

function handleSetMobileNumberStep2($userId, $chatId, $text) {
    if (!is_numeric($text)) {
        sendMessage($chatId, "<i>❌Invalid Mobile Number. Please enter a valid Mobile Number.</i>", null, 'HTML');
        clearUserSession($userId);
        return;
    }

    // Generate OTP
    $otp = generateOTP();
    $otpApiKey = getBotConfig('otp_api_key');

    if (empty($otpApiKey)) {
        sendMessage($chatId, "OTP service not configured. Please contact admin.");
        clearUserSession($userId);
        return;
    }

    // Send OTP
    $otpResponse = sendOTP($text, $otp, $otpApiKey);

    sendMessage($chatId, "👇️Enter the verification code we sent to.\n💡The code is valid for five minutes.\n\nSend /cancel to cancel the process.");

    setUserSession($userId, 'verify_otp', [
        'mobile_number' => $text,
        'otp' => $otp,
        'otp_timestamp' => time() // Store OTP generation time
    ]);
}

function handleVerifyOTPStep2($userId, $chatId, $text, $data) {
    if (!is_numeric($text)) {
        sendMessage($chatId, "<i>❌Invalid OTP. Please enter a valid OTP by clicking on the button again.</i>", null, 'HTML');
        clearUserSession($userId);
        return;
    }

    // Check OTP expiry (5 minutes = 300 seconds)
    $otpTimestamp = $data['otp_timestamp'] ?? 0;
    if (time() - $otpTimestamp > 300) {
        sendMessage($chatId, "❌OTP has expired. Please request a new one.", null, 'HTML');
        clearUserSession($userId); // Clear session to force new OTP request
        return;
    }

    if ($text == $data['otp']) {
        if (updateAccountInfo($userId, 'mobile_number', $data['mobile_number'])) {
            showAccountInfoMenu($userId, $chatId);
        } else {
            sendMessage($chatId, "Error updating mobile number.");
        }
    } else {
        sendMessage($chatId, "❌Invalid OTP. Please enter a valid OTP by clicking on the button again.", null, 'HTML');
    }

    clearUserSession($userId);
}

function handleSetBinanceIdStep2($userId, $chatId, $text) {
    // Basic validation for Binance ID
    $text = trim($text);

    if (empty($text)) {
        sendMessage($chatId, "<i>❌ Please enter a Binance ID. Cannot be empty.</i>", null, 'HTML');
        clearUserSession($userId);
        return;
    }

    if (strlen($text) > 100) {
        sendMessage($chatId, "<i>❌ Binance ID is too long. Please enter a valid ID.</i>", null, 'HTML');
        clearUserSession($userId);
        return;
    }

    // Basic format validation for common Binance ID formats
    if (!isValidBinanceId($text)) {
        sendMessage($chatId, "<i>❌ Invalid Binance ID format. Please enter a valid email address or phone number used for your Binance account.</i>", null, 'HTML');
        clearUserSession($userId);
        return;
    }

    if (updateAccountInfo($userId, 'binance_id', $text)) {
        sendMessage($chatId, "✅ Binance ID updated successfully!");
        // Show updated USDT setup
        $user = getUser($userId);
        $usdtMessage = "₿ <b>USDT (Binance ID) Setup</b>\n\n";
        $usdtMessage .= "⚙️ Your Binance ID has been updated.\n\n";
        $usdtMessage .= "✅ <b>Current Method:</b> USDT (Binance ID)\n\n";
        $usdtMessage .= "<b>Binance ID:</b> {$user['binance_id']}\n\n";
        $usdtMessage .= "💡 You can now make withdrawals to this Binance account.";

        $usdtKeyboard = [
            'inline_keyboard' => [
                [
                    ['text' => '₿ Update Binance ID', 'callback_data' => 'set USDTAddress']
                ],
                [
                    ['text' => '🔄 Change Method', 'callback_data' => 'setAccountInfo']
                ],
                [
                    ['text' => '↩️ Back', 'callback_data' => 'myWallet']
                ]
            ]
        ];

        sendMessage($chatId, $usdtMessage, $usdtKeyboard, 'HTML');
    } else {
        sendMessage($chatId, "❌ Error updating Binance ID. Please try again.");
    }
    clearUserSession($userId);
}

// Keep the old function name for backward compatibility but redirect to new function
function handleSetUSDTAddressStep2($userId, $chatId, $text) {
    handleSetBinanceIdStep2($userId, $chatId, $text);
}

// Validate Binance ID format
function isValidBinanceId($binanceId) {
    $binanceId = trim($binanceId);

    // Check if it's a valid email format
    if (filter_var($binanceId, FILTER_VALIDATE_EMAIL)) {
        return true;
    }

    // Check if it's a valid phone number format (basic validation)
    // Allow formats like: +**********, **********, +91-**********, etc.
    if (preg_match('/^[\+]?[0-9\-\s\(\)]{7,20}$/', $binanceId)) {
        return true;
    }

    // Check if it's a Binance UID (numeric, typically 8-12 digits)
    if (preg_match('/^[0-9]{8,12}$/', $binanceId)) {
        return true;
    }

    // Allow alphanumeric IDs (some Binance IDs might be alphanumeric)
    if (preg_match('/^[a-zA-Z0-9@._-]{3,50}$/', $binanceId)) {
        return true;
    }

    return false;
}

function showAccountInfoMenu($userId, $chatId) {
    $user = getUser($userId);
    $withdrawalMethod = $user['withdrawal_method'] ?? 'bank';

    if ($withdrawalMethod === 'usdt') {
        // Show USDT setup
        $usdtMessage = "₿ <b>USDT (BEP-20) Setup</b>\n\n";
        $usdtMessage .= "⚙️ Please set your USDT BEP-20 wallet address for withdrawals.\n";
        $usdtMessage .= "⚠️ Make sure to provide a valid BEP-20 address!\n\n";
        $usdtMessage .= "✅ <b>Current Method:</b> USDT (BEP-20)\n\n";
        $usdtMessage .= "<b>USDT Address:</b> " . ($user['usdt_address'] ? $user['usdt_address'] : '<i>Not set</i>') . "\n\n";
        $usdtMessage .= "💡 <b>Note:</b> BEP-20 addresses start with '0x' and are 42 characters long.";

        $usdtKeyboard = [
            'inline_keyboard' => [
                [
                    ['text' => '₿ Set USDT Address', 'callback_data' => 'set USDTAddress']
                ],
                [
                    ['text' => '🔄 Change Method', 'callback_data' => 'setAccountInfo']
                ],
                [
                    ['text' => '↩️ Back', 'callback_data' => 'myWallet']
                ]
            ]
        ];

        sendMessage($chatId, $usdtMessage, $usdtKeyboard, 'HTML');
    } else {
        // Show bank account setup
        $accountMessage = "🏦 <b>Bank Account Setup</b>\n\n";
        $accountMessage .= "⚙️ Please set your bank account details for withdrawals.\n";
        $accountMessage .= "⚠️ Any incorrect information may result in failed withdrawal!\n\n";
        $accountMessage .= "✅ <b>Current Method:</b> Bank Account\n\n";
        $accountMessage .= "<b>Name:</b> {$user['name']}\n";
        $accountMessage .= "<b>IFSC:</b> {$user['ifsc']}\n";
        $accountMessage .= "<b>Email:</b> {$user['email']}\n";
        $accountMessage .= "<b>Account Number:</b> {$user['account_number']}\n";
        $accountMessage .= "<b>Mobile Number:</b> {$user['mobile_number']}";

        $accountKeyboard = [
            'inline_keyboard' => [
                [
                    ['text' => '👤Name', 'callback_data' => 'set Name'],
                    ['text' => 'ℹ️IFSC', 'callback_data' => 'set IFSC'],
                    ['text' => '📧Email', 'callback_data' => 'set Email']
                ],
                [
                    ['text' => '💳Account Number', 'callback_data' => 'set AccountNumber']
                ],
                [
                    ['text' => '📱Mobile Number', 'callback_data' => 'set MobileNumber']
                ],
                [
                    ['text' => '🔄 Change Method', 'callback_data' => 'setAccountInfo']
                ],
                [
                    ['text' => '↩️ Back', 'callback_data' => 'myWallet']
                ]
            ]
        ];

        sendMessage($chatId, $accountMessage, $accountKeyboard, 'HTML');
    }
}

// Gift claim handler
function handleClaimBonus($userId, $chatId) {
    // Check maintenance and ban status
    if (isMaintenanceMode()) {
        sendMessage($chatId, MAINTENANCE_TEXT, null, 'HTML');
        return;
    }

    $user = getUser($userId);
    if ($user['banned']) {
        sendMessage($chatId, BANNED_TEXT, null, 'HTML');
        return;
    }

    // Get current gift broadcast data
    $giftBroadcast = getCurrentGiftBroadcast();

    if (!$giftBroadcast) {
        sendMessage($chatId, "❌ No active gift broadcast found. Please wait for the admin to create a new gift campaign.");
        error_log("[GiftClaimDebug] User {$userId} attempted claim but no active gift broadcast found.");
        return;
    }

    $giftChannel = $giftBroadcast['channel'];
    $giftAmount = $giftBroadcast['amount'];
    $channelType = $giftBroadcast['channel_type'] ?? 'public';
    $channelId = $giftBroadcast['channel_id'] ?? $giftChannel;
    $inviteLink = $giftBroadcast['invite_link'] ?? null;
    $channelTitle = $giftBroadcast['channel_title'] ?? $giftChannel;

    // Log the gift channel being used
    error_log("[GiftClaimDebug] User {$userId} attempting claim. Channel type: '{$channelType}', channel: '{$giftChannel}', amount: {$giftAmount}");

    if ($user['gift_claimed']) {
        sendMessage($chatId, "❌ You have already claimed your bonus for this campaign.");
        return;
    }

    // Check if user joined the gift channel (use channel ID for private channels)
    $verificationId = ($channelType === 'private') ? $channelId : $giftChannel;

    // For private channels, ensure we have a valid channel ID
    if ($channelType === 'private') {
        if (!$channelId || !preg_match('/^-100\d{10,}$/', $channelId)) {
            error_log("[GiftClaimDebug] Invalid or missing channel ID for private channel: {$channelId}");
            sendMessage($chatId, "❌ <b>Configuration Error</b>\n\nPrivate channel verification failed. Please contact admin to reconfigure the gift broadcast with a valid channel ID.", null, 'HTML');
            return;
        }
        error_log("[GiftClaimDebug] Using channel ID for private channel verification: {$channelId}");
    }

    $chatMember = getChatMember($verificationId, $userId);

    // Log initial chat member status
    error_log("[GiftClaimDebug] User {$userId} initial getChatMember response for '{$verificationId}' (type: {$channelType}): " . json_encode($chatMember));
    $isMember = $chatMember && in_array($chatMember['status'], ['member', 'administrator', 'creator']);

    if (!$isMember) {
        error_log("[GiftClaimDebug] User {$userId} initial check failed for '{$verificationId}'. Waiting 3s for API propagation.");
        // Wait for 3 seconds and try again, in case of API propagation delay
        sleep(3);
        $chatMember = getChatMember($verificationId, $userId);
        // Log second chat member status
        error_log("[GiftClaimDebug] User {$userId} second getChatMember response for '{$verificationId}' after 3s delay: " . json_encode($chatMember));
        $isMember = $chatMember && in_array($chatMember['status'], ['member', 'administrator', 'creator']);

        if (!$isMember) {
            error_log("[GiftClaimDebug] User {$userId} second check also failed for '{$verificationId}'. Sending join message.");

            // Generate appropriate join message based on channel type
            if ($channelType === 'private') {
                $joinMessage = "💡 <b>You Must Join The Private Channel First!</b>\n\n🔗 <a href=\"{$inviteLink}\">👉 Click here to join {$channelTitle}</a>\n\nAfter joining, please wait a moment and try claiming again.";
            } else {
                $joinMessage = "💡 <b>You Must Join The Channel First!</b>\n\n🔗 <a href=\"https://t.me/{$giftChannel}\">👉 Click here to join @{$giftChannel}</a>\n\nAfter joining, please wait a moment and try claiming again.";
            }

            sendMessage($chatId, $joinMessage, null, 'HTML', true);
            return;
        }
    }

    // Give gift
    setGiftClaimed($userId, true);
    updateUserBalance($userId, $giftAmount, 'add');

    error_log("[GiftClaimDebug] User {$userId} successfully claimed gift of ₹{$giftAmount} from {$channelType} channel '{$giftChannel}'.");

    // Create wallet button for immediate access to updated balance
    $walletKeyboard = [
        'inline_keyboard' => [
            [
                ['text' => '💰 My Wallet', 'callback_data' => 'myWallet']
            ]
        ]
    ];

    sendMessage($chatId, "🎉 <b>Congratulations!</b>\n\n🎁 You have successfully claimed your bonus of <b>₹{$giftAmount}</b>!\n\n💰 The amount has been added to your wallet.", $walletKeyboard, 'HTML');
}

// Broadcast handlers for admin
function handleBroadcastGiftButton($userId, $chatId) {
    if (!isAdmin($userId)) return;

    $message = "🎁 <b>Enhanced Gift Broadcast Setup</b>\n\n";
    $message .= "This system supports both <b>public</b> and <b>private</b> channels!\n\n";
    $message .= "📢 <b>For Public Channels:</b>\n";
    $message .= "• Enter channel username (without @)\n";
    $message .= "• Example: <code>mychannel</code>\n\n";
    $message .= "🔒 <b>For Private Channels:</b>\n";
    $message .= "• Enter the numeric channel ID\n";
    $message .= "• Example: <code>-100**********</code>\n";
    $message .= "• Must start with <code>-100</code>\n";
    $message .= "• You'll be asked for invite link in next step\n\n";
    $message .= "❓ <b>How to get Channel ID:</b>\n";
    $message .= "1. Forward any message from your channel to @userinfobot\n";
    $message .= "2. Or add @userinfobot to your channel temporarily\n";
    $message .= "3. Copy the channel ID (starts with -100)\n\n";
    $message .= "⚠️ <b>Requirements:</b>\n";
    $message .= "✅ The channel must exist and be accessible\n";
    $message .= "✅ The bot must have admin permissions\n";
    $message .= "✅ You must have admin access to the channel\n\n";
    $message .= "Please enter the channel username (public) or channel ID (private):";

    sendMessage($chatId, $message, null, 'HTML');
    setUserSession($userId, 'broadcast_gift_channel');
}

function handleBroadcastGiftStep2($userId, $chatId, $text) {
    if (!isAdmin($userId)) return;

    // Detect channel type
    $channelType = detectChannelType($text);

    if ($channelType === 'unknown') {
        sendMessage($chatId, "❌ <b>Invalid Input</b>\n\nPlease enter either:\n• Channel username (without @): <code>mychannel</code>\n• Private channel ID (starts with -100): <code>-100**********</code>\n\n❓ <b>How to get Channel ID:</b>\nForward any message from your private channel to @userinfobot", null, 'HTML');
        clearUserSession($userId);
        return;
    }

    if ($channelType === 'private_invite_link') {
        sendMessage($chatId, "🔒 <b>Private Channel Detected</b>\n\n❌ <b>Invite links are not supported for verification.</b>\n\nFor private channels, please provide the <b>numeric channel ID</b> instead.\n\n❓ <b>How to get your Channel ID:</b>\n1. Forward any message from your channel to @userinfobot\n2. Or add @userinfobot to your channel temporarily\n3. Copy the channel ID (starts with -100)\n\n📝 <b>Example:</b> <code>-100**********</code>\n\nPlease enter your channel ID:", null, 'HTML');
        clearUserSession($userId);
        return;
    }

    // Verify channel access based on type
    if ($channelType === 'public') {
        if (strpos($text, '@') === 0) {
            sendMessage($chatId, "❌ Invalid channel username. Please enter without '@' symbol.");
            clearUserSession($userId);
            return;
        }

        $verification = verifyChannelAccess($text);
        $channelData = [
            'type' => 'public',
            'username' => $text,
            'id' => '@' . $text,
            'invite_link' => null
        ];
    } else {
        // Private channel - use channel ID verification
        $verification = verifyPrivateChannelById($text);
        $channelData = [
            'type' => 'private',
            'username' => null,
            'id' => $text, // The channel ID itself
            'invite_link' => null, // Will be set later if needed
            'channel_id' => $text
        ];
    }

    if (!$verification['success']) {
        $errorMessage = "❌ <b>Channel Verification Failed</b>\n\n" . $verification['error'];

        // Add specific guidance for private channels
        if ($channelType === 'private') {
            $errorMessage .= "\n\n<b>📋 Private Channel Setup Guide:</b>\n";
            $errorMessage .= "1. Add the bot to your private channel\n";
            $errorMessage .= "2. Make the bot an administrator\n";
            $errorMessage .= "3. Grant these permissions:\n";
            $errorMessage .= "   • Delete messages\n";
            $errorMessage .= "   • Ban users\n";
            $errorMessage .= "   • Invite users\n";
            $errorMessage .= "   • Pin messages\n";
            $errorMessage .= "4. Verify the channel ID is correct\n";
            $errorMessage .= "5. Try again with the same channel ID\n\n";
            $errorMessage .= "❓ <b>How to get Channel ID:</b>\n";
            $errorMessage .= "Forward any message from your channel to @userinfobot";
        }

        $errorMessage .= "\n\nPlease fix the issue and try again.";
        sendMessage($chatId, $errorMessage, null, 'HTML');
        clearUserSession($userId);
        return;
    }

    $channelInfo = $verification['channel_info'];
    $channelTitle = $channelInfo['title'] ?? ($channelType === 'public' ? $text : 'Private Channel');
    $channelData['title'] = $channelTitle;

    $message = "✅ <b>Channel Verified Successfully!</b>\n\n";

    if ($channelType === 'public') {
        $message .= "📢 <b>Type:</b> Public Channel\n";
        $message .= "📢 <b>Channel:</b> @{$text}\n";
        $message .= "🤖 <b>Bot Status:</b> Administrator ✅\n";
    } else {
        $message .= "🔒 <b>Type:</b> Private Channel\n";
        $message .= "🆔 <b>Channel ID:</b> <code>{$text}</code>\n";
        $message .= "🤖 <b>Bot Status:</b> Administrator ✅\n";

        if (isset($verification['bot_status'])) {
            $message .= "👑 <b>Admin Level:</b> {$verification['bot_status']}\n";
        }

        if (isset($verification['verification_method'])) {
            $message .= "✅ <b>Verified via:</b> {$verification['verification_method']}\n";
        }
    }

    $message .= "📝 <b>Title:</b> {$channelTitle}\n\n";

    // For private channels, ask for invite link next
    if ($channelType === 'private') {
        $message .= "Now please provide the <b>invite link</b> for users to join this private channel:\n\n";
        $message .= "📝 <b>Example:</b> <code>https://t.me/+AbCdEfGhIjKlMnOp</code>\n\n";
        $message .= "This link will be used for users to join the channel when claiming gifts.";

        sendMessage($chatId, $message, null, 'HTML');
        setUserSession($userId, 'broadcast_gift_invite_link', [
            'gift_channel' => $text, // Channel ID
            'channel_title' => $channelTitle,
            'channel_data' => $channelData
        ]);
    } else {
        $message .= "Now please enter the gift amount (in ₹):";

        sendMessage($chatId, $message, null, 'HTML');
        setUserSession($userId, 'broadcast_gift_amount', [
            'gift_channel' => $text,
            'channel_title' => $channelTitle,
            'channel_data' => $channelData
        ]);
    }
}

function handleBroadcastGiftInviteLinkStep($userId, $chatId, $text, $data) {
    if (!isAdmin($userId)) return;

    // Validate invite link format
    if (!preg_match('/(?:https?:\/\/)?(?:www\.)?(?:t\.me\/|telegram\.me\/)\+([a-zA-Z0-9_-]+)/', $text)) {
        sendMessage($chatId, "❌ <b>Invalid Invite Link Format</b>\n\nPlease provide a valid Telegram invite link.\n\n📝 <b>Example:</b> <code>https://t.me/+AbCdEfGhIjKlMnOp</code>\n\nTry again:", null, 'HTML');
        return;
    }

    // Update channel data with invite link
    $channelData = $data['channel_data'];
    $channelData['invite_link'] = $text;

    $message = "✅ <b>Invite Link Added Successfully!</b>\n\n";
    $message .= "🔗 <b>Invite Link:</b> {$text}\n\n";
    $message .= "Now please enter the gift amount (in ₹):";

    sendMessage($chatId, $message, null, 'HTML');
    setUserSession($userId, 'broadcast_gift_amount', [
        'gift_channel' => $data['gift_channel'],
        'channel_title' => $data['channel_title'],
        'channel_data' => $channelData
    ]);
}

function handleBroadcastGiftStep3($userId, $chatId, $text, $data) {
    if (!isAdmin($userId)) return;

    if (!isValidAmount($text)) {
        sendMessage($chatId, "❌ Invalid amount. Please enter a valid number.");
        clearUserSession($userId);
        return;
    }

    $giftChannel = $data['gift_channel'];
    $channelTitle = $data['channel_title'] ?? $giftChannel;
    $channelData = $data['channel_data'] ?? [];
    $giftAmount = (float)$text;

    // Store the current gift broadcast data with enhanced channel information
    if (!setCurrentGiftBroadcast($giftChannel, $giftAmount, $channelData)) {
        sendMessage($chatId, "❌ Error storing gift broadcast data. Please try again.");
        clearUserSession($userId);
        return;
    }

    // Reset all users' gift claimed status for the new broadcast
    $allUsers = getAllUsers();

    // Validate we have users to broadcast to
    if (empty($allUsers)) {
        error_log("[GiftBroadcastError] No users found to broadcast to.");
        sendMessage($chatId, "⚠️ No users found to send the broadcast to.");
        clearCurrentGiftBroadcast();
        clearUserSession($userId);
        return;
    }

    foreach ($allUsers as $targetUserId) {
        setGiftClaimed($targetUserId, false);
    }

    $confirmMessage = "🎁 <b>Gift Broadcast Ready!</b>\n\n";

    if ($channelData['type'] === 'private') {
        $confirmMessage .= "🔒 <b>Type:</b> Private Channel\n";
        $confirmMessage .= "🔗 <b>Invite Link:</b> {$channelData['invite_link']}\n";
    } else {
        $confirmMessage .= "📢 <b>Type:</b> Public Channel\n";
        $confirmMessage .= "📢 <b>Channel:</b> @{$channelData['username']}\n";
    }

    $confirmMessage .= "📝 <b>Title:</b> {$channelTitle}\n";
    $confirmMessage .= "💰 <b>Amount:</b> ₹{$giftAmount}\n";
    $confirmMessage .= "👥 <b>Target Users:</b> " . count($allUsers) . "\n\n";
    $confirmMessage .= "🚀 Starting broadcast now...";

    sendMessage($chatId, $confirmMessage, null, 'HTML');
    error_log("[GiftBroadcastStart] Admin {$userId} initiated gift broadcast for channel '{$giftChannel}' with amount {$giftAmount} to " . count($allUsers) . " users.");

    // Use enhanced broadcast system for gift broadcasts
    $broadcastResult = broadcastGiftMessage($giftChannel, $giftAmount, $channelData, $allUsers, $userId);

    // Send completion feedback to admin
    $resultMessage = "✅ <b>Gift Broadcast Complete!</b>\n\n";
    $resultMessage .= "📊 <b>Statistics:</b>\n";
    $resultMessage .= "✅ <b>Sent Successfully:</b> {$broadcastResult['success_count']}\n";
    $resultMessage .= "❌ <b>Failed:</b> {$broadcastResult['failed_count']}\n";
    $resultMessage .= "🚫 <b>Blocked Users:</b> {$broadcastResult['blocked_count']}\n";
    $resultMessage .= "👥 <b>Total Users:</b> {$broadcastResult['total_users']}\n";
    $resultMessage .= "⏱️ <b>Duration:</b> {$broadcastResult['duration']}s\n";
    $resultMessage .= "📈 <b>Success Rate:</b> {$broadcastResult['success_rate']}%\n\n";

    if ($channelData['type'] === 'private') {
        $resultMessage .= "🎁 Gift broadcast is now active. Users can claim bonuses after joining the private channel via the invite link.";
    } else {
        $resultMessage .= "🎁 Gift broadcast is now active. Users can claim bonuses after joining @{$channelData['username']}.";
    }

    if (!empty($broadcastResult['errors'])) {
        $resultMessage .= "\n\n⚠️ <b>Sample Errors:</b>\n";
        $errorSample = array_slice($broadcastResult['errors'], 0, 3);
        foreach ($errorSample as $error) {
            $resultMessage .= "• " . htmlspecialchars($error) . "\n";
        }
        if (count($broadcastResult['errors']) > 3) {
            $resultMessage .= "• ... and " . (count($broadcastResult['errors']) - 3) . " more\n";
        }
    }

    sendMessage($chatId, $resultMessage, null, 'HTML');
    clearUserSession($userId);
}

function handleBroadcastText($userId, $chatId) {
    if (!isAdmin($userId)) return;

    $message = "📢 <b>Enhanced Broadcast System</b>\n\n";
    $message .= "Send the content you want to broadcast to all users:\n\n";
    $message .= "📝 <b>Text Message:</b> Send plain text\n";
    $message .= "🖼️ <b>Photo:</b> Send photo with or without caption\n";
    $message .= "🎥 <b>Video:</b> Send video with or without caption\n";
    $message .= "📄 <b>Document:</b> Send document with or without caption\n";
    $message .= "🎵 <b>Audio:</b> Send audio file with or without caption\n\n";
    $message .= "💡 <b>Note:</b> The exact content you send will be broadcasted to all users.\n\n";
    $message .= "Send /cancel to cancel the broadcast.";

    sendMessage($chatId, $message, null, 'HTML');
    setUserSession($userId, 'broadcast_message');
}

// Missing function that was referenced in webhook.php
function handleBroadcastTextStep2($userId, $chatId, $text) {
    if (!isAdmin($userId)) return;

    // Check if admin already has an active broadcast
    $activeBroadcast = getActiveBroadcast($userId);
    if ($activeBroadcast) {
        sendMessage($chatId, "⚠️ <b>Broadcast Already Running</b>\n\nYou have an active broadcast (ID: <code>" . substr($activeBroadcast, -8) . "</code>).\n\nPlease wait for it to complete or send /cancel to stop it.", null, 'HTML');
        clearUserSession($userId);
        return;
    }

    // This is for simple text broadcasts
    $message = [
        'text' => $text
    ];

    // Use the enhanced broadcast system
    $broadcastResult = broadcastMessage($message, $userId);

    // Send feedback to admin
    $feedbackMessage = "📢 <b>Text Broadcast Completed!</b>\n\n";
    $feedbackMessage .= "📊 <b>Statistics:</b>\n";
    $feedbackMessage .= "✅ <b>Sent Successfully:</b> {$broadcastResult['success_count']}\n";
    $feedbackMessage .= "❌ <b>Failed:</b> {$broadcastResult['failed_count']}\n";
    $feedbackMessage .= "🚫 <b>Blocked Users:</b> {$broadcastResult['blocked_count']}\n";
    $feedbackMessage .= "👥 <b>Total Users:</b> {$broadcastResult['total_users']}\n\n";

    if (!empty($broadcastResult['errors'])) {
        $feedbackMessage .= "⚠️ <b>Sample Errors:</b>\n";
        $errorSample = array_slice($broadcastResult['errors'], 0, 3);
        foreach ($errorSample as $error) {
            $feedbackMessage .= "• " . htmlspecialchars($error) . "\n";
        }
        if (count($broadcastResult['errors']) > 3) {
            $feedbackMessage .= "• ... and " . (count($broadcastResult['errors']) - 3) . " more\n";
        }
    }

    sendMessage($chatId, $feedbackMessage, null, 'HTML');
    clearUserSession($userId);
}

// Enhanced broadcast message handler
function handleBroadcastMessageStep2($userId, $chatId, $message) {
    if (!isAdmin($userId)) return;

    // Check if admin already has an active broadcast
    $activeBroadcast = getActiveBroadcast($userId);
    if ($activeBroadcast) {
        sendMessage($chatId, "⚠️ <b>Broadcast Already Running</b>\n\nYou have an active broadcast (ID: <code>" . substr($activeBroadcast, -8) . "</code>).\n\nPlease wait for it to complete or send /cancel to stop it.", null, 'HTML');
        clearUserSession($userId);
        return;
    }

    // Detect message type and broadcast accordingly
    $broadcastResult = broadcastMessage($message, $userId);

    // Send feedback to admin
    $feedbackMessage = "📢 <b>Broadcast Completed!</b>\n\n";
    $feedbackMessage .= "📊 <b>Statistics:</b>\n";
    $feedbackMessage .= "✅ <b>Sent Successfully:</b> {$broadcastResult['success_count']}\n";
    $feedbackMessage .= "❌ <b>Failed:</b> {$broadcastResult['failed_count']}\n";
    $feedbackMessage .= "🚫 <b>Blocked Users:</b> {$broadcastResult['blocked_count']}\n";
    $feedbackMessage .= "👥 <b>Total Users:</b> {$broadcastResult['total_users']}\n\n";

    if (!empty($broadcastResult['errors'])) {
        $feedbackMessage .= "⚠️ <b>Errors:</b>\n";
        foreach (array_slice($broadcastResult['errors'], 0, 5) as $error) {
            $feedbackMessage .= "• {$error}\n";
        }
        if (count($broadcastResult['errors']) > 5) {
            $feedbackMessage .= "• ... and " . (count($broadcastResult['errors']) - 5) . " more errors\n";
        }
    }

    sendMessage($chatId, $feedbackMessage, null, 'HTML');
    clearUserSession($userId);
}
?>
