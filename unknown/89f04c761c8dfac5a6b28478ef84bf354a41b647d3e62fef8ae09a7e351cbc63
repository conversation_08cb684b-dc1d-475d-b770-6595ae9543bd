<?php
require_once 'config.php';
require_once 'database_functions.php';

// Admin command handlers

/**
 * Handle /rank command - Display withdrawal statistics for top referrers
 */
function handleRankCommand($userId, $chatId) {
    // Check if user is admin
    if (!isAdmin($userId)) {
        sendMessage($chatId, "❌ <b>Access Denied</b>\n\nThis command is only available to administrators.", null, 'HTML');
        return;
    }

    try {
        // Get top users by withdrawal amount
        $topUsers = getTopUsersByWithdrawals(15);

        if (empty($topUsers)) {
            sendMessage($chatId, "📊 <b>Withdrawal Rankings</b>\n\n❌ No withdrawal data available yet.\n\nUsers need to make successful withdrawals to appear in rankings.", null, 'HTML');
            return;
        }

        // Format the ranking message
        $message = "🏆 <b>TOP WITHDRAWAL RANKINGS</b>\n";
        $message .= "📊 <i>Top 15 Users by Total Successful Withdrawals</i>\n\n";

        $rank = 1;
        foreach ($topUsers as $user) {
            // Get rank emoji
            $rankEmoji = getRankEmoji($rank);

            // Format user display name (handle empty names)
            $displayName = !empty($user['first_name']) ? htmlspecialchars($user['first_name']) : 'Unknown User';
            $username = !empty($user['username']) ? '@' . htmlspecialchars($user['username']) : 'No username';

            // Format withdrawal count
            $withdrawalCount = $user['withdrawal_count'] ?? 0;
            $withdrawalText = $withdrawalCount == 1 ? 'withdrawal' : 'withdrawals';

            // Add banned indicator if user is banned
            $statusIndicator = ($user['banned'] ?? false) ? ' 🚫' : '';

            // Format withdrawal amount
            $withdrawalAmount = number_format($user['successful_withdraw'], 2);

            // Add user to ranking
            $message .= "{$rankEmoji} <b>#{$rank}</b> - {$displayName}{$statusIndicator}\n";
            $message .= "   👤 {$username} (ID: {$user['user_id']})\n";
            $message .= "   💰 ₹{$withdrawalAmount} ({$withdrawalCount} {$withdrawalText})\n";
            $message .= "   👥 {$user['total_referrals']} referrals\n\n";

            $rank++;
        }

        // Add summary statistics
        $totalWithdrawals = array_sum(array_column($topUsers, 'successful_withdraw'));
        $totalReferrals = array_sum(array_column($topUsers, 'total_referrals'));
        $totalWithdrawalCount = array_sum(array_column($topUsers, 'withdrawal_count'));
        $averageWithdrawal = count($topUsers) > 0 ? round($totalWithdrawals / count($topUsers), 2) : 0;
        $averageReferrals = count($topUsers) > 0 ? round($totalReferrals / count($topUsers), 1) : 0;

        // Find highest single withdrawal and most referrals
        $highestWithdrawal = count($topUsers) > 0 ? max(array_column($topUsers, 'successful_withdraw')) : 0;
        $mostReferrals = count($topUsers) > 0 ? max(array_column($topUsers, 'total_referrals')) : 0;

        $message .= "📊 <b>SUMMARY STATISTICS</b>\n";
        $message .= "💰 Total Withdrawals: ₹" . number_format($totalWithdrawals, 2) . "\n";
        $message .= "👥 Total Referrals: {$totalReferrals}\n";
        $message .= "🔢 Total Withdrawal Transactions: {$totalWithdrawalCount}\n";
        $message .= "📈 Average Withdrawal: ₹{$averageWithdrawal}\n";
        $message .= "📊 Average Referrals: {$averageReferrals}\n";
        $message .= "🏆 Highest Single User: ₹" . number_format($highestWithdrawal, 2) . "\n";
        $message .= "👑 Most Referrals: {$mostReferrals}\n\n";

        // Add footer information
        $message .= "📈 <i>Rankings based on total successful withdrawal amounts</i>\n";
        $message .= "🔄 <i>Data updated in real-time</i>\n";
        $message .= "📅 <i>Generated: " . date('M d, Y H:i') . "</i>\n";
        $message .= "💾 <i>Storage: " . strtoupper(STORAGE_MODE) . "</i>";

        // Send the ranking message
        sendMessage($chatId, $message, null, 'HTML');

        // Send additional photo message with simplified ranking
        sendRankPhotoMessage($chatId, $topUsers);

    } catch (Exception $e) {
        error_log("Error in handleRankCommand: " . $e->getMessage());
        sendMessage($chatId, "❌ <b>Error</b>\n\nFailed to retrieve ranking data. Please try again later.", null, 'HTML');
    }
}

/**
 * Send rank photo message with simplified ranking
 */
function sendRankPhotoMessage($chatId, $topUsers) {
    try {
        // Check if rank.jpg exists
        $rankImagePath = __DIR__ . '/rank.jpg';
        if (!file_exists($rankImagePath)) {
            error_log("Rank image not found: " . $rankImagePath);
            // Send text message instead if image doesn't exist
            $caption = generateRankPhotoCaption($topUsers);
            $keyboard = getRankInlineKeyboard();
            sendMessage($chatId, $caption, $keyboard, 'HTML');
            return;
        }

        // Generate caption for the photo
        $caption = generateRankPhotoCaption($topUsers);

        // Create inline keyboard
        $keyboard = getRankInlineKeyboard();

        // Send photo with caption and keyboard
        sendPhoto($chatId, $rankImagePath, $caption, $keyboard, 'HTML');

    } catch (Exception $e) {
        error_log("Error in sendRankPhotoMessage: " . $e->getMessage());
        // Fallback: send as text message
        try {
            $caption = generateRankPhotoCaption($topUsers);
            $keyboard = getRankInlineKeyboard();
            sendMessage($chatId, $caption, $keyboard, 'HTML');
        } catch (Exception $fallbackError) {
            error_log("Error in rank photo fallback: " . $fallbackError->getMessage());
        }
    }
}

/**
 * Generate caption for rank photo message
 */
function generateRankPhotoCaption($topUsers) {
    $caption = "WITHDRAWAL RANK!💥\n\n";

    // Get top 10 users only
    $top10Users = array_slice($topUsers, 0, 10);

    if (empty($top10Users)) {
        $caption .= "❌ No withdrawal data available yet.\n\n";
    } else {
        foreach ($top10Users as $index => $user) {
            $rank = $index + 1;
            $emoji = getRankPhotoEmoji($rank);
            $amount = number_format($user['successful_withdraw'], 0); // No decimal places for simplified view
            $firstName = !empty($user['first_name']) ? htmlspecialchars($user['first_name']) : 'Unknown User';

            $caption .= "{$emoji} Withdraw ₹{$amount} - 👤{$firstName}*\n";
        }
        $caption .= "\n";
    }

    // Add footer content
    $caption .= "💰 <b>Minimum Withdrawal: ₹100</b>\n";
    $caption .= "🎁 <b>Joining Bonus: Up to ₹100</b>\n";
    $caption .= "👥 <b>Per Referral: Up to ₹100</b>\n\n";

    $caption .= "🔥 <b>Start earning money now!</b>\n";
    $caption .= "💸 <b>Instant withdrawals available</b>\n";
    $caption .= "🚀 <b>Join thousands of successful users</b>\n\n";

    $caption .= "📈 <i>Rankings updated in real-time</i>\n";
    $caption .= "💎 <i>Become the next top earner!</i>";

    return $caption;
}

/**
 * Get emoji for rank photo (simplified version)
 */
function getRankPhotoEmoji($rank) {
    switch ($rank) {
        case 1: return '🥇';
        case 2: return '🥈';
        case 3: return '🥉';
        default: return '🎖';
    }
}

/**
 * Get inline keyboard for rank photo
 */
function getRankInlineKeyboard() {
    $botUsername = getBotUsername();
    $startLink = "https://t.me/{$botUsername}?start=from_channel";

    return [
        'inline_keyboard' => [
            [
                [
                    'text' => 'Get Money',
                    'url' => $startLink
                ]
            ]
        ]
    ];
}

/**
 * Get rank emoji based on position
 */
function getRankEmoji($rank) {
    switch ($rank) {
        case 1: return '🥇';
        case 2: return '🥈';
        case 3: return '🥉';
        case 4:
        case 5: return '🏅';
        default: return '📍';
    }
}

/**
 * Get top users by successful withdrawal amounts
 */
function getTopUsersByWithdrawals($limit = 15) {
    if (STORAGE_MODE === 'json') {
        return getTopUsersByWithdrawalsJson($limit);
    } else {
        return getTopUsersByWithdrawalsMysql($limit);
    }
}

/**
 * Get top users from JSON storage
 */
function getTopUsersByWithdrawalsJson($limit = 15) {
    $users = readJsonFile(USERS_FILE);
    if (empty($users)) {
        return [];
    }

    $userRankings = [];

    foreach ($users as $userId => $userData) {
        $successfulWithdraw = $userData['successful_withdraw'] ?? 0;

        // Only include users with successful withdrawals
        if ($successfulWithdraw > 0) {
            // Count successful withdrawals
            $withdrawalCount = 0;
            $withdrawalReports = $userData['withdrawal_report'] ?? [];
            foreach ($withdrawalReports as $report) {
                if (($report['status'] ?? '') === 'Passed') {
                    $withdrawalCount++;
                }
            }

            // Count total referrals
            $totalReferrals = count($userData['promotion_report'] ?? []);

            $userRankings[] = [
                'user_id' => $userId,
                'first_name' => $userData['first_name'] ?? 'Unknown',
                'username' => $userData['username'] ?? '',
                'successful_withdraw' => $successfulWithdraw,
                'withdrawal_count' => $withdrawalCount,
                'total_referrals' => $totalReferrals,
                'banned' => $userData['banned'] ?? false
            ];
        }
    }

    // Sort by successful withdrawal amount (descending), then by referral count (descending), then by user_id (ascending for consistency)
    usort($userRankings, function($a, $b) {
        // Primary sort: withdrawal amount (descending)
        if (abs($a['successful_withdraw'] - $b['successful_withdraw']) > 0.01) {
            return $b['successful_withdraw'] <=> $a['successful_withdraw'];
        }

        // Secondary sort: referral count (descending)
        if ($a['total_referrals'] != $b['total_referrals']) {
            return $b['total_referrals'] <=> $a['total_referrals'];
        }

        // Tertiary sort: user_id (ascending for consistency)
        return $a['user_id'] <=> $b['user_id'];
    });

    // Return top users (limit)
    return array_slice($userRankings, 0, $limit);
}

/**
 * Get top users from MySQL storage
 */
function getTopUsersByWithdrawalsMysql($limit = 15) {
    try {
        $pdo = getDB();

        $query = "
            SELECT
                u.user_id,
                u.first_name,
                u.username,
                u.successful_withdraw,
                u.banned,
                u.created_at,
                COUNT(DISTINCT pr.id) as total_referrals,
                COUNT(CASE WHEN wr.status = 'Passed' THEN 1 END) as withdrawal_count,
                COALESCE(SUM(CASE WHEN wr.status = 'Passed' THEN wr.amount ELSE 0 END), 0) as verified_withdrawal_total
            FROM users u
            LEFT JOIN promotion_reports pr ON u.user_id = pr.referrer_id
            LEFT JOIN withdrawal_reports wr ON u.user_id = wr.user_id
            WHERE u.successful_withdraw > 0
            GROUP BY u.user_id, u.first_name, u.username, u.successful_withdraw, u.banned, u.created_at
            ORDER BY u.successful_withdraw DESC, total_referrals DESC, u.created_at ASC
            LIMIT ?
        ";

        $stmt = $pdo->prepare($query);
        $stmt->execute([$limit]);

        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Ensure data consistency - use verified total if available and different
        foreach ($results as &$result) {
            if ($result['verified_withdrawal_total'] > 0 &&
                abs($result['verified_withdrawal_total'] - $result['successful_withdraw']) > 0.01) {
                error_log("Withdrawal amount mismatch for user {$result['user_id']}: stored={$result['successful_withdraw']}, calculated={$result['verified_withdrawal_total']}");
            }
        }

        return $results;

    } catch (Exception $e) {
        error_log("Error in getTopUsersByWithdrawalsMysql: " . $e->getMessage());
        return [];
    }
}
function handleAddBalance($userId, $chatId) {
    if (!isAdmin($userId)) return;

    sendMessage($chatId, "Enter users's Telegram Id of whom you wanna add balance.");
    setUserSession($userId, 'add_balance_id');
}

function handleAddBalanceStep2($userId, $chatId, $text) {
    if (!isAdmin($userId)) return;

    if (!isValidTelegramId($text)) {
        sendMessage($chatId, "Invalid Telegram Id.");
        clearUserSession($userId);
        return;
    }

    $targetUser = getUser($text);
    if (!$targetUser) {
        sendMessage($chatId, "User not found.");
        clearUserSession($userId);
        return;
    }

    sendMessage($chatId, "Enter amount you wanna add.");
    setUserSession($userId, 'add_balance_amount', ['target_user_id' => $text]);
}

function handleAddBalanceStep3($userId, $chatId, $text, $data) {
    if (!isAdmin($userId)) return;

    if (!isValidAmount($text)) {
        sendMessage($chatId, "Invalid amount.");
        clearUserSession($userId);
        return;
    }

    $targetUserId = $data['target_user_id'];
    $amount = (float)$text;

    if (updateUserBalance($targetUserId, $amount, 'add')) {
        sendMessage($chatId, "Added ₹{$amount} to {$targetUserId}.");
        // Create fake SMS-style credit message
$msgId = rand(1000000000, 1999999999);
$time = date("d-m-Y H:i:s");
$last4 = substr($targetUserId, -4);

// Get updated balance after adding
$updatedUser = getUser($targetUserId);
$updatedBalance = number_format($updatedUser['balance'], 2);

$message = "<b>Your A/c XXXXXXXX{$last4} credited Rs.{$amount} by INTP. Bal after txn Rs {$updatedBalance} Msg Id {$msgId} Time {$time} -TeamInstanto</b>";

sendMessage($targetUserId, $message);
    } else {
        sendMessage($chatId, "Error adding balance.");
    }

    clearUserSession($userId);
}

function handleRemoveBalance($userId, $chatId) {
    if (!isAdmin($userId)) return;

    sendMessage($chatId, "Enter users's Telegram Id of whom you wanna remove balance.");
    setUserSession($userId, 'remove_balance_id');
}

function handleRemoveBalanceStep2($userId, $chatId, $text) {
    if (!isAdmin($userId)) return;

    if (!isValidTelegramId($text)) {
        sendMessage($chatId, "Invalid Telegram Id.");
        clearUserSession($userId);
        return;
    }

    $targetUser = getUser($text);
    if (!$targetUser) {
        sendMessage($chatId, "User not found.");
        clearUserSession($userId);
        return;
    }

    sendMessage($chatId, "Enter amount you wanna remove.");
    setUserSession($userId, 'remove_balance_amount', ['target_user_id' => $text, 'current_balance' => $targetUser['balance']]);
}

function handleRemoveBalanceStep3($userId, $chatId, $text, $data) {
    if (!isAdmin($userId)) return;

    if (!isValidAmount($text)) {
        sendMessage($chatId, "Invalid amount.");
        clearUserSession($userId);
        return;
    }

    $targetUserId = $data['target_user_id'];
    $amount = (float)$text;
    $currentBalance = $data['current_balance'];

    if ($currentBalance < $amount) {
        sendMessage($chatId, "User's balance is less than the amount you wanna remove.");
        clearUserSession($userId);
        return;
    }

    if (updateUserBalance($targetUserId, $amount, 'subtract')) {
        sendMessage($chatId, "Removed ₹{$amount} from {$targetUserId}.");
        date_default_timezone_set('Asia/Kolkata');
$msgId = rand(1000000000, 1999999999);
$time = date("d-m-Y H:i:s");
$last4 = substr($targetUserId, -4);
$updatedUser = getUser($targetUserId);
$updatedBalance = number_format($updatedUser['balance'], 2);

$message = "<b>Your A/c XXXXXXXX{$last4} debited Rs.{$amount} by INTP. Bal after txn Rs {$updatedBalance} Msg Id {$msgId} Time {$time} -TeamInstanto</b>";

sendMessage($targetUserId, $message);
    } else {
        sendMessage($chatId, "Error removing balance.");
    }

    clearUserSession($userId);
}

function handleBanUser($userId, $chatId) {
    if (!isAdmin($userId)) return;

    sendMessage($chatId, "Enter users's Telegram Id of whom you wanna ban.");
    setUserSession($userId, 'ban_user_id');
}

function handleBanUserStep2($userId, $chatId, $text) {
    if (!isAdmin($userId)) return;

    if (!isValidTelegramId($text)) {
        sendMessage($chatId, "Invalid Telegram Id.");
        clearUserSession($userId);
        return;
    }

    $targetUser = getUser($text);
    if (!$targetUser) {
        sendMessage($chatId, "User not found.");
        clearUserSession($userId);
        return;
    }

    if (banUser($text, true)) {
        sendMessage($chatId, "Banned {$text}.");
        sendMessage($text, "<b>🚫 Account Banned – Policy Violation

Dear User,

Your account has been permanently banned due to violations of our platform’s fair usage policy, including:

• Use of fake referrals
• Creation of multiple accounts
• Attempts to manipulate or cheat the referral system

We enforce strict rules to maintain fairness and integrity for all users. Further attempts to bypass this ban may result in a complete block of your IP or linked devices.

If you believe this action was taken in error or need further clarification, you may contact our support team:

📩 @InstantoHelpBot

Thank you for your understanding.

— Team Instanto</b>", null, 'HTML');
    } else {
        sendMessage($chatId, "Error banning user.");
    }

    clearUserSession($userId);
}

function handleUnbanUser($userId, $chatId) {
    if (!isAdmin($userId)) return;

    sendMessage($chatId, "Enter users's Telegram Id of whom you wanna unban.");
    setUserSession($userId, 'unban_user_id');
}

function handleUnbanUserStep2($userId, $chatId, $text) {
    if (!isAdmin($userId)) return;

    if (!isValidTelegramId($text)) {
        sendMessage($chatId, "Invalid Telegram Id.");
        clearUserSession($userId);
        return;
    }

    $targetUser = getUser($text);
    if (!$targetUser) {
        sendMessage($chatId, "User not found.");
        clearUserSession($userId);
        return;
    }

    if (banUser($text, false)) {
        sendMessage($chatId, "Unbanned {$text}.");
        sendMessage($text, "<b>✔️ You have been unbanned by admin.</b>", null, 'HTML');
    } else {
        sendMessage($chatId, "Error unbanning user.");
    }

    clearUserSession($userId);
}

function handleSetMainChannel($userId, $chatId) {
    if (!isAdmin($userId)) return;

    sendMessage($chatId, "Enter channel username without '@' to set as main channel.");
    setUserSession($userId, 'set_main_channel');
}

function handleSetMainChannelStep2($userId, $chatId, $text) {
    if (!isAdmin($userId)) return;

    if (strpos($text, '@') === 0) {
        sendMessage($chatId, "Invalid channel username.");
        clearUserSession($userId);
        return;
    }

    if (updateAdminSetting('main_channel', $text)) {
        $botUsername = getBotUsername();
        sendMessage($chatId, "Main channel set to @{$text}.\n\nNote : Must make @{$botUsername} admin in the channel.");
    } else {
        sendMessage($chatId, "Error setting main channel.");
    }

    clearUserSession($userId);
}

function handleSetPrivateLogsChannel($userId, $chatId) {
    if (!isAdmin($userId)) return;

    sendMessage($chatId, "Enter channel username with '@' ( if have public channel ) or channel id ( if have private channel ) to set as private logs channel.");
    setUserSession($userId, 'set_private_logs_channel');
}

function handleSetPrivateLogsChannelStep2($userId, $chatId, $text) {
    if (!isAdmin($userId)) return;

    if (updateAdminSetting('private_logs_channel', $text)) {
        sendMessage($chatId, "Private logs channel set to {$text}.");
    } else {
        sendMessage($chatId, "Error setting private logs channel.");
    }

    clearUserSession($userId);
}

function handleSetMaintenanceStatus($userId, $chatId) {
    if (!isAdmin($userId)) return;

    sendMessage($chatId, "Send status of maintenance ( On / Off ).");
    setUserSession($userId, 'set_maintenance_status');
}

function handleSetMaintenanceStatusStep2($userId, $chatId, $text) {
    if (!isAdmin($userId)) return;

    if ($text !== 'On' && $text !== 'Off') {
        sendMessage($chatId, "Invalid status.");
        clearUserSession($userId);
        return;
    }

    if (updateAdminSetting('maintenance_status', $text)) {
        sendMessage($chatId, "Maintenance status set to {$text}.");
    } else {
        sendMessage($chatId, "Error setting maintenance status.");
    }

    clearUserSession($userId);
}

function handleSetOTPAPIKey($userId, $chatId) {
    if (!isAdmin($userId)) return;

    sendMessage($chatId, "Enter your API Key from this website : renflair.in");
    setUserSession($userId, 'set_otp_api_key');
}

function handleSetOTPAPIKeyStep2($userId, $chatId, $text) {
    if (!isAdmin($userId)) return;

    if (updateAdminSetting('otp_website_api_key', $text)) {
        sendMessage($chatId, "OTP website API key set to {$text}.");
    } else {
        sendMessage($chatId, "Error setting OTP API key.");
    }

    clearUserSession($userId);
}

function handleSetPerReferAmount($userId, $chatId) {
    if (!isAdmin($userId)) return;

    sendMessage($chatId, "Enter per refer amount range in format: min-max (e.g., 20-50)");
    setUserSession($userId, 'set_per_refer_amount');
}

function handleSetPerReferAmountStep2($userId, $chatId, $text) {
    if (!isAdmin($userId)) return;

    // Validate range format (min-max)
    if (preg_match('/^(\d+)-(\d+)$/', trim($text), $matches)) {
        $minAmount = intval($matches[1]);
        $maxAmount = intval($matches[2]);

        if ($minAmount > 0 && $maxAmount > 0 && $minAmount <= $maxAmount) {
            if (updateAdminSetting('per_refer_amount_range', trim($text))) {
                sendMessage($chatId, "Per refer amount range set to ₹{$minAmount}-₹{$maxAmount}.");
            } else {
                sendMessage($chatId, "Error setting per refer amount range.");
            }
        } else {
            sendMessage($chatId, "Invalid range: minimum must be ≤ maximum and both must be positive.");
        }
    } else {
        sendMessage($chatId, "Invalid format. Use: min-max (e.g., 20-50)");
    }

    clearUserSession($userId);
}

function handleSetJoiningBonusAmount($userId, $chatId) {
    if (!isAdmin($userId)) return;

    sendMessage($chatId, "Enter joining bonus amount range in format: min-max (e.g., 20-50)");
    setUserSession($userId, 'set_joining_bonus_amount');
}

function handleSetJoiningBonusAmountStep2($userId, $chatId, $text) {
    if (!isAdmin($userId)) return;

    // Validate range format (min-max)
    if (preg_match('/^(\d+)-(\d+)$/', trim($text), $matches)) {
        $minAmount = intval($matches[1]);
        $maxAmount = intval($matches[2]);

        if ($minAmount > 0 && $maxAmount > 0 && $minAmount <= $maxAmount) {
            if (updateAdminSetting('joining_bonus_amount_range', trim($text))) {
                sendMessage($chatId, "Joining bonus amount range set to ₹{$minAmount}-₹{$maxAmount}.");
            } else {
                sendMessage($chatId, "Error setting joining bonus amount range.");
            }
        } else {
            sendMessage($chatId, "Invalid range: minimum must be ≤ maximum and both must be positive.");
        }
    } else {
        sendMessage($chatId, "Invalid format. Use: min-max (e.g., 20-50)");
    }

    clearUserSession($userId);
}

function handleCheckUserRecord($userId, $chatId) {
    if (!isAdmin($userId)) return;

    sendMessage($chatId, "Enter user's Telegram Id to check.");
    setUserSession($userId, 'check_user_record');
}

function handleCheckUserRecordStep2($userId, $chatId, $text) {
    if (!isAdmin($userId)) return;

    if (!isValidTelegramId($text)) {
        sendMessage($chatId, "Invalid Telegram Id.");
        clearUserSession($userId);
        return;
    }

    $targetUser = getUser($text);
    if (!$targetUser) {
        sendMessage($chatId, "User not found.");
        clearUserSession($userId);
        return;
    }

    $record = "{$text} record\n\n";
    $record .= "Banned : " . ($targetUser['banned'] ? 'true' : 'false') . "\n";
    $record .= "Referred : " . ($targetUser['referred'] ? 'true' : 'false') . "\n";
    $record .= "Referred by : {$targetUser['referred_by']}\n";
    $record .= "Joining bonus got : ₹{$targetUser['joining_bonus_got']}\n";
    $record .= "Referral link : {$targetUser['referral_link']}\n";
    $record .= "Balance : ₹{$targetUser['balance']}\n";
    $record .= "Successful withdraw : ₹{$targetUser['successful_withdraw']}\n";
    $record .= "Withdraw under review : ₹{$targetUser['withdraw_under_review']}\n\n";
    $record .= "Account info :\n";
    $record .= "Name : {$targetUser['name']}\n";
    $record .= "IFSC : {$targetUser['ifsc']}\n";
    $record .= "Email : {$targetUser['email']}\n";
    $record .= "Account number : {$targetUser['account_number']}\n";
    $record .= "Mobile number : {$targetUser['mobile_number']}";

    sendMessage($chatId, $record);
    clearUserSession($userId);
}

function handlePassUserWithdrawal($userId, $chatId) {
    if (!isAdmin($userId)) return;

    sendMessage($chatId, "Send user's Telegram Id of whom you wanna pass withdrawal.");
    setUserSession($userId, 'pass_user_withdrawal');
}

function handlePassUserWithdrawalStep2($userId, $chatId, $text) {
    if (!isAdmin($userId)) return;

    if (!isValidTelegramId($text)) {
        sendMessage($chatId, "Invalid Telegram Id.");
        clearUserSession($userId);
        return;
    }

    $targetUser = getUser($text);
    if (!$targetUser) {
        sendMessage($chatId, "User not found.");
        clearUserSession($userId);
        return;
    }

    $withdrawal = updateWithdrawalStatus($text, 'Passed');
    if ($withdrawal) {
        sendMessage($chatId, "Withdrawal of {$text} passed.");
        sendMessage($text, "✅Withdrawal request passed\n💵₹{$withdrawal['amount']}     ⏰" . getCurrentDate() . "\n\nYour withdrawal request has been approved, and the payment will be credited to your account in <b>1-2 working days🕒</b>", null, 'HTML');
    } else {
        sendMessage($chatId, "User's withdrawal request is not under review.");
    }

    clearUserSession($userId);
}

function handleFailUserWithdrawal($userId, $chatId) {
    if (!isAdmin($userId)) return;

    sendMessage($chatId, "Send user's Telegram Id of whom you wanna fail withdrawal.");
    setUserSession($userId, 'fail_user_withdrawal');
}

function handleFailUserWithdrawalStep2($userId, $chatId, $text) {
    if (!isAdmin($userId)) return;

    if (!isValidTelegramId($text)) {
        sendMessage($chatId, "Invalid Telegram Id.");
        clearUserSession($userId);
        return;
    }

    $targetUser = getUser($text);
    if (!$targetUser) {
        sendMessage($chatId, "User not found.");
        clearUserSession($userId);
        return;
    }

    $withdrawal = updateWithdrawalStatus($text, 'Failed');
    if ($withdrawal) {
        sendMessage($chatId, "Withdrawal of {$text} failed.");
        sendMessage($text, "❌Withdrawal request failed\n💵₹{$withdrawal['amount']}     ⏰" . getCurrentDate() . "\n\nYour withdrawal request has been declined, and the payment will not be credited to your account.", null, 'HTML');
    } else {
        sendMessage($chatId, "User's withdrawal request is not under review.");
    }

    clearUserSession($userId);
}

// Force subscription channel management handlers
function handleAddForceSubChannel($userId, $chatId) {
    if (!isAdmin($userId)) return;

    $message = "📢 <b>Add Force Subscription Channel</b>\n\n";
    $message .= "To add a new force subscription channel:\n\n";
    $message .= "1️⃣ Go to the channel you want to add\n";
    $message .= "2️⃣ Forward any message from that channel to this bot\n";
    $message .= "3️⃣ I will automatically verify and add the channel\n\n";
    $message .= "⚠️ <b>Requirements:</b>\n";
    $message .= "• The bot must be an administrator in the channel\n";
    $message .= "• The channel must be public or the bot must have access\n\n";
    $message .= "📤 <b>Please forward a message from the channel now:</b>";

    sendMessage($chatId, $message, null, 'HTML');
    setUserSession($userId, 'add_force_sub_channel');
}

function handleAddForceSubChannelStep2($userId, $chatId, $message) {
    if (!isAdmin($userId)) return;

    // Check if message is forwarded from a channel
    if (!isset($message['forward_from_chat'])) {
        sendMessage($chatId, "❌ Please forward a message from the channel you want to add.");
        return;
    }

    $forwardedChat = $message['forward_from_chat'];

    // Check if it's a channel
    if ($forwardedChat['type'] !== 'channel') {
        sendMessage($chatId, "❌ The forwarded message must be from a channel, not a group or private chat.");
        clearUserSession($userId);
        return;
    }

    $channelId = $forwardedChat['id'];
    $channelTitle = $forwardedChat['title'];
    $channelUsername = isset($forwardedChat['username']) ? $forwardedChat['username'] : null;

    // Verify bot has admin access
    if (!isBotAdminInChannel($channelId)) {
        sendMessage($chatId, "❌ <b>Bot Permission Error</b>\n\nThe bot is not an administrator in this channel. Please:\n\n1️⃣ Add the bot as an administrator in the channel\n2️⃣ Try again by forwarding a message", null, 'HTML');
        clearUserSession($userId);
        return;
    }

    // Prepare channel data
    $channelData = [
        'id' => $channelId,
        'title' => $channelTitle,
        'username' => $channelUsername,
        'type' => 'force_sub',
        'added_by' => $userId,
        'added_at' => time()
    ];

    // Add channel
    if (addForceSubChannel($channelData)) {
        $message = "✅ <b>Channel Added Successfully!</b>\n\n";
        $message .= "📢 <b>Channel:</b> {$channelTitle}\n";
        $message .= "🆔 <b>ID:</b> {$channelId}\n";
        if ($channelUsername) {
            $message .= "👤 <b>Username:</b> @{$channelUsername}\n";
        }
        $message .= "🤖 <b>Bot Status:</b> Administrator ✅\n\n";
        $message .= "The channel has been added to the force subscription list. All new users will need to join this channel.";

        sendMessage($chatId, $message, null, 'HTML');
    } else {
        sendMessage($chatId, "❌ Failed to add channel. It may already exist in the force subscription list.");
    }

    clearUserSession($userId);
}

function handleRemoveForceSubChannel($userId, $chatId) {
    if (!isAdmin($userId)) return;

    $channels = getForceSubChannels();

    if (empty($channels)) {
        sendMessage($chatId, "📢 No force subscription channels found.\n\nUse \"➕ Add Force Sub Channel\" to add channels first.");
        return;
    }

    $message = "📢 <b>Remove Force Subscription Channel</b>\n\n";
    $message .= "Select a channel to remove:\n\n";

    $keyboard = ['inline_keyboard' => []];

    foreach ($channels as $index => $channel) {
        $channelTitle = $channel['title'] ?? 'Unknown Channel';
        $channelId = $channel['id'];

        $keyboard['inline_keyboard'][] = [
            ['text' => "🗑️ {$channelTitle}", 'callback_data' => "remove_force_sub_{$channelId}"]
        ];
    }

    $keyboard['inline_keyboard'][] = [
        ['text' => '↩️ Back to Admin Panel', 'callback_data' => 'admin']
    ];

    sendMessage($chatId, $message, $keyboard, 'HTML');
}

function handleRemoveForceSubChannelConfirm($userId, $chatId, $channelId) {
    if (!isAdmin($userId)) return;

    $channels = getForceSubChannels();
    $channelToRemove = null;

    foreach ($channels as $channel) {
        if ($channel['id'] == $channelId) {
            $channelToRemove = $channel;
            break;
        }
    }

    if (!$channelToRemove) {
        sendMessage($chatId, "❌ Channel not found.");
        return;
    }

    if (removeForceSubChannel($channelId)) {
        $message = "✅ <b>Channel Removed Successfully!</b>\n\n";
        $message .= "📢 <b>Removed Channel:</b> {$channelToRemove['title']}\n";
        $message .= "🆔 <b>ID:</b> {$channelId}\n\n";
        $message .= "The channel has been removed from the force subscription list.";

        sendMessage($chatId, $message, null, 'HTML');
    } else {
        sendMessage($chatId, "❌ Failed to remove channel.");
    }
}

function handleViewForceSubChannels($userId, $chatId) {
    if (!isAdmin($userId)) return;

    $allChannels = getAllForceSubChannels();

    if (empty($allChannels)) {
        sendMessage($chatId, "📢 <b>Force Subscription Channels</b>\n\n❌ No channels configured.\n\nUse \"➕ Add Force Sub Channel\" to add channels.", null, 'HTML');
        return;
    }

    $message = "📢 <b>Force Subscription Channels</b>\n\n";
    $message .= "All users must join these channels:\n\n";

    foreach ($allChannels as $index => $channel) {
        $number = $index + 1;
        $channelTitle = $channel['title'] ?? 'Unknown Channel';
        $channelUsername = $channel['username'] ?? str_replace('@', '', $channel['id']);
        $channelType = $channel['type'] ?? 'force_sub';

        $typeIcon = $channelType === 'main' ? '🏠' : '📢';

        $message .= "<b>{$number}.</b> {$typeIcon} <a href=\"https://t.me/{$channelUsername}\">{$channelTitle}</a>\n";
        $message .= "   🆔 ID: <code>{$channel['id']}</code>\n\n";
    }

    $message .= "👥 <b>Total Channels:</b> " . count($allChannels);

    sendMessage($chatId, $message, null, 'HTML');
}

// Withdrawal tax and control system handlers
function handleWithdrawalSettings($userId, $chatId) {
    if (!isAdmin($userId)) return;

    $settings = getWithdrawalSettings();

    $message = "🏛️ <b>Withdrawal Tax & Control Settings</b>\n\n";

    // Withdrawal status
    $statusIcon = $settings['withdrawal_enabled'] ? '✅' : '❌';
    $statusText = $settings['withdrawal_enabled'] ? 'Enabled' : 'Disabled';
    $message .= "📊 <b>Withdrawal Status:</b> {$statusIcon} {$statusText}\n\n";

    // Tax information
    $message .= "🏛️ <b>Tax Configuration:</b>\n";
    if ($settings['tax_type'] === 'none' || $settings['tax_amount'] <= 0) {
        $message .= "   💰 No tax applied\n";
    } elseif ($settings['tax_type'] === 'fixed') {
        $message .= "   💰 Fixed: ₹{$settings['tax_amount']} per withdrawal\n";
    } elseif ($settings['tax_type'] === 'percentage') {
        $message .= "   💰 Percentage: {$settings['tax_amount']}% of withdrawal amount\n";
    }

    $message .= "\n📋 <b>Select an option to configure:</b>";

    $keyboard = [
        'inline_keyboard' => [
            [
                ['text' => $settings['withdrawal_enabled'] ? '❌ Disable Withdrawals' : '✅ Enable Withdrawals',
                 'callback_data' => 'toggle_withdrawal_status']
            ],
            [
                ['text' => '🏛️ Configure Tax Settings', 'callback_data' => 'configure_withdrawal_tax']
            ],
            [
                ['text' => '📊 View Tax Preview', 'callback_data' => 'preview_withdrawal_tax']
            ],
            [
                ['text' => '↩️ Back to Admin Panel', 'callback_data' => 'admin']
            ]
        ]
    ];

    sendMessage($chatId, $message, $keyboard, 'HTML');
}

function handleToggleWithdrawalStatus($userId, $chatId) {
    if (!isAdmin($userId)) return;

    $settings = getWithdrawalSettings();
    $newStatus = !$settings['withdrawal_enabled'];

    if (updateWithdrawalSettings($newStatus, $settings['tax_type'], $settings['tax_amount'])) {
        $statusText = $newStatus ? 'enabled' : 'disabled';
        $statusIcon = $newStatus ? '✅' : '❌';

        $message = "{$statusIcon} <b>Withdrawal Status Updated</b>\n\n";
        $message .= "Withdrawals are now <b>{$statusText}</b> for all users.\n\n";

        if (!$newStatus) {
            $message .= "⚠️ Users will see a message that withdrawals are currently disabled when they try to withdraw.";
        } else {
            $message .= "✅ Users can now make withdrawal requests normally.";
        }

        sendMessage($chatId, $message, null, 'HTML');

        // Return to withdrawal settings
        handleWithdrawalSettings($userId, $chatId);
    } else {
        sendMessage($chatId, "❌ Failed to update withdrawal status. Please try again.");
    }
}

function handleConfigureWithdrawalTax($userId, $chatId) {
    if (!isAdmin($userId)) return;

    $message = "🏛️ <b>Configure Withdrawal Tax</b>\n\n";
    $message .= "Select the type of tax you want to apply:\n\n";
    $message .= "💰 <b>No Tax:</b> No deductions from withdrawals\n";
    $message .= "💵 <b>Fixed Tax:</b> Fixed amount deducted per withdrawal\n";
    $message .= "📊 <b>Percentage Tax:</b> Percentage of withdrawal amount deducted\n\n";
    $message .= "Choose an option:";

    $keyboard = [
        'inline_keyboard' => [
            [
                ['text' => '💰 No Tax', 'callback_data' => 'set_tax_none']
            ],
            [
                ['text' => '💵 Fixed Tax Amount', 'callback_data' => 'set_tax_fixed']
            ],
            [
                ['text' => '📊 Percentage Tax', 'callback_data' => 'set_tax_percentage']
            ],
            [
                ['text' => '↩️ Back to Withdrawal Settings', 'callback_data' => 'withdrawal_settings']
            ]
        ]
    ];

    sendMessage($chatId, $message, $keyboard, 'HTML');
}

function handleSetTaxType($userId, $chatId, $taxType) {
    if (!isAdmin($userId)) return;

    if ($taxType === 'none') {
        $settings = getWithdrawalSettings();
        if (updateWithdrawalSettings($settings['withdrawal_enabled'], 'none', 0)) {
            sendMessage($chatId, "✅ <b>Tax Removed</b>\n\nNo tax will be applied to withdrawals.", null, 'HTML');
            handleWithdrawalSettings($userId, $chatId);
        } else {
            sendMessage($chatId, "❌ Failed to update tax settings. Please try again.");
        }
        return;
    }

    if ($taxType === 'fixed') {
        $message = "💵 <b>Set Fixed Tax Amount</b>\n\n";
        $message .= "Enter the fixed amount to be deducted from each withdrawal:\n\n";
        $message .= "Example: <code>10</code> (for ₹10 per withdrawal)\n\n";
        $message .= "💡 <i>Send the amount as a number (without ₹ symbol)</i>";
    } elseif ($taxType === 'percentage') {
        $message = "📊 <b>Set Percentage Tax</b>\n\n";
        $message .= "Enter the percentage to be deducted from each withdrawal:\n\n";
        $message .= "Example: <code>5</code> (for 5% of withdrawal amount)\n\n";
        $message .= "💡 <i>Send the percentage as a number (without % symbol)</i>";
    }

    sendMessage($chatId, $message, null, 'HTML');
    setUserSession($userId, 'set_withdrawal_tax', ['type' => $taxType]);
}

function handleSetWithdrawalTaxStep2($userId, $chatId, $amount) {
    if (!isAdmin($userId)) return;

    $session = getUserSession($userId);
    if (!$session || $session['step'] !== 'set_withdrawal_tax') {
        sendMessage($chatId, "❌ Session expired. Please try again.");
        return;
    }

    $taxType = $session['data']['type'];
    $taxAmount = floatval($amount);

    // Validate input
    if ($taxAmount < 0) {
        sendMessage($chatId, "❌ Tax amount cannot be negative. Please enter a valid amount.");
        return;
    }

    if ($taxType === 'percentage' && $taxAmount >= 100) {
        sendMessage($chatId, "❌ Percentage cannot be 100% or higher. Please enter a value less than 100.");
        return;
    }

    if ($taxType === 'fixed' && $taxAmount > 1000) {
        sendMessage($chatId, "❌ Fixed tax amount seems too high. Please enter a reasonable amount (max ₹1000).");
        return;
    }

    $settings = getWithdrawalSettings();
    if (updateWithdrawalSettings($settings['withdrawal_enabled'], $taxType, $taxAmount)) {
        $message = "✅ <b>Tax Settings Updated</b>\n\n";

        if ($taxType === 'fixed') {
            $message .= "💵 <b>Fixed Tax:</b> ₹{$taxAmount} per withdrawal\n\n";
            $message .= "Example: If user withdraws ₹100, they will receive ₹" . (100 - $taxAmount) . " after tax.";
        } elseif ($taxType === 'percentage') {
            $message .= "📊 <b>Percentage Tax:</b> {$taxAmount}% of withdrawal amount\n\n";
            $message .= "Example: If user withdraws ₹100, they will receive ₹" . (100 - ($taxAmount * 100 / 100)) . " after tax.";
        }

        sendMessage($chatId, $message, null, 'HTML');
        clearUserSession($userId);
        handleWithdrawalSettings($userId, $chatId);
    } else {
        sendMessage($chatId, "❌ Failed to update tax settings. Please try again.");
        clearUserSession($userId);
    }
}

function handlePreviewWithdrawalTax($userId, $chatId) {
    if (!isAdmin($userId)) return;

    $settings = getWithdrawalSettings();

    $message = "📊 <b>Withdrawal Tax Preview</b>\n\n";

    if ($settings['tax_type'] === 'none' || $settings['tax_amount'] <= 0) {
        $message .= "💰 <b>Current Setting:</b> No tax applied\n\n";
        $message .= "All withdrawal amounts will be processed without deductions.";
    } else {
        $message .= "🏛️ <b>Current Tax Configuration:</b>\n";

        if ($settings['tax_type'] === 'fixed') {
            $message .= "💵 Fixed Tax: ₹{$settings['tax_amount']} per withdrawal\n\n";
        } elseif ($settings['tax_type'] === 'percentage') {
            $message .= "📊 Percentage Tax: {$settings['tax_amount']}% of withdrawal amount\n\n";
        }

        $message .= "📋 <b>Examples:</b>\n";

        $testAmounts = [100, 200, 500, 1000];
        foreach ($testAmounts as $amount) {
            $calculation = calculateWithdrawalTax($amount);
            $message .= "• ₹{$amount} → ₹" . number_format($calculation['final_amount'], 2);
            if ($calculation['tax_amount'] > 0) {
                $message .= " (tax: ₹" . number_format($calculation['tax_amount'], 2) . ")";
            }
            $message .= "\n";
        }
    }

    $keyboard = [
        'inline_keyboard' => [
            [
                ['text' => '↩️ Back to Withdrawal Settings', 'callback_data' => 'withdrawal_settings']
            ]
        ]
    ];

    sendMessage($chatId, $message, $keyboard, 'HTML');
}
?>
