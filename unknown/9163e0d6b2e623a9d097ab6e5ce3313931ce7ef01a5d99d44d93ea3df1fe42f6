<?php
/**
 * Cache clearing script for PHP and server caching
 */

echo "=== Cache Clearing Script ===\n\n";

// Clear OPcache if available
if (function_exists('opcache_reset')) {
    if (opcache_reset()) {
        echo "✅ OPcache cleared successfully\n";
    } else {
        echo "❌ Failed to clear OPcache\n";
    }
} else {
    echo "ℹ️ OPcache not available\n";
}

// Clear file stat cache
clearstatcache();
echo "✅ File stat cache cleared\n";

// Force garbage collection
if (function_exists('gc_collect_cycles')) {
    $collected = gc_collect_cycles();
    echo "✅ Garbage collection completed (collected: {$collected})\n";
}

// Clear any session cache
if (session_status() === PHP_SESSION_ACTIVE) {
    session_destroy();
    echo "✅ Session cache cleared\n";
}

// Test if the welcome message function works now
echo "\n🧪 Testing welcome message after cache clear...\n";

require_once 'config.php';
require_once 'core_functions.php';

$testMessage = getWelcomeMessage(50, 'testchannel');

if (strpos($testMessage, 'Welcome to My Amazing Bot') !== false) {
    echo "✅ SUCCESS: Custom welcome message is now working!\n";
} else {
    echo "❌ ISSUE: Welcome message still shows old content\n";
    echo "First 100 characters: " . substr($testMessage, 0, 100) . "...\n";
}

echo "\n=== Cache Clear Complete ===\n";
echo "Now try sending /start to your bot to test the new message.\n";
?>
