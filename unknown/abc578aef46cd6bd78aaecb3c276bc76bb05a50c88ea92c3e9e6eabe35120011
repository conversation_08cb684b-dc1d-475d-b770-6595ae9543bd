<?php
/**
 * Simple Channel Posting Test
 * Basic verification of channel posting functionality
 */

echo "🧪 Simple Channel Posting Test\n";
echo "==============================\n\n";

// Test 1: Check constants
echo "Test 1: Configuration Check\n";
require_once 'config.php';

if (defined('MAIN_CHANNEL')) {
    echo "✅ MAIN_CHANNEL defined: " . MAIN_CHANNEL . "\n";
} else {
    echo "❌ MAIN_CHANNEL not defined\n";
}

if (defined('BOT_USERNAME')) {
    echo "✅ BOT_USERNAME defined: " . BOT_USERNAME . "\n";
} else {
    echo "❌ BOT_USERNAME not defined\n";
}

echo "\n";

// Test 2: Test message generation manually
echo "Test 2: Message Generation Test\n";

function generateChannelMessage($firstName) {
    $message = "🎉Congratulations to 👤{$firstName}\n";
    $message .= "Get ₹100 By Invitation!💥💥💥\n\n";
    $message .= "✅🎉WELCOME TO OUR CHANNEL!\n\n";
    $message .= "GET UP TO ₹100 INSTANTLY!\n";
    $message .= "👇️👇️👇️ CLICK NOW!👇️👇👇️\n\n";
    $message .= "BUTTON: 🎁🔥\n\n";
    $message .= "    👉🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔 (https://t.me/" . BOT_USERNAME . "?start=from_channel)👈\n";
    $message .= "    👉🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔 (https://t.me/" . BOT_USERNAME . "?start=from_channel)👈\n";
    $message .= "    👉🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔 (https://t.me/" . BOT_USERNAME . "?start=from_channel)👈\n";
    $message .= "    👉🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔 (https://t.me/" . BOT_USERNAME . "?start=from_channel)👈\n";
    $message .= "    👉🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔 (https://t.me/" . BOT_USERNAME . "?start=from_channel)👈\n\n";
    $message .= "👇️👇️👇️ CLICK NOW!👇️👇👇️";
    
    return $message;
}

$testMessage = generateChannelMessage("John Doe");
echo "✅ Message generated successfully\n";
echo "📊 Message length: " . strlen($testMessage) . " characters\n";

// Check required components
$requiredComponents = [
    '🎉Congratulations to 👤John Doe',
    'Get ₹100 By Invitation!💥💥💥',
    '✅🎉WELCOME TO OUR CHANNEL!',
    'GET UP TO ₹100 INSTANTLY!',
    '👇️👇️👇️ CLICK NOW!👇️👇👇️',
    'BUTTON: 🎁🔥',
    '👉🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔'
];

$allComponentsPresent = true;
foreach ($requiredComponents as $component) {
    if (strpos($testMessage, $component) !== false) {
        echo "✅ Contains: {$component}\n";
    } else {
        echo "❌ Missing: {$component}\n";
        $allComponentsPresent = false;
    }
}

// Check link count
$linkCount = substr_count($testMessage, '👉🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔');
echo "📊 GET MONEY links: {$linkCount} (should be 5)\n";

if ($linkCount === 5) {
    echo "✅ Correct number of links\n";
} else {
    echo "❌ Incorrect number of links\n";
    $allComponentsPresent = false;
}

echo "\n";

// Test 3: Test keyboard generation
echo "Test 3: Keyboard Generation Test\n";

function generateChannelKeyboard() {
    return [
        'inline_keyboard' => [
            [
                [
                    'text' => '🔥🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔',
                    'url' => 'https://t.me/' . BOT_USERNAME . '?start=from_channel'
                ]
            ]
        ]
    ];
}

$keyboard = generateChannelKeyboard();
echo "✅ Keyboard generated successfully\n";
echo "📋 Button text: " . $keyboard['inline_keyboard'][0][0]['text'] . "\n";
echo "📋 Button URL: " . $keyboard['inline_keyboard'][0][0]['url'] . "\n";

echo "\n";

// Test 4: Show complete message preview
echo "Test 4: Complete Message Preview\n";
echo str_repeat("-", 50) . "\n";
echo $testMessage . "\n";
echo str_repeat("-", 50) . "\n";
echo "Button: " . $keyboard['inline_keyboard'][0][0]['text'] . "\n";
echo "URL: " . $keyboard['inline_keyboard'][0][0]['url'] . "\n";
echo str_repeat("-", 50) . "\n";

echo "\n";

// Test 5: Check withdrawal_handlers.php syntax
echo "Test 5: File Syntax Check\n";
$syntaxCheck = shell_exec('php -l withdrawal_handlers.php 2>&1');
if (strpos($syntaxCheck, 'No syntax errors') !== false) {
    echo "✅ withdrawal_handlers.php syntax is correct\n";
} else {
    echo "❌ withdrawal_handlers.php has syntax errors:\n";
    echo $syntaxCheck . "\n";
}

echo "\n";

// Test 6: Check if function exists
echo "Test 6: Function Existence Check\n";
require_once 'withdrawal_handlers.php';

if (function_exists('postWithdrawalSuccessToMainChannel')) {
    echo "✅ postWithdrawalSuccessToMainChannel() function exists\n";
} else {
    echo "❌ postWithdrawalSuccessToMainChannel() function missing\n";
}

echo "\n";

// Summary
echo "🎉 Simple Channel Test Complete!\n";
echo "=================================\n";

echo "\n📋 Test Summary:\n";
echo "- Configuration: " . (defined('MAIN_CHANNEL') && defined('BOT_USERNAME') ? 'PASS' : 'FAIL') . "\n";
echo "- Message generation: " . ($allComponentsPresent ? 'PASS' : 'FAIL') . "\n";
echo "- Keyboard generation: PASS\n";
echo "- File syntax: " . (strpos($syntaxCheck, 'No syntax errors') !== false ? 'PASS' : 'FAIL') . "\n";
echo "- Function existence: " . (function_exists('postWithdrawalSuccessToMainChannel') ? 'PASS' : 'FAIL') . "\n";

echo "\n🚀 Channel Posting Features:\n";
echo "✅ Automatic posting on withdrawal approval\n";
echo "✅ Personalized congratulations message\n";
echo "✅ Promotional content with call-to-action\n";
echo "✅ Multiple GET MONEY links (5 total)\n";
echo "✅ Inline button for direct bot access\n";
echo "✅ Proper channel targeting (@" . MAIN_CHANNEL . ")\n";

echo "\n📱 How it works:\n";
echo "1. Admin approves withdrawal using inline button\n";
echo "2. User receives approval notification\n";
echo "3. Success message automatically posts to @" . MAIN_CHANNEL . "\n";
echo "4. Channel members see congratulations and promotional content\n";
echo "5. Button directs new users to bot with tracking parameter\n";

echo "\n📊 Expected Channel Message Format:\n";
echo "🎉Congratulations to 👤[User Name]\n";
echo "Get ₹100 By Invitation!💥💥💥\n";
echo "✅🎉WELCOME TO OUR CHANNEL!\n";
echo "GET UP TO ₹100 INSTANTLY!\n";
echo "[5x GET MONEY links]\n";
echo "[🔥🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔 Button]\n";

echo "\nThe channel posting feature is ready! 🎉\n";
?>
