<?php
require_once 'config.php';
require_once 'database_functions.php';

/**
 * Custom Referral Link Management System
 * Allows admins to create personalized referral parameters
 */

/**
 * Handle custom referral link management commands
 */
function handleCustomReferralCommand($userId, $chatId, $command, $parameters = []) {
    // Check if user is admin
    if (!isAdmin($userId)) {
        sendMessage($chatId, "❌ <b>Access Denied</b>\n\nThis feature is only available to administrators.", null, 'HTML');
        return;
    }

    switch ($command) {
        case 'list':
            showCustomReferralList($chatId);
            break;
        case 'create':
            if (count($parameters) >= 2) {
                createCustomReferralLink($chatId, $parameters[0], $parameters[1]);
            } else {
                showCustomReferralHelp($chatId);
            }
            break;
        case 'edit':
            if (count($parameters) >= 2) {
                editCustomReferralLink($chatId, $parameters[0], $parameters[1]);
            } else {
                showCustomReferralHelp($chatId);
            }
            break;
        case 'delete':
            if (count($parameters) >= 1) {
                deleteCustomReferralLink($chatId, $parameters[0]);
            } else {
                showCustomReferralHelp($chatId);
            }
            break;
        case 'view':
            if (count($parameters) >= 1) {
                viewUserCustomLinks($chatId, $parameters[0]);
            } else {
                showCustomReferralHelp($chatId);
            }
            break;
        default:
            showCustomReferralHelp($chatId);
            break;
    }
}

/**
 * Create a new custom referral link
 */
function createCustomReferralLink($chatId, $customParam, $userId) {
    try {
        // Validate custom parameter
        if (!isValidCustomParameter($customParam)) {
            sendMessage($chatId, "❌ <b>Invalid Parameter</b>\n\nCustom parameters must:\n• Be 3-30 characters long\n• Contain only letters, numbers, hyphens, and underscores\n• Not start or end with hyphens/underscores", null, 'HTML');
            return;
        }

        // Check if parameter already exists
        if (customParameterExists($customParam)) {
            sendMessage($chatId, "❌ <b>Parameter Already Exists</b>\n\nThe custom parameter '<code>{$customParam}</code>' is already in use.\n\nPlease choose a different parameter.", null, 'HTML');
            return;
        }

        // Validate user ID
        $targetUser = getUser($userId);
        if (!$targetUser) {
            sendMessage($chatId, "❌ <b>User Not Found</b>\n\nUser ID '<code>{$userId}</code>' does not exist in the system.", null, 'HTML');
            return;
        }

        // Create the custom referral link
        $result = saveCustomReferralLink($userId, $customParam);
        
        if ($result) {
            $customLink = "https://t.me/" . BOT_USERNAME . "?start={$customParam}";
            $userName = htmlspecialchars($targetUser['first_name']);
            
            $message = "✅ <b>Custom Referral Link Created</b>\n\n";
            $message .= "👤 <b>User:</b> {$userName} (ID: <code>{$userId}</code>)\n";
            $message .= "🔗 <b>Custom Parameter:</b> <code>{$customParam}</code>\n";
            $message .= "🌐 <b>Custom Link:</b>\n<code>{$customLink}</code>\n\n";
            $message .= "📋 <i>Users who click this link will be credited as referrals to {$userName}</i>";
            
            sendMessage($chatId, $message, null, 'HTML');
            
            // Log the action
            error_log("Custom referral link created: {$customParam} -> {$userId} by admin {$chatId}");
        } else {
            sendMessage($chatId, "❌ <b>Creation Failed</b>\n\nFailed to create custom referral link. Please try again.", null, 'HTML');
        }

    } catch (Exception $e) {
        error_log("Error creating custom referral link: " . $e->getMessage());
        sendMessage($chatId, "❌ <b>Error</b>\n\nAn error occurred while creating the custom referral link.", null, 'HTML');
    }
}

/**
 * Edit an existing custom referral link
 */
function editCustomReferralLink($chatId, $oldParam, $newParam) {
    try {
        // Validate new parameter
        if (!isValidCustomParameter($newParam)) {
            sendMessage($chatId, "❌ <b>Invalid Parameter</b>\n\nCustom parameters must:\n• Be 3-30 characters long\n• Contain only letters, numbers, hyphens, and underscores\n• Not start or end with hyphens/underscores", null, 'HTML');
            return;
        }

        // Check if old parameter exists
        $userId = getUserIdByCustomParameter($oldParam);
        if (!$userId) {
            sendMessage($chatId, "❌ <b>Parameter Not Found</b>\n\nThe custom parameter '<code>{$oldParam}</code>' does not exist.", null, 'HTML');
            return;
        }

        // Check if new parameter already exists (and it's not the same as old)
        if ($oldParam !== $newParam && customParameterExists($newParam)) {
            sendMessage($chatId, "❌ <b>Parameter Already Exists</b>\n\nThe custom parameter '<code>{$newParam}</code>' is already in use.\n\nPlease choose a different parameter.", null, 'HTML');
            return;
        }

        // Update the custom referral link
        $result = updateCustomReferralLink($oldParam, $newParam);
        
        if ($result) {
            $targetUser = getUser($userId);
            $userName = htmlspecialchars($targetUser['first_name']);
            $newCustomLink = "https://t.me/" . BOT_USERNAME . "?start={$newParam}";
            
            $message = "✅ <b>Custom Referral Link Updated</b>\n\n";
            $message .= "👤 <b>User:</b> {$userName} (ID: <code>{$userId}</code>)\n";
            $message .= "🔗 <b>Old Parameter:</b> <code>{$oldParam}</code>\n";
            $message .= "🔗 <b>New Parameter:</b> <code>{$newParam}</code>\n";
            $message .= "🌐 <b>New Link:</b>\n<code>{$newCustomLink}</code>\n\n";
            $message .= "📋 <i>The old parameter is no longer valid</i>";
            
            sendMessage($chatId, $message, null, 'HTML');
            
            // Log the action
            error_log("Custom referral link updated: {$oldParam} -> {$newParam} for user {$userId} by admin {$chatId}");
        } else {
            sendMessage($chatId, "❌ <b>Update Failed</b>\n\nFailed to update custom referral link. Please try again.", null, 'HTML');
        }

    } catch (Exception $e) {
        error_log("Error updating custom referral link: " . $e->getMessage());
        sendMessage($chatId, "❌ <b>Error</b>\n\nAn error occurred while updating the custom referral link.", null, 'HTML');
    }
}

/**
 * Delete a custom referral link
 */
function deleteCustomReferralLink($chatId, $customParam) {
    try {
        // Check if parameter exists
        $userId = getUserIdByCustomParameter($customParam);
        if (!$userId) {
            sendMessage($chatId, "❌ <b>Parameter Not Found</b>\n\nThe custom parameter '<code>{$customParam}</code>' does not exist.", null, 'HTML');
            return;
        }

        // Delete the custom referral link
        $result = removeCustomReferralLink($customParam);
        
        if ($result) {
            $targetUser = getUser($userId);
            $userName = htmlspecialchars($targetUser['first_name']);
            $defaultLink = "https://t.me/" . BOT_USERNAME . "?start={$userId}";
            
            $message = "✅ <b>Custom Referral Link Deleted</b>\n\n";
            $message .= "👤 <b>User:</b> {$userName} (ID: <code>{$userId}</code>)\n";
            $message .= "🗑 <b>Deleted Parameter:</b> <code>{$customParam}</code>\n";
            $message .= "🔄 <b>Reverted to Default:</b>\n<code>{$defaultLink}</code>\n\n";
            $message .= "📋 <i>User now uses the default numeric referral link</i>";
            
            sendMessage($chatId, $message, null, 'HTML');
            
            // Log the action
            error_log("Custom referral link deleted: {$customParam} for user {$userId} by admin {$chatId}");
        } else {
            sendMessage($chatId, "❌ <b>Deletion Failed</b>\n\nFailed to delete custom referral link. Please try again.", null, 'HTML');
        }

    } catch (Exception $e) {
        error_log("Error deleting custom referral link: " . $e->getMessage());
        sendMessage($chatId, "❌ <b>Error</b>\n\nAn error occurred while deleting the custom referral link.", null, 'HTML');
    }
}

/**
 * View custom links for a specific user
 */
function viewUserCustomLinks($chatId, $userId) {
    try {
        $targetUser = getUser($userId);
        if (!$targetUser) {
            sendMessage($chatId, "❌ <b>User Not Found</b>\n\nUser ID '<code>{$userId}</code>' does not exist in the system.", null, 'HTML');
            return;
        }

        $customLinks = getCustomReferralLinksByUser($userId);
        $userName = htmlspecialchars($targetUser['first_name']);
        
        $message = "👤 <b>Custom Referral Links for {$userName}</b>\n";
        $message .= "ℹ️ <b>User ID:</b> <code>{$userId}</code>\n\n";
        
        if (empty($customLinks)) {
            $defaultLink = "https://t.me/" . BOT_USERNAME . "?start={$userId}";
            $message .= "📋 <b>No custom links found</b>\n\n";
            $message .= "🔗 <b>Default Link:</b>\n<code>{$defaultLink}</code>";
        } else {
            $message .= "🔗 <b>Custom Links:</b>\n";
            foreach ($customLinks as $link) {
                $customLink = "https://t.me/" . BOT_USERNAME . "?start={$link['custom_parameter']}";
                $createdDate = date('M d, Y', strtotime($link['created_at']));
                $message .= "• <code>{$link['custom_parameter']}</code> (Created: {$createdDate})\n";
                $message .= "  <code>{$customLink}</code>\n\n";
            }
            
            $defaultLink = "https://t.me/" . BOT_USERNAME . "?start={$userId}";
            $message .= "🔗 <b>Default Link:</b>\n<code>{$defaultLink}</code>";
        }
        
        sendMessage($chatId, $message, null, 'HTML');

    } catch (Exception $e) {
        error_log("Error viewing user custom links: " . $e->getMessage());
        sendMessage($chatId, "❌ <b>Error</b>\n\nAn error occurred while retrieving custom links.", null, 'HTML');
    }
}

/**
 * Show list of all custom referral links
 */
function showCustomReferralList($chatId) {
    try {
        $customLinks = getAllCustomReferralLinks();
        
        $message = "📋 <b>All Custom Referral Links</b>\n\n";
        
        if (empty($customLinks)) {
            $message .= "❌ <b>No custom links found</b>\n\n";
            $message .= "Use <code>/customref create [parameter] [user_id]</code> to create the first custom link.";
        } else {
            $message .= "📊 <b>Total Custom Links:</b> " . count($customLinks) . "\n\n";
            
            foreach ($customLinks as $link) {
                $user = getUser($link['user_id']);
                $userName = $user ? htmlspecialchars($user['first_name']) : 'Unknown User';
                $customLink = "https://t.me/" . BOT_USERNAME . "?start={$link['custom_parameter']}";
                $createdDate = date('M d, Y', strtotime($link['created_at']));
                
                $message .= "🔗 <code>{$link['custom_parameter']}</code>\n";
                $message .= "👤 {$userName} (ID: <code>{$link['user_id']}</code>)\n";
                $message .= "📅 Created: {$createdDate}\n";
                $message .= "<code>{$customLink}</code>\n\n";
            }
        }
        
        sendMessage($chatId, $message, null, 'HTML');

    } catch (Exception $e) {
        error_log("Error showing custom referral list: " . $e->getMessage());
        sendMessage($chatId, "❌ <b>Error</b>\n\nAn error occurred while retrieving the custom referral list.", null, 'HTML');
    }
}

/**
 * Show custom referral management interface (for admin panel button)
 */
function showCustomReferralManagement($userId, $chatId) {
    // Check if user is admin
    if (!isAdmin($userId)) {
        sendMessage($chatId, "❌ <b>Access Denied</b>\n\nThis feature is only available to administrators.", null, 'HTML');
        return;
    }

    $customLinks = getAllCustomReferralLinks();
    $totalLinks = count($customLinks);

    $message = "🔗 <b>Custom Referral Link Management</b>\n\n";
    $message .= "📊 <b>Total Custom Links:</b> {$totalLinks}\n\n";

    if ($totalLinks > 0) {
        $message .= "📋 <b>Recent Links:</b>\n";
        $recentLinks = array_slice($customLinks, -5); // Show last 5 links

        foreach ($recentLinks as $link) {
            $user = getUser($link['user_id']);
            $userName = $user ? htmlspecialchars($user['first_name']) : 'Unknown User';
            $message .= "• <code>{$link['custom_parameter']}</code> → {$userName}\n";
        }

        if ($totalLinks > 5) {
            $message .= "• ... and " . ($totalLinks - 5) . " more\n";
        }
    } else {
        $message .= "❌ <b>No custom links found</b>\n";
    }

    $message .= "\n📝 <b>Quick Commands:</b>\n";
    $message .= "• <code>/customref list</code> - View all links\n";
    $message .= "• <code>/customref create [param] [user_id]</code> - Create link\n";
    $message .= "• <code>/customref help</code> - Full command list\n\n";
    $message .= "💡 <i>Use commands in chat for detailed management</i>";

    $keyboard = [
        'inline_keyboard' => [
            [
                ['text' => '📋 View All Links', 'callback_data' => 'customref_list'],
                ['text' => '❓ Help', 'callback_data' => 'customref_help']
            ],
            [
                ['text' => '↩️ Back to Admin', 'callback_data' => 'admin']
            ]
        ]
    ];

    sendMessage($chatId, $message, $keyboard, 'HTML');
}

/**
 * Handle custom referral callback actions
 */
function handleCustomReferralCallback($userId, $chatId, $action) {
    // Check if user is admin
    if (!isAdmin($userId)) {
        sendMessage($chatId, "❌ <b>Access Denied</b>\n\nThis feature is only available to administrators.", null, 'HTML');
        return;
    }

    switch ($action) {
        case 'list':
            showCustomReferralList($chatId);
            break;
        case 'help':
            showCustomReferralHelp($chatId);
            break;
        default:
            showCustomReferralManagement($userId, $chatId);
            break;
    }
}

/**
 * Show help for custom referral commands
 */
function showCustomReferralHelp($chatId) {
    $message = "🔗 <b>Custom Referral Link Management</b>\n\n";
    $message .= "📋 <b>Available Commands:</b>\n\n";
    $message .= "<code>/customref list</code>\n";
    $message .= "• Show all custom referral links\n\n";
    $message .= "<code>/customref create [parameter] [user_id]</code>\n";
    $message .= "• Create new custom link\n";
    $message .= "• Example: <code>/customref create free-44 123456789</code>\n\n";
    $message .= "<code>/customref edit [old_param] [new_param]</code>\n";
    $message .= "• Update existing custom parameter\n";
    $message .= "• Example: <code>/customref edit free-44 premium-offer</code>\n\n";
    $message .= "<code>/customref delete [parameter]</code>\n";
    $message .= "• Delete custom link (reverts to default)\n";
    $message .= "• Example: <code>/customref delete free-44</code>\n\n";
    $message .= "<code>/customref view [user_id]</code>\n";
    $message .= "• View all links for specific user\n";
    $message .= "• Example: <code>/customref view 123456789</code>\n\n";
    $message .= "📝 <b>Parameter Rules:</b>\n";
    $message .= "• 3-30 characters long\n";
    $message .= "• Letters, numbers, hyphens, underscores only\n";
    $message .= "• Cannot start/end with hyphens or underscores\n";
    $message .= "• Must be unique across the system";
    
    sendMessage($chatId, $message, null, 'HTML');
}

/**
 * Validate custom parameter format
 */
function isValidCustomParameter($param) {
    // Check length (3-30 characters)
    if (strlen($param) < 3 || strlen($param) > 30) {
        return false;
    }
    
    // Check format: alphanumeric, hyphens, underscores only
    if (!preg_match('/^[a-zA-Z0-9_-]+$/', $param)) {
        return false;
    }
    
    // Cannot start or end with hyphens or underscores
    if (preg_match('/^[-_]|[-_]$/', $param)) {
        return false;
    }
    
    // Cannot be purely numeric (to avoid conflicts with user IDs)
    if (is_numeric($param)) {
        return false;
    }
    
    return true;
}
?>
