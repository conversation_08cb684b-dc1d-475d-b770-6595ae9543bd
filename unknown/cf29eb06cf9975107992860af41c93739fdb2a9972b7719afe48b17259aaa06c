<?php
require_once 'config.php';
require_once 'database_functions.php';

// Constants for messages (BANNED_TEXT and MAINTENANCE_TEXT are defined in core_functions.php)

function handleStartCommand($userId, $chatId, $firstName, $lastName, $username, $text) {
    // Check rate limit
    if (!checkRateLimit($userId)) {
        sendMessage($chatId, "⚠️ Too many requests. Please wait a moment before trying again.");
        return;
    }

    // Extract referral ID from /start command
    $referralId = 'None';
    if (preg_match('/\/start\s+(.+)/', $text, $matches)) {
        $referralParam = $matches[1];

        // Check if it's a custom referral parameter
        $customReferralUserId = getUserIdByCustomParameter($referralParam);
        if ($customReferralUserId) {
            // It's a custom parameter, use the mapped user ID
            $referralId = $customReferralUserId;
            error_log("Custom referral parameter used: {$referralParam} -> User ID: {$customReferralUserId}");
        } else {
            // It's either a numeric user ID or an invalid parameter
            $referralId = $referralParam;
        }
    }

    // Check if user exists
    $user = getUser($userId);

    if (!$user) {
        // Create new user
        createUser($userId, $firstName, $lastName, $username, $referralId);

        // Send notification to admin logs
        $privateLogsChannel = getBotConfig('private_logs_channel');
        if (!empty($privateLogsChannel)) {
            $totalUsers = getTotalUsers();
            $botUsername = getBotConfig('bot_username');

            $logMessage = "<b>🆕 New user notification\n\n🧒 Name : {$firstName}\n🤫 Username : @{$username}\n🔗 User's link : <a href=\"tg://user?id={$userId}\">Link</a>\n🆔 User's ID :</b> <code>{$userId}</code>\n\n<b>🤩 Total users :</b> <code>{$totalUsers}</code>\n\n<b>🤖 Bot : @{$botUsername}</b>";

            sendMessage($privateLogsChannel, $logMessage, null, 'HTML', true);
        }

        // Notify referrer if applicable
        if ($referralId !== 'None' && isValidTelegramId($referralId)) {
            $referrerUser = getUser($referralId);
            if ($referrerUser) {
                // Send notification to referrer about the new referral
                $notificationResult = sendMessage($referralId, "👏You invited <b>{$firstName}</b>!\n<blockquote><b>You Will Receive Money, After He/She Joins Channel & Clicks GET MONEY</b></blockquote>", null, 'HTML');
                error_log("[ReferralNotificationDebug] Notification sent to referrer {$referralId} about new user {$firstName} (ID: {$userId}): " . ($notificationResult ? 'success' : 'failed'));
            } else {
                error_log("[ReferralNotificationDebug] Referrer {$referralId} not found for new user {$userId}");
            }
        }

        // Reload user data
        $user = getUser($userId);
    }

    // Check if user is banned
    if ($user['banned']) {
        sendMessage($chatId, BANNED_TEXT, null, 'HTML');
        return;
    }

    // Check maintenance status
    if (isMaintenanceMode()) {
        sendMessage($chatId, MAINTENANCE_TEXT, null, 'HTML');
        return;
    }

    // Send welcome message
    $joiningBonusAmount = getBotConfig('joining_bonus_amount'); // This is for internal use only
    $mainChannel = getBotConfig('main_channel');

    $welcomeMessage = getWelcomeMessage($joiningBonusAmount, $mainChannel); // Function now uses generic amounts internally

    $keyboard = [
        'inline_keyboard' => [
            [['text' => '💰GET MONEY💰', 'callback_data' => 'joined']]
        ]
    ];

    sendMessage($chatId, $welcomeMessage, $keyboard, 'HTML', true);

    // Send reminder message
    $reminderMessage = "<b>💡You Must <a href=\"https://t.me/{$mainChannel}\">Join Our Channel</a> Before Clicking On [💰GET MONEY💰]</b>";
    sendMessage($chatId, $reminderMessage, null, 'HTML', true);
}

function handleAdminCommand($userId, $chatId) {
    if (!isAdmin($userId)) {
        return;
    }

    $botUsername = getBotConfig('bot_username');
    $mainChannel = getBotConfig('main_channel');
    $privateLogsChannel = getBotConfig('private_logs_channel');
    $maintenanceStatus = getBotConfig('maintenance_mode');
    $otpApiKey = getBotConfig('otp_api_key');
    $perReferAmount = getBotConfig('per_refer_amount');
    $joiningBonusAmount = getBotConfig('joining_bonus_amount');

    // Get actual configured ranges for admin display
    $referralRange = getReferralAmountRange();
    $bonusRange = getJoiningBonusAmountRange();
    $referralRangeDisplay = "₹{$referralRange['min']}-₹{$referralRange['max']}";
    $bonusRangeDisplay = "₹{$bonusRange['min']}-₹{$bonusRange['max']}";

    $adminMessage = "<b>👋 Hello bro, welcome to the admin panel of @{$botUsername}.\n\n🏘️ Main channel : @{$mainChannel}\n🤫 Private logs channel : {$privateLogsChannel}\n⚙️ Maintenance status :</b> <code>{$maintenanceStatus}</code>\n<b>🔐 OTP website API key :</b> <code>{$otpApiKey}</code>\n<b>🧑‍🤝‍🧑 Per refer amount :</b> <code>{$referralRangeDisplay}</code>\n<b>💸 Joining bonus amount :</b> <code>{$bonusRangeDisplay}</code>\n\n<b>⚠️ Note :</b> <i>Send /admin again after setting all the things to see the changes.</i>";

    $keyboard = [
        'inline_keyboard' => [
            [
                ['text' => '➕ Add balance', 'callback_data' => 'add'],
                ['text' => '➖ Remove balance', 'callback_data' => 'remove']
            ],
            [
                ['text' => '🚫 Ban user', 'callback_data' => 'ban'],
                ['text' => '✔️ Unban user', 'callback_data' => 'unban']
            ],
            [
                ['text' => '🏘️ Set / Change main channel', 'callback_data' => 'mainChannel']
            ],
            [
                ['text' => '🤫 Set / Change private logs channel', 'callback_data' => 'privateLogsChannel']
            ],
            [
                ['text' => '➕ Add Force Sub Channel', 'callback_data' => 'addForceSubChannel'],
                ['text' => '➖ Remove Force Sub Channel', 'callback_data' => 'removeForceSubChannel']
            ],
            [
                ['text' => '📋 View Force Sub Channels', 'callback_data' => 'viewForceSubChannels']
            ],
            [
                ['text' => '⚙️ Set / Change maintenance status', 'callback_data' => 'maintenanceStatus']
            ],
            [
                ['text' => '🔐 Set / Change OTP website API key', 'callback_data' => 'OTPWebsiteAPIKey']
            ],
            [
                ['text' => '🧑‍🤝‍🧑 Set / Change per refer amount', 'callback_data' => 'perReferAmount']
            ],
            [
                ['text' => '💸 Set / Change joining bonus amount', 'callback_data' => 'joiningBonusAmount']
            ],
            [
                ['text' => '🏛️ Withdrawal Tax & Control', 'callback_data' => 'withdrawal_settings']
            ],
            [
                ['text' => '🔍 Check user\'s record', 'callback_data' => 'checkUserRecord']
            ],
            [
                ['text' => '🏧 Pass user\'s withdrawal', 'callback_data' => 'passUserWithdrawal']
            ],
            [
                ['text' => '🏧 Fail user\'s withdrawal', 'callback_data' => 'failUserWithdrawal']
            ],
            [
                ['text' => '🎁 Broadcast gift button', 'callback_data' => 'broadcastGiftButton']
            ],
            [
                ['text' => '📝 Broadcast text', 'callback_data' => 'broadcastText']
            ],
            [
                ['text' => '📋 Manage Tasks', 'callback_data' => 'manageTasks'],
                ['text' => '➕ Add New Task', 'callback_data' => 'addNewTask']
            ],
            [
                ['text' => '🎫 Generate Gift Code', 'callback_data' => 'generateGiftCode']
            ],
            [
                ['text' => '🏆 Configure Level Rewards', 'callback_data' => 'configureLevelRewards'],
                ['text' => '🔄 Toggle Level Bonus', 'callback_data' => 'toggleLevelBonus']
            ],
            [
                ['text' => '🔗 Custom Referral Links', 'callback_data' => 'customReferralLinks']
            ]
        ]
    ];

    sendMessage($chatId, $adminMessage, $keyboard, 'HTML');
}

function handleJoinedChannel($userId, $chatId, $messageId, $firstName) {
    // Check maintenance and ban status
    if (isMaintenanceMode()) {
        sendMessage($chatId, MAINTENANCE_TEXT, null, 'HTML');
        return;
    }

    $user = getUser($userId);
    if ($user['banned']) {
        sendMessage($chatId, BANNED_TEXT, null, 'HTML');
        return;
    }

    // Check if user joined all required channels
    $membershipCheck = verifyUserMembershipInAllChannels($userId);

    if (!$membershipCheck['all_joined']) {
        $missingChannels = $membershipCheck['missing_channels'];

        if (empty($missingChannels)) {
            // Fallback to main channel check if no force sub channels configured
            $mainChannel = getBotConfig('main_channel');
            $chatMember = getChatMember($mainChannel, $userId);

            if (!$chatMember || !in_array($chatMember['status'], ['member', 'administrator', 'creator'])) {
                sendMessage($chatId, "<i>🚫 You must join our channel before you can get money.</i>", null, 'HTML');
                return;
            }
        } else {
            $message .= "<b>📢 Missing channels:</b>\n\n";

            foreach ($missingChannels as $index => $channel) {
                $channelUsername = $channel['username'] ?? str_replace('@', '', $channel['id']);
                $channelTitle = $channel['title'] ?? $channelUsername;
                $number = $index + 1;

                $message .= "<b>{$number}.</b> <a href=\"https://t.me/{$channelUsername}\">{$channelTitle}</a>\n";
            }

            $message .= "\n<b>✅ Join all channels above and try again!</b>";

            sendMessage($chatId, $message, null, 'HTML');
            return;
        }
    }

    // Delete the original message
    deleteMessage($chatId, $messageId);

    // Handle referral reward
    $referredBy = $user['referred_by'];
    error_log("[ReferralRewardDebug] User {$userId} ({$firstName}) - referred_by: '{$referredBy}', already_referred: " . ($user['referred'] ? 'true' : 'false'));

    if ($referredBy !== 'None' && $referredBy !== '' && !$user['referred']) {
        // Check if referrer exists
        $referrerUser = getUser($referredBy);
        if (!$referrerUser) {
            error_log("[ReferralRewardDebug] Referrer {$referredBy} not found for user {$userId}");
        } else {
            $amountToCredit = generateReferralAmount(); // Use configurable range instead of hardcoded values
            error_log("[ReferralRewardDebug] Processing referral reward: referrer={$referredBy}, referred={$userId}, amount={$amountToCredit}");

            // Update referrer balance and mark user as referred
            $rewardResult = updateReferralReward($referredBy, $userId, $amountToCredit);
            $markResult = markUserReferred($userId);

            error_log("[ReferralRewardDebug] Reward update result: " . ($rewardResult ? 'success' : 'failed') . ", Mark referred result: " . ($markResult ? 'success' : 'failed'));

            if ($rewardResult && $markResult) {
                // Get updated referrer data
                $referrerUser = getUser($referredBy);
                error_log("[ReferralRewardDebug] Referrer {$referredBy} new balance: ₹{$referrerUser['balance']}");

                // Notify referrer
                $userDisplayBonus = getUserFacingJoiningBonusAmount();
                $referrerKeyboard = [
                    'inline_keyboard' => [
                        [
                            ['text' => '👥 Invite friends', 'url' => "https://t.me/share/url?text={$referrerUser['referral_link']}\nI've Got Up To ₹{$userDisplayBonus}! Click URL To Join & Make Money Now!"]
                        ],
                        [
                            ['text' => '💰 My Wallet', 'callback_data' => 'myWallet']
                        ]
                    ]
                ];

                $notificationResult = sendMessage($referredBy, "🎉Invite {$firstName} 🔍  successfully! You get <b>₹{$amountToCredit}! 🎁\n\n💵Balance:₹{$referrerUser['balance']}.00</b>", $referrerKeyboard, 'HTML', true);
                error_log("[ReferralRewardDebug] Notification sent to referrer {$referredBy}: " . ($notificationResult ? 'success' : 'failed'));
            } else {
                error_log("[ReferralRewardDebug] Failed to process referral reward for user {$userId}");
            }
        }
    } else {
        error_log("[ReferralRewardDebug] Skipping referral reward - either no referrer or already processed");
    }

    // Handle joining bonus
    if ($user['joining_bonus_got'] == 0) {
        $joiningBonusToCredit = generateJoiningBonusAmount(); // Use configurable range instead of hardcoded values
        $minWithdrawal = getMinWithdrawalAmount();
        $toWithdraw = $minWithdrawal - $joiningBonusToCredit;

        updateJoiningBonus($userId, $joiningBonusToCredit);

        sendMessage($chatId, "🎉Congratulation! You get ₹{$joiningBonusToCredit}!\n💵Withdraw still needs ₹{$toWithdraw}.\nCash out in [💰My wallet]");
    }

    // Send invitation message
    $user = getUser($userId); // Reload user data

    // Use generic amounts for user-facing message (actual amounts are handled inside getInvitationMessage)
    $inviteMessage = getInvitationMessage(null, null, $user['referral_link']);

    $userDisplayBonus = getUserFacingJoiningBonusAmount();
    $inviteKeyboard = [
        'inline_keyboard' => [
            [
                ['text' => '👥 Invite friends', 'url' => "https://t.me/share/url?text={$user['referral_link']}\nI've Got Up To ₹{$userDisplayBonus}! Click URL To Join & Make Money Now!"]
            ],
            [
                ['text' => '💰 My Wallet', 'callback_data' => 'myWallet']
            ]
        ]
    ];

    sendMessage($chatId, $inviteMessage, $inviteKeyboard, 'HTML', true);
}

function handleMyWallet($userId, $chatId, $messageId) {
    // Check maintenance and ban status
    $adminSettings = getAdminSettings();
    if ($adminSettings['maintenance_status'] === 'On') {
        sendMessage($chatId, MAINTENANCE_TEXT, null, 'HTML');
        return;
    }

    $user = getUser($userId);
    if ($user['banned']) {
        sendMessage($chatId, BANNED_TEXT, null, 'HTML');
        return;
    }

    // Get withdrawal method and display status
    $withdrawalMethod = $user['withdrawal_method'] ?? 'bank';
    $methodStatus = '';

    if ($withdrawalMethod === 'usdt') {
        $methodStatus = "✅ USDT (Binance ID)";
    } else {
        $methodStatus = "✅ Bank Account";
    }

    $walletMessage = "💰 <b>My Wallet</b>\n\n";
    $walletMessage .= "💵 <b>Balance:</b> ₹{$user['balance']}\n";
    $walletMessage .= "✅ <b>Successful Withdraw:</b> ₹{$user['successful_withdraw']}\n";
    $walletMessage .= "⏳ <b>Under Review:</b> ₹{$user['withdraw_under_review']}\n\n";
    $walletMessage .= "🔧 <b>Withdrawal Method:</b> {$methodStatus}";

    $walletKeyboard = [
        'inline_keyboard' => [
            [
                ['text' => '🏧 Cash out', 'callback_data' => 'cashOut']
            ],
            [
                ['text' => '⚙️ Set account info', 'callback_data' => 'setAccountInfo']
            ],
            [
                ['text' => '📊 Promotion report', 'callback_data' => 'promotionReport']
            ],
            [
                ['text' => '📝 Withdrawal record', 'callback_data' => 'withdrawalRecord']
            ],
            [
                ['text' => '🎁 Extra Rewards', 'callback_data' => 'extraRewards']
            ],
            [
                ['text' => '↩️ Back', 'callback_data' => 'joined']
            ]
        ]
    ];

    smartNavigateToTextMessage($chatId, $messageId, $walletMessage, $walletKeyboard);
}

function handleCashOut($userId, $chatId, $messageId) {
    // Check maintenance and ban status
    if (isMaintenanceMode()) {
        sendMessage($chatId, MAINTENANCE_TEXT, null, 'HTML');
        return;
    }

    $user = getUser($userId);
    if ($user['banned']) {
        sendMessage($chatId, BANNED_TEXT, null, 'HTML');
        return;
    }

    // Check if withdrawals are globally enabled
    if (!isWithdrawalEnabled()) {
        $disabledMessage = "❌ <b>Withdrawals are currently not available at this moment.</b>\n\n";
        $disabledMessage .= "Please try again later or contact our support for more information 
        @instantohelpbot\n\n";
        $disabledMessage .= "💰 <b>Your Current Balance:</b> ₹{$user['balance']}";

        $backKeyboard = [
            'inline_keyboard' => [
                [
                    ['text' => '↩️Back', 'callback_data' => 'myWallet']
                ]
            ]
        ];

        smartNavigateToTextMessage($chatId, $messageId, $disabledMessage, $backKeyboard, 'HTML');
        return;
    }

    // Get withdrawal settings for tax information
    $withdrawalSettings = getWithdrawalSettings();

    $cashOutMessage = "💰 <b>Withdrawal Center</b>\n\n";
    $cashOutMessage .= "💵 <b>Your Balance:</b> ₹{$user['balance']}\n\n";

    // Show tax information if applicable
    if ($withdrawalSettings['tax_type'] !== 'none' && $withdrawalSettings['tax_amount'] > 0) {
        $cashOutMessage .= "🏛️ <b>Withdrawal Tax:</b> ";
        if ($withdrawalSettings['tax_type'] === 'fixed') {
            $cashOutMessage .= "₹{$withdrawalSettings['tax_amount']} per withdrawal\n";
        } elseif ($withdrawalSettings['tax_type'] === 'percentage') {
            $cashOutMessage .= "{$withdrawalSettings['tax_amount']}% of withdrawal amount\n";
        }
        $cashOutMessage .= "\n";
    }

    $cashOutMessage .= "Please select the withdrawal amount:";

    // Build withdrawal keyboard from configuration
    $withdrawalAmounts = getWithdrawalAmounts();
    $keyboard = [];
    $row = [];

    foreach ($withdrawalAmounts as $amount) {
        $row[] = ['text' => "₹{$amount}", 'callback_data' => "withdraw {$amount}"];

        // Create new row after every 3 buttons
        if (count($row) === 3) {
            $keyboard[] = $row;
            $row = [];
        }
    }

    // Add remaining buttons if any
    if (!empty($row)) {
        $keyboard[] = $row;
    }

    // Add back button
    $keyboard[] = [['text' => '↩️ Back', 'callback_data' => 'myWallet']];

    $cashOutKeyboard = ['inline_keyboard' => $keyboard];

    smartNavigateToTextMessage($chatId, $messageId, $cashOutMessage, $cashOutKeyboard);
}

function handleWithdrawAmount($userId, $chatId, $messageId, $amount) {
    // Check maintenance and ban status
    if (isMaintenanceMode()) {
        sendMessage($chatId, MAINTENANCE_TEXT, null, 'HTML');
        return;
    }

    $user = getUser($userId);
    if ($user['banned']) {
        sendMessage($chatId, BANNED_TEXT, null, 'HTML');
        return;
    }

    // Check if withdrawals are globally enabled
    if (!isWithdrawalEnabled()) {
        sendMessage($chatId, "❌ <b>Withdrawals are currently disabled by the administrator.</b>\n\nPlease try again later or contact support for more information.", null, 'HTML');
        return;
    }

    // Check account info based on withdrawal method
    $withdrawalMethod = $user['withdrawal_method'] ?? 'bank';

    if ($withdrawalMethod === 'usdt') {
        // Check Binance ID (support both old and new field names)
        $binanceId = $user['binance_id'] ?? $user['usdt_address'] ?? '';
        if (empty($binanceId)) {
            sendMessage($chatId, "<i>❌ Please set your Binance ID first by clicking 'Set account info'.</i>", null, 'HTML');
            return;
        }

        // Basic Binance ID validation - just ensure it's not empty
        if (empty(trim($binanceId))) {
            sendMessage($chatId, "<i>❌ Your Binance ID is empty. Please update it by clicking 'Set account info'.</i>", null, 'HTML');
            return;
        }
    } else {
        // Check bank account info
        if (empty($user['name']) || empty($user['ifsc']) || empty($user['email']) ||
            empty($user['account_number']) || empty($user['mobile_number'])) {
            sendMessage($chatId, "<i>❌ Please set your bank account info first by clicking 'Set account info'.</i>", null, 'HTML');
            return;
        }
    }

    // Check if user has withdrawal under review
    if ($user['withdraw_under_review'] > 0) {
        sendMessage($chatId, "<i>❌ You have already withdrawal under review.</i>", null, 'HTML');
        return;
    }

    // Calculate withdrawal tax
    $taxCalculation = calculateWithdrawalTax($amount);

    // Check if user has sufficient balance for the original amount
    if ($user['balance'] < $amount) {
        sendMessage($chatId, "<i>❌ Insufficient balance.</i>", null, 'HTML');
        return;
    }

    // Check if final amount after tax is greater than 0
    if ($taxCalculation['final_amount'] <= 0) {
        sendMessage($chatId, "❌ <b>Withdrawal amount too low after tax deduction.</b>\n\nPlease try a higher amount.", null, 'HTML');
        return;
    }

    // Create withdrawal with original amount (tax will be handled in the system)
    if (createWithdrawal($userId, $amount)) {
        $currentDate = getCurrentDate();

        $withdrawalKeyboard = [
            'inline_keyboard' => [
                [
                    ['text' => '↩️Back', 'callback_data' => 'myWallet']
                ]
            ]
        ];

        // Prepare withdrawal confirmation message with tax breakdown
        $confirmationMessage = "✅ <b>Withdrawal Request Submitted</b>\n\n";
        $confirmationMessage .= "💵 <b>Requested Amount:</b> ₹{$amount}\n";

        if ($taxCalculation['tax_type'] !== 'none' && $taxCalculation['tax_amount'] > 0) {
            $confirmationMessage .= "🏛️ <b>Tax Deduction:</b> ₹" . number_format($taxCalculation['tax_amount'], 2);
            if ($taxCalculation['tax_type'] === 'percentage') {
                $confirmationMessage .= " ({$taxCalculation['tax_rate']}%)";
            }
            $confirmationMessage .= "\n";
            $confirmationMessage .= "💰 <b>Final Amount:</b> ₹" . number_format($taxCalculation['final_amount'], 2) . "\n";
        } else {
            $confirmationMessage .= "💰 <b>Final Amount:</b> ₹{$amount}\n";
        }

        $confirmationMessage .= "⏱️ <b>Date:</b> {$currentDate}\n";
        $confirmationMessage .= "📋 <b>Status:</b> Under review\n\n";
        $confirmationMessage .= "The result will be notified in 1-3 working days.";

        sendMessage($chatId, $confirmationMessage, $withdrawalKeyboard, 'HTML');

        // Notify admin directly (admin user ID: **********)
        $adminUserId = **********;
        $withdrawalMethod = $user['withdrawal_method'] ?? 'bank';
        $methodDisplay = ($withdrawalMethod === 'usdt') ? 'USDT (Binance ID)' : 'Bank Account';

        $adminMessage = "<b>🆕 New withdrawal requested by {$user['first_name']}\n\nℹ️ User ID :</b> <code>{$userId}</code>\n<b>💵 Requested Amount :</b> <code>₹{$amount}</code>\n<b>🔧 Withdrawal Method :</b> {$methodDisplay}\n";

        if ($taxCalculation['tax_type'] !== 'none' && $taxCalculation['tax_amount'] > 0) {
            $adminMessage .= "<b>🏛️ Tax Deduction :</b> <code>₹" . number_format($taxCalculation['tax_amount'], 2) . "</code>";
            if ($taxCalculation['tax_type'] === 'percentage') {
                $adminMessage .= " ({$taxCalculation['tax_rate']}%)";
            }
            $adminMessage .= "\n";
            $adminMessage .= "<b>💰 Final Amount :</b> <code>₹" . number_format($taxCalculation['final_amount'], 2) . "</code>\n";
        } else {
            $adminMessage .= "<b>💰 Final Amount :</b> <code>₹{$amount}</code>\n";
        }

        if ($withdrawalMethod === 'usdt') {
            $binanceId = $user['binance_id'] ?? $user['usdt_address'] ?? '';
            $adminMessage .= "\n<b>👇 USDT Details :\n\n₿ Binance ID :</b> <code>{$binanceId}</code>\n\n<b>✔️ Use the buttons below to approve or reject this withdrawal request.</b>";
        } else {
            $adminMessage .= "\n<b>👇 Bank Account Details :\n\nName : {$user['name']}\nIFSC :</b> <code>{$user['ifsc']}</code>\n<b>Email : {$user['email']}\nAccount Number :</b> <code>{$user['account_number']}</code>\n<b>Mobile Number :</b> <code>{$user['mobile_number']}</code>\n\n<b>✔️ Use the buttons below to approve or reject this withdrawal request.</b>";
        }

        // Create inline keyboard for withdrawal approval
        $withdrawalKeyboard = [
            'inline_keyboard' => [
                [
                    ['text' => '✅ Approve', 'callback_data' => "approve_withdrawal_{$userId}"],
                    ['text' => '❌ Reject', 'callback_data' => "reject_withdrawal_{$userId}"]
                ]
            ]
        ];

        // Send withdrawal request notification directly to admin
        sendMessage($adminUserId, $adminMessage, $withdrawalKeyboard, 'HTML');

        // Post immediate confirmation to main channel that withdrawal request was received
        postWithdrawalRequestToMainChannel($user, $amount);
    } else {
        sendMessage($chatId, "<i>❌ Error processing withdrawal. Please try again.</i>", null, 'HTML');
    }
}

function handleSetAccountInfo($userId, $chatId, $messageId) {
    // Check maintenance and ban status
    $adminSettings = getAdminSettings();
    if ($adminSettings['maintenance_status'] === 'On') {
        sendMessage($chatId, MAINTENANCE_TEXT, null, 'HTML');
        return;
    }

    $user = getUser($userId);
    if ($user['banned']) {
        sendMessage($chatId, BANNED_TEXT, null, 'HTML');
        return;
    }

    // Show withdrawal method selection
    $methodMessage = "⚙️ <b>Select Withdrawal Method</b>\n\n";
    $methodMessage .= "Please choose your preferred withdrawal method:\n\n";
    $methodMessage .= "🏦 <b>Bank Account</b> - Traditional bank transfer\n";
    $methodMessage .= "₿ <b>USDT (Binance ID)</b> - Cryptocurrency withdrawal via Binance\n\n";
    $methodMessage .= "⚠️ You can change this method anytime by clicking 'Set Account Info' again.";

    $methodKeyboard = [
        'inline_keyboard' => [
            [
                ['text' => '🏦 Bank Account', 'callback_data' => 'withdrawal_method_bank']
            ],
            [
                ['text' => '₿ USDT (Binance ID)', 'callback_data' => 'withdrawal_method_usdt']
            ],
            [
                ['text' => '↩️ Back', 'callback_data' => 'myWallet']
            ]
        ]
    ];

    smartNavigateToTextMessage($chatId, $messageId, $methodMessage, $methodKeyboard, 'HTML');
}

function handleWithdrawalMethodSelection($userId, $chatId, $messageId, $method) {
    // Check maintenance and ban status
    $adminSettings = getAdminSettings();
    if ($adminSettings['maintenance_status'] === 'On') {
        sendMessage($chatId, MAINTENANCE_TEXT, null, 'HTML');
        return;
    }

    $user = getUser($userId);
    if ($user['banned']) {
        sendMessage($chatId, BANNED_TEXT, null, 'HTML');
        return;
    }

    // Set the withdrawal method
    updateAccountInfo($userId, 'withdrawal_method', $method);

    if ($method === 'bank') {
        // Show bank account setup
        showBankAccountSetup($userId, $chatId, $messageId);
    } else {
        // Show Binance ID setup
        showBinanceIdSetup($userId, $chatId, $messageId);
    }
}

function showBankAccountSetup($userId, $chatId, $messageId) {
    $user = getUser($userId);

    $accountMessage = "🏦 <b>Bank Account Setup</b>\n\n";
    $accountMessage .= "⚙️ Please set your bank account details for withdrawals.\n";
    $accountMessage .= "⚠️ Any incorrect information may result in failed withdrawal!\n\n";
    $accountMessage .= "✅ <b>Current Method:</b> Bank Account\n\n";
    $accountMessage .= "<b>Name:</b> {$user['name']}\n";
    $accountMessage .= "<b>IFSC:</b> {$user['ifsc']}\n";
    $accountMessage .= "<b>Email:</b> {$user['email']}\n";
    $accountMessage .= "<b>Account Number:</b> {$user['account_number']}\n";
    $accountMessage .= "<b>Mobile Number:</b> {$user['mobile_number']}";

    $accountKeyboard = [
        'inline_keyboard' => [
            [
                ['text' => '👤Name', 'callback_data' => 'set Name'],
                ['text' => 'ℹ️IFSC', 'callback_data' => 'set IFSC'],
                ['text' => '📧Email', 'callback_data' => 'set Email']
            ],
            [
                ['text' => '💳Account Number', 'callback_data' => 'set AccountNumber']
            ],
            [
                ['text' => '📱Mobile Number', 'callback_data' => 'set MobileNumber']
            ],
            [
                ['text' => '🔄 Change Method', 'callback_data' => 'setAccountInfo']
            ],
            [
                ['text' => '↩️ Back', 'callback_data' => 'myWallet']
            ]
        ]
    ];

    smartNavigateToTextMessage($chatId, $messageId, $accountMessage, $accountKeyboard, 'HTML');
}

function showBinanceIdSetup($userId, $chatId, $messageId) {
    $user = getUser($userId);

    $usdtMessage = "₿ <b>USDT (Binance ID) Setup</b>\n\n";
    $usdtMessage .= "⚙️ Please set your Binance ID for USDT withdrawals.\n";
    $usdtMessage .= "⚠️ Make sure to provide a valid Binance ID (email or phone number)!\n\n";
    $usdtMessage .= "✅ <b>Current Method:</b> USDT (Binance ID)\n\n";

    // Support both old and new field names for backward compatibility
    $binanceId = $user['binance_id'] ?? $user['usdt_address'] ?? '';
    $usdtMessage .= "<b>Binance ID:</b> " . ($binanceId ? $binanceId : '<i>Not set</i>') . "\n\n";
    $usdtMessage .= "💡 <b>Note:</b> Enter your Binance account email, phone number, or UID.";

    $usdtKeyboard = [
        'inline_keyboard' => [
            [
                ['text' => '₿ Set Binance ID', 'callback_data' => 'set USDTAddress']
            ],
            [
                ['text' => '🔄 Change Method', 'callback_data' => 'setAccountInfo']
            ],
            [
                ['text' => '↩️ Back', 'callback_data' => 'myWallet']
            ]
        ]
    ];

    smartNavigateToTextMessage($chatId, $messageId, $usdtMessage, $usdtKeyboard, 'HTML');
}

// Keep the old function name for backward compatibility
function showUSDTAddressSetup($userId, $chatId, $messageId) {
    showBinanceIdSetup($userId, $chatId, $messageId);
}

function handlePromotionReport($userId, $chatId, $messageId) {
    // Check maintenance and ban status
    $adminSettings = getAdminSettings();
    if ($adminSettings['maintenance_status'] === 'On') {
        sendMessage($chatId, MAINTENANCE_TEXT, null, 'HTML');
        return;
    }

    $user = getUser($userId);
    if ($user['banned']) {
        sendMessage($chatId, BANNED_TEXT, null, 'HTML');
        return;
    }

    $promotionReports = getPromotionReports($userId);
    $message = "👥 Promotion report :\n\n";

    foreach ($promotionReports as $report) {
        $message .= "💵 {$report['referred_user_name']} : ₹{$report['amount_got']}\n";
    }

    $backKeyboard = [
        'inline_keyboard' => [
            [
                ['text' => '↩️Back', 'callback_data' => 'myWallet']
            ]
        ]
    ];

    smartNavigateToTextMessage($chatId, $messageId, $message, $backKeyboard);
}

function handleWithdrawalRecord($userId, $chatId, $messageId) {
    // Check maintenance and ban status
    $adminSettings = getAdminSettings();
    if ($adminSettings['maintenance_status'] === 'On') {
        sendMessage($chatId, MAINTENANCE_TEXT, null, 'HTML');
        return;
    }

    $user = getUser($userId);
    if ($user['banned']) {
        sendMessage($chatId, BANNED_TEXT, null, 'HTML');
        return;
    }

    $withdrawalReports = getWithdrawalReports($userId);
    $message = "💰Withdrawal Records:\n\n";

    foreach ($withdrawalReports as $record) {
        $message .= "💵 ₹{$record['amount']} ⏱️ {$record['date']} - {$record['status']}\n";
    }

    $backKeyboard = [
        'inline_keyboard' => [
            [
                ['text' => '↩️Back', 'callback_data' => 'myWallet']
            ]
        ]
    ];

    smartNavigateToTextMessage($chatId, $messageId, $message, $backKeyboard);
}

/**
 * Calculate total referral earnings for a user
 */
function calculateUserReferralEarnings($userId) {
    $user = getUser($userId);
    if (!$user) {
        return 0;
    }

    $total = 0;

    if (STORAGE_MODE === 'json') {
        // For JSON mode, calculate from promotion_report array
        $promotionReports = $user['promotion_report'] ?? [];
        foreach ($promotionReports as $report) {
            $total += $report['amount_got'] ?? 0;
        }
    } else {
        // For MySQL mode, get from promotion_reports table
        $promotionReports = getPromotionReports($userId);
        foreach ($promotionReports as $report) {
            $total += $report['amount_got'] ?? 0;
        }
    }

    return $total;
}

/**
 * Post withdrawal request confirmation to main channel
 */
function postWithdrawalRequestToMainChannel($user, $amount) {
    try {
        $firstName = htmlspecialchars($user['first_name'] ?? 'User');
        $lastName = htmlspecialchars($user['last_name'] ?? '');
        // Create full name, but if lastName is empty, just use firstName
        $fullName = !empty($lastName) ? trim($firstName . ' ' . $lastName) : $firstName;
        $mainChannelId = '@' . MAIN_CHANNEL;

        // Calculate user's total referral earnings
        $totalReferralEarnings = calculateUserReferralEarnings($user['user_id']);

        // Create the congratulatory withdrawal message with dynamic referral earnings
        $message = "<b>🎉Congratulations to 👤{$fullName} ☠ Get ₹{$totalReferralEarnings} By Invitation!💥💥💥</b>\n\n";
        $message .= "<b>🎁🎉EXTRA BONUS!🎁</b>\n";
        $message .= "<b>If you receive the withdrawal, send screenshot of payment to 👉@instantohelpbot, get extra bonus!</b>\n";
        $message .= "<b>The bonus will arrive within 3 days.</b>\n\n";
        $message .= "<b>✅🎉WELCOME TO OUR CHANNEL!</b>\n\n";
        $message .= "<b>GET UP TO ₹100 INSTANTLY!</b>\n";
        $message .= "<b>👇️👇️👇️ CLICK NOW!👇️👇👇️</b>\n\n";
        $message .= "<b><a href=\"https://t.me/InstantoPayBot?start=from_channel\">👉🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔👈</a></b>\n";
        $message .= "<b><a href=\"https://t.me/InstantoPayBot?start=from_channel\">👉🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔👈</a></b>\n";
        $message .= "<b><a href=\"https://t.me/InstantoPayBot?start=from_channel\">👉🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔👈</a></b>\n";
        $message .= "<b><a href=\"https://t.me/InstantoPayBot?start=from_channel\">👉🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔👈</a></b>\n";
        $message .= "<b><a href=\"https://t.me/InstantoPayBot?start=from_channel\">👉🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔👈</a></b>\n\n";
        $message .= "<b>👇️👇️👇️ CLICK NOW!👇️👇👇️</b>";

        // Create inline keyboard with the updated button
        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔥🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔',
                        'url' => 'https://t.me/InstantoPayBot?start=from_channel'
                    ]
                ]
            ]
        ];

        // Send message to main channel
        $result = sendMessage($mainChannelId, $message, $keyboard, 'HTML');

        if ($result) {
            error_log("Withdrawal request confirmation posted to main channel for user: {$user['user_id']}, amount: ₹{$amount}");
        } else {
            error_log("Failed to post withdrawal request confirmation to main channel for user: {$user['user_id']}");
        }

        return $result;

    } catch (Exception $e) {
        error_log("Error posting withdrawal request confirmation to main channel: " . $e->getMessage());
        return false;
    }
}
?>
