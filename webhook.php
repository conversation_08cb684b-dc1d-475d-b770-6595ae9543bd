<?php
// Fixed webhook without function redeclaration issues
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set content type
header('Content-Type: application/json');

try {
    // Load configuration first
    require_once 'config.php';

    // Load other required files
    require_once 'storage_abstraction.php';
    require_once 'database_functions.php';
    require_once 'bot_handlers.php';
    require_once 'admin_handlers.php';
    require_once 'user_account_handlers.php';

    // Get the update from Telegram
    $input = file_get_contents('php://input');

    // Log the update for debugging
    error_log("Webhook received: " . $input);

    // Handle empty input (might be a test request)
    if (empty($input)) {
        echo json_encode(['status' => 'webhook is working', 'timestamp' => date('Y-m-d H:i:s')]);
        exit;
    }

    $update = json_decode($input, true);

    // Validate the update
    if (!$update) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON']);
        exit;
    }

    // Clean old sessions periodically (1% chance)
    if (rand(1, 100) === 1) {
        cleanOldSessions();
    }

    // Handle different types of updates
    if (isset($update['message'])) {
        handleMessage($update['message']);
    } elseif (isset($update['callback_query'])) {
        handleCallbackQuery($update['callback_query']);
    }

    // Return success response
    http_response_code(200);
    echo json_encode(['ok' => true]);

} catch (Exception $e) {
    error_log("Webhook error: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());

    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}

function handleMessage($message) {
    $userId = $message['from']['id'];
    $chatId = $message['chat']['id'];
    $text = $message['text'] ?? '';
    $firstName = $message['from']['first_name'] ?? '';
    $lastName = $message['from']['last_name'] ?? '';
    $username = $message['from']['username'] ?? '';

    // Check if user is in a session (multi-step operation)
    $session = getUserSession($userId);

    if ($session) {
        handleSessionMessage($userId, $chatId, $text, $session);
        return;
    }

    // Handle commands
    if (strpos($text, '/start') === 0) {
        handleStartCommand($userId, $chatId, $firstName, $lastName, $username, $text);
    } elseif ($text === '/admin') {
        handleAdminCommand($userId, $chatId);
    } elseif ($text === '/rank') {
        require_once 'admin_handlers.php';
        handleRankCommand($userId, $chatId);
    } elseif ($text === '/cancel') {
        // Check if user has an active broadcast first
        $activeBroadcast = getActiveBroadcast($userId);
        if ($activeBroadcast && isAdmin($userId)) {
            // Cancel the active broadcast
            if (cancelBroadcast($activeBroadcast, $userId)) {
                sendMessage($chatId, "🚫 <b>Broadcast Cancelled</b>\n\nBroadcast ID: <code>" . substr($activeBroadcast, -8) . "</code>\n\nThe broadcast has been stopped and will complete processing current users.", null, 'HTML');
            } else {
                sendMessage($chatId, "❌ Failed to cancel broadcast. Please try again.");
            }
        } else {
            // Clear regular session
            clearUserSession($userId);
            sendMessage($chatId, "🚫 Process cancelled.");
        }
    } elseif (strpos($text, '/customref') === 0) {
        // Handle custom referral link management
        require_once 'custom_referral_handlers.php';
        $parts = explode(' ', $text);
        $command = $parts[1] ?? 'help';
        $parameters = array_slice($parts, 2);
        handleCustomReferralCommand($userId, $chatId, $command, $parameters);
    } elseif ($text === '/debug') {
        // Debug command for testing
        $debugInfo = "🔧 Debug Information:\n\n";
        $debugInfo .= "✅ Webhook is working\n";
        $debugInfo .= "✅ User ID: $userId\n";
        $debugInfo .= "✅ Chat ID: $chatId\n";
        $debugInfo .= "✅ Storage mode: " . STORAGE_MODE . "\n";
        $debugInfo .= "✅ Bot configured\n";
        $debugInfo .= "\nSend /start to test the bot!";

        sendMessage($chatId, $debugInfo);
    }
}

function handleCallbackQuery($callbackQuery) {
    $userId = $callbackQuery['from']['id'];
    $chatId = $callbackQuery['message']['chat']['id'];
    $messageId = $callbackQuery['message']['message_id'];
    $data = $callbackQuery['data'];
    $firstName = $callbackQuery['from']['first_name'] ?? '';
    $lastName = $callbackQuery['from']['last_name'] ?? '';

    // Check if this is a withdrawal approval/rejection callback (handled separately)
    $isWithdrawalCallback = (strpos($data, 'approve_withdrawal_') === 0 || strpos($data, 'reject_withdrawal_') === 0);

    // Answer callback query to remove loading state (except for withdrawal callbacks)
    if (!$isWithdrawalCallback) {
        telegramRequest('answerCallbackQuery', [
            'callback_query_id' => $callbackQuery['id']
        ]);
    }

    // Handle different callback data
    switch ($data) {
        case 'add':
            handleAddBalance($userId, $chatId);
            break;
        case 'remove':
            handleRemoveBalance($userId, $chatId);
            break;
        case 'ban':
            handleBanUser($userId, $chatId);
            break;
        case 'unban':
            handleUnbanUser($userId, $chatId);
            break;
        case 'mainChannel':
            handleSetMainChannel($userId, $chatId);
            break;
        case 'privateLogsChannel':
            handleSetPrivateLogsChannel($userId, $chatId);
            break;
        case 'maintenanceStatus':
            handleSetMaintenanceStatus($userId, $chatId);
            break;
        case 'OTPWebsiteAPIKey':
            handleSetOTPAPIKey($userId, $chatId);
            break;
        case 'perReferAmount':
            handleSetPerReferAmount($userId, $chatId);
            break;
        case 'joiningBonusAmount':
            handleSetJoiningBonusAmount($userId, $chatId);
            break;
        case 'checkUserRecord':
            handleCheckUserRecord($userId, $chatId);
            break;
        case 'passUserWithdrawal':
            handlePassUserWithdrawal($userId, $chatId);
            break;
        case 'failUserWithdrawal':
            handleFailUserWithdrawal($userId, $chatId);
            break;
        case 'broadcastGiftButton':
            handleBroadcastGiftButton($userId, $chatId);
            break;
        case 'broadcastText':
            handleBroadcastText($userId, $chatId);
            break;
        case 'addForceSubChannel':
            handleAddForceSubChannel($userId, $chatId);
            break;
        case 'removeForceSubChannel':
            handleRemoveForceSubChannel($userId, $chatId);
            break;
        case 'viewForceSubChannels':
            handleViewForceSubChannels($userId, $chatId);
            break;
        case 'withdrawal_settings':
            handleWithdrawalSettings($userId, $chatId);
            break;
        case 'toggle_withdrawal_status':
            handleToggleWithdrawalStatus($userId, $chatId);
            break;
        case 'configure_withdrawal_tax':
            handleConfigureWithdrawalTax($userId, $chatId);
            break;
        case 'set_tax_none':
            handleSetTaxType($userId, $chatId, 'none');
            break;
        case 'set_tax_fixed':
            handleSetTaxType($userId, $chatId, 'fixed');
            break;
        case 'set_tax_percentage':
            handleSetTaxType($userId, $chatId, 'percentage');
            break;
        case 'preview_withdrawal_tax':
            handlePreviewWithdrawalTax($userId, $chatId);
            break;
        case 'extraRewards':
            require_once 'extra_rewards_handlers.php';
            handleExtraRewards($userId, $chatId, $messageId);
            break;
        case 'taskRewards':
            require_once 'extra_rewards_handlers.php';
            handleTaskRewards($userId, $chatId, $messageId);
            break;
        case 'redeemGiftCode':
            require_once 'extra_rewards_handlers.php';
            handleRedeemGiftCode($userId, $chatId, $messageId);
            break;
        case 'manageTasks':
            require_once 'admin_task_handlers.php';
            handleManageTasks($userId, $chatId);
            break;
        case 'addNewTask':
            require_once 'admin_task_handlers.php';
            handleAddNewTask($userId, $chatId);
            break;
        case 'generateGiftCode':
            require_once 'admin_task_handlers.php';
            handleGenerateGiftCode($userId, $chatId);
            break;
        case 'viewPendingSubmissions':
            require_once 'admin_task_handlers.php';
            handleViewPendingSubmissions($userId, $chatId);
            break;
        case 'levelRewards':
            require_once 'extra_rewards_handlers.php';
            handleLevelRewards($userId, $chatId, $messageId);
            break;
        case 'configureLevelRewards':
            require_once 'admin_task_handlers.php';
            handleConfigureLevelRewards($userId, $chatId);
            break;
        case 'toggleLevelBonus':
            require_once 'admin_task_handlers.php';
            handleToggleLevelBonus($userId, $chatId);
            break;
        case 'admin':
            handleAdminCommand($userId, $chatId);
            break;
        case 'joined':
            handleJoinedChannel($userId, $chatId, $messageId, $firstName);
            break;
        case 'myWallet':
            handleMyWallet($userId, $chatId, $messageId);
            break;
        case 'cashOut':
            handleCashOut($userId, $chatId, $messageId);
            break;
        case 'setAccountInfo':
            handleSetAccountInfo($userId, $chatId, $messageId);
            break;
        case 'promotionReport':
            handlePromotionReport($userId, $chatId, $messageId);
            break;
        case 'withdrawalRecord':
            handleWithdrawalRecord($userId, $chatId, $messageId);
            break;
        case 'claimBonus':
            handleClaimBonus($userId, $chatId);
            break;
        case 'customReferralLinks':
            require_once 'custom_referral_handlers.php';
            showCustomReferralManagement($userId, $chatId);
            break;
        case 'withdrawal_method_bank':
            handleWithdrawalMethodSelection($userId, $chatId, $messageId, 'bank');
            break;
        case 'withdrawal_method_usdt':
            handleWithdrawalMethodSelection($userId, $chatId, $messageId, 'usdt');
            break;
        default:
            // Handle withdrawal amounts
            if (strpos($data, 'withdraw ') === 0) {
                $amount = (int)str_replace('withdraw ', '', $data);
                handleWithdrawAmount($userId, $chatId, $messageId, $amount);
            }
            // Handle account info setting
            elseif (strpos($data, 'set ') === 0) {
                $field = str_replace('set ', '', $data);
                handleSetAccountField($userId, $chatId, $field);
            }
            // Handle remove force sub channel
            elseif (strpos($data, 'remove_force_sub_') === 0) {
                $channelId = str_replace('remove_force_sub_', '', $data);
                handleRemoveForceSubChannelConfirm($userId, $chatId, $channelId);
            }
            // Handle view task
            elseif (strpos($data, 'viewTask_') === 0) {
                $taskId = str_replace('viewTask_', '', $data);
                require_once 'extra_rewards_handlers.php';
                handleViewTask($userId, $chatId, $messageId, $taskId);
            }
            // Handle submit task
            elseif (strpos($data, 'submitTask_') === 0) {
                $taskId = str_replace('submitTask_', '', $data);
                require_once 'extra_rewards_handlers.php';
                handleSubmitTask($userId, $chatId, $taskId);
            }
            // Handle approve task
            elseif (strpos($data, 'approveTask_') === 0) {
                $submissionId = str_replace('approveTask_', '', $data);
                require_once 'admin_task_handlers.php';
                handleApproveTaskSubmission($userId, $chatId, $submissionId);
            }
            // Handle reject task
            elseif (strpos($data, 'rejectTask_') === 0) {
                $submissionId = str_replace('rejectTask_', '', $data);
                require_once 'admin_task_handlers.php';
                handleRejectTaskSubmission($userId, $chatId, $submissionId);
            }
            // Handle set task status
            elseif (strpos($data, 'setTaskStatus_') === 0) {
                $status = str_replace('setTaskStatus_', '', $data);
                require_once 'admin_task_handlers.php';
                handleAddTaskStep6($userId, $chatId, $status);
            }
            // Handle claim level bonus
            elseif (strpos($data, 'claimLevel_') === 0) {
                $level = (int)str_replace('claimLevel_', '', $data);
                require_once 'extra_rewards_handlers.php';
                handleClaimLevelBonus($userId, $chatId, $level);
            }
            // Handle withdrawal approval
            elseif (strpos($data, 'approve_withdrawal_') === 0) {
                $targetUserId = str_replace('approve_withdrawal_', '', $data);
                require_once 'withdrawal_handlers.php';
                handleWithdrawalApprovalCallback($callbackQuery, $targetUserId, 'approve');
            }
            // Handle withdrawal rejection
            elseif (strpos($data, 'reject_withdrawal_') === 0) {
                $targetUserId = str_replace('reject_withdrawal_', '', $data);
                require_once 'withdrawal_handlers.php';
                handleWithdrawalApprovalCallback($callbackQuery, $targetUserId, 'reject');
            }
            // Handle custom referral callbacks
            elseif (strpos($data, 'customref_') === 0) {
                require_once 'custom_referral_handlers.php';
                $action = str_replace('customref_', '', $data);
                handleCustomReferralCallback($userId, $chatId, $action);
            }
            break;
    }
}

function handleSessionMessage($userId, $chatId, $text, $session) {
    $step = $session['step'];
    $data = $session['data'];

    // Handle cancel command
    if ($text === '/cancel') {
        // Check if user has an active broadcast first
        $activeBroadcast = getActiveBroadcast($userId);
        if ($activeBroadcast && isAdmin($userId)) {
            // Cancel the active broadcast
            if (cancelBroadcast($activeBroadcast, $userId)) {
                sendMessage($chatId, "🚫 <b>Broadcast Cancelled</b>\n\nBroadcast ID: <code>" . substr($activeBroadcast, -8) . "</code>\n\nThe broadcast has been stopped and will complete processing current users.", null, 'HTML');
            } else {
                sendMessage($chatId, "❌ Failed to cancel broadcast. Please try again.");
            }
        } else {
            // Clear regular session
            clearUserSession($userId);
            sendMessage($chatId, "🚫 Process cancelled.");
        }
        return;
    }

    // Route to appropriate handler based on step
    switch ($step) {
        case 'add_balance_id':
            handleAddBalanceStep2($userId, $chatId, $text);
            break;
        case 'add_balance_amount':
            handleAddBalanceStep3($userId, $chatId, $text, $data);
            break;
        case 'remove_balance_id':
            handleRemoveBalanceStep2($userId, $chatId, $text);
            break;
        case 'remove_balance_amount':
            handleRemoveBalanceStep3($userId, $chatId, $text, $data);
            break;
        case 'ban_user_id':
            handleBanUserStep2($userId, $chatId, $text);
            break;
        case 'unban_user_id':
            handleUnbanUserStep2($userId, $chatId, $text);
            break;
        case 'set_main_channel':
            handleSetMainChannelStep2($userId, $chatId, $text);
            break;
        case 'set_private_logs_channel':
            handleSetPrivateLogsChannelStep2($userId, $chatId, $text);
            break;
        case 'set_maintenance_status':
            handleSetMaintenanceStatusStep2($userId, $chatId, $text);
            break;
        case 'set_otp_api_key':
            handleSetOTPAPIKeyStep2($userId, $chatId, $text);
            break;
        case 'set_per_refer_amount':
            handleSetPerReferAmountStep2($userId, $chatId, $text);
            break;
        case 'set_joining_bonus_amount':
            handleSetJoiningBonusAmountStep2($userId, $chatId, $text);
            break;
        case 'check_user_record':
            handleCheckUserRecordStep2($userId, $chatId, $text);
            break;
        case 'pass_user_withdrawal':
            handlePassUserWithdrawalStep2($userId, $chatId, $text);
            break;
        case 'fail_user_withdrawal':
            handleFailUserWithdrawalStep2($userId, $chatId, $text);
            break;
        case 'broadcast_gift_channel':
            handleBroadcastGiftStep2($userId, $chatId, $text);
            break;
        case 'broadcast_gift_invite_link':
            handleBroadcastGiftInviteLinkStep($userId, $chatId, $text, $data);
            break;
        case 'broadcast_gift_amount':
            handleBroadcastGiftStep3($userId, $chatId, $text, $data);
            break;
        case 'broadcast_text':
            handleBroadcastTextStep2($userId, $chatId, $text);
            break;
        case 'broadcast_message':
            // Handle enhanced broadcast message
            global $update;
            require_once 'user_account_handlers.php';
            handleBroadcastMessageStep2($userId, $chatId, $update['message']);
            break;
        case 'set_account_name':
            handleSetAccountNameStep2($userId, $chatId, $text);
            break;
        case 'set_account_ifsc':
            handleSetAccountIFSCStep2($userId, $chatId, $text);
            break;
        case 'set_account_email':
            handleSetAccountEmailStep2($userId, $chatId, $text);
            break;
        case 'set_account_number':
            handleSetAccountNumberStep2($userId, $chatId, $text);
            break;
        case 'set_mobile_number':
            handleSetMobileNumberStep2($userId, $chatId, $text);
            break;
        case 'verify_otp':
            handleVerifyOTPStep2($userId, $chatId, $text, $data);
            break;
        case 'add_force_sub_channel':
            // Handle forwarded message for adding force sub channel
            global $update;
            handleAddForceSubChannelStep2($userId, $chatId, $update['message']);
            break;
        case 'submit_task_screenshot':
            // Handle task screenshot submission
            global $update;
            require_once 'extra_rewards_handlers.php';
            handleTaskScreenshotSubmission($userId, $chatId, $update['message'], $data);
            break;
        case 'redeem_gift_code':
            require_once 'extra_rewards_handlers.php';
            handleGiftCodeRedemption($userId, $chatId, $text);
            break;
        case 'add_task_name':
            require_once 'admin_task_handlers.php';
            handleAddTaskStep2($userId, $chatId, $text);
            break;
        case 'add_task_description':
            require_once 'admin_task_handlers.php';
            handleAddTaskStep3($userId, $chatId, $text, $data);
            break;
        case 'add_task_reward':
            require_once 'admin_task_handlers.php';
            handleAddTaskStep4($userId, $chatId, $text, $data);
            break;
        case 'add_task_media':
            require_once 'admin_task_handlers.php';
            handleAddTaskStep5($userId, $chatId, $text, $data);
            break;
        case 'generate_gift_code':
            require_once 'admin_task_handlers.php';
            handleGenerateGiftCodeStep2($userId, $chatId, $text);
            break;
        case 'generate_gift_amount':
            require_once 'admin_task_handlers.php';
            handleGenerateGiftCodeStep3($userId, $chatId, $text, $data);
            break;
        case 'generate_gift_limit':
            require_once 'admin_task_handlers.php';
            handleGenerateGiftCodeStep4($userId, $chatId, $text, $data);
            break;
        case 'configure_level_referrals':
            require_once 'admin_task_handlers.php';
            handleConfigureLevelReferralsStep2($userId, $chatId, $text);
            break;
        case 'configure_level_bonuses':
            require_once 'admin_task_handlers.php';
            handleConfigureLevelBonusesStep3($userId, $chatId, $text, $data);
            break;
        case 'set_withdrawal_tax':
            handleSetWithdrawalTaxStep2($userId, $chatId, $text);
            break;
        case 'set_usdt_address':
        case 'set_binance_id':
            handleSetBinanceIdStep2($userId, $chatId, $text);
            break;
        default:
            clearUserSession($userId);
            break;
    }
}
?>
