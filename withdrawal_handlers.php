<?php
require_once 'config.php';
require_once 'core_functions.php';
require_once 'database_functions.php';

/**
 * Handle withdrawal approval/rejection via inline buttons with callback query
 */
function handleWithdrawalApprovalCallback($callbackQuery, $targetUserId, $action) {
    $adminUserId = $callbackQuery['from']['id'];
    $chatId = $callbackQuery['message']['chat']['id'];
    $messageId = $callbackQuery['message']['message_id'];
    $callbackQueryId = $callbackQuery['id'];

    // Check if user is admin
    if (!isAdmin($adminUserId)) {
        answerCallbackQuery($callbackQueryId, "❌ You are not authorized to perform this action.", true);
        return;
    }

    try {
        // Get admin info for logging
        $adminUser = getUser($adminUserId);
        $adminName = $adminUser['first_name'] ?? 'Admin';
        
        // Get target user info
        $targetUser = getUser($targetUserId);
        if (!$targetUser) {
            answerCallbackQuery($callbackQueryId, "❌ User not found.", true);
            return;
        }

        // Check if user has withdrawal under review
        if ($targetUser['withdraw_under_review'] <= 0) {
            answerCallbackQuery($callbackQueryId, "❌ No withdrawal request found for this user.", true);
            return;
        }

        $withdrawalAmount = $targetUser['withdraw_under_review'];
        $currentDate = getCurrentDate();

        if ($action === 'approve') {
            // Approve the withdrawal
            $withdrawal = updateWithdrawalStatus($targetUserId, 'Passed');
            
            if ($withdrawal) {
                // Send success notification to user
                $userMessage = "✅ <b>Withdrawal Approved!</b>\n\n";
                $userMessage .= "💵 <b>Amount:</b> ₹{$withdrawalAmount}\n";
                $userMessage .= "⏰ <b>Date:</b> {$currentDate}\n\n";
                $userMessage .= "🎉 Your withdrawal request has been approved!\n";
                $userMessage .= "💳 The payment will be credited to your account in <b>1-2 working days</b>.\n\n";
                $userMessage .= "📧 You will receive a confirmation email once the payment is processed.\n";
                $userMessage .= "💬 For any queries, contact our support team.";

                sendMessage($targetUserId, $userMessage, null, 'HTML');

                // Update the admin log message
                $updatedAdminMessage = getUpdatedWithdrawalMessage($targetUser, $withdrawalAmount, 'APPROVED', $adminName, $currentDate);
                editMessageText($chatId, $messageId, $updatedAdminMessage, null, 'HTML');

                // Send callback response
                answerCallbackQuery($callbackQueryId, "✅ Withdrawal approved successfully!", false);

                // Log the action
                error_log("Withdrawal approved: User {$targetUserId}, Amount ₹{$withdrawalAmount}, Admin: {$adminName} ({$adminUserId})");

            } else {
                answerCallbackQuery($callbackQueryId, "❌ Failed to approve withdrawal. Please try again.", true);
            }

        } elseif ($action === 'reject') {
            // Reject the withdrawal
            $withdrawal = updateWithdrawalStatus($targetUserId, 'Failed');
            
            if ($withdrawal) {
                // Send rejection notification to user
                $userMessage = "❌ <b>Withdrawal Rejected</b>\n\n";
                $userMessage .= "💵 <b>Amount:</b> ₹{$withdrawalAmount}\n";
                $userMessage .= "⏰ <b>Date:</b> {$currentDate}\n\n";
                $userMessage .= "😔 Your withdrawal request has been declined.\n\n";
                $userMessage .= "📋 <b>Possible reasons:</b>\n";
                $userMessage .= "• Incorrect account details\n";
                $userMessage .= "• Insufficient verification\n";
                $userMessage .= "• Policy violation\n";
                $userMessage .= "• Technical issues\n\n";
                $userMessage .= "💬 Please contact our support team for more information.\n";
                $userMessage .= "🔄 You can try submitting a new withdrawal request after resolving any issues.";
                
                sendMessage($targetUserId, $userMessage, null, 'HTML');

                // Update the admin log message
                $updatedAdminMessage = getUpdatedWithdrawalMessage($targetUser, $withdrawalAmount, 'REJECTED', $adminName, $currentDate);
                editMessageText($chatId, $messageId, $updatedAdminMessage, null, 'HTML');

                // Send callback response
                answerCallbackQuery($callbackQueryId, "❌ Withdrawal rejected successfully!", false);

                // Log the action
                error_log("Withdrawal rejected: User {$targetUserId}, Amount ₹{$withdrawalAmount}, Admin: {$adminName} ({$adminUserId})");

            } else {
                answerCallbackQuery($callbackQueryId, "❌ Failed to reject withdrawal. Please try again.", true);
            }
        }

    } catch (Exception $e) {
        error_log("Error in handleWithdrawalApprovalCallback: " . $e->getMessage());
        answerCallbackQuery($callbackQueryId, "❌ An error occurred. Please try again.", true);
    }
}

/**
 * Generate updated admin message after withdrawal processing
 */
function getUpdatedWithdrawalMessage($user, $amount, $status, $adminName, $processedDate) {
    $userId = $user['user_id'];
    $statusEmoji = ($status === 'APPROVED') ? '✅' : '❌';
    $statusColor = ($status === 'APPROVED') ? '🟢' : '🔴';
    
    $message = "<b>{$statusEmoji} Withdrawal {$status}</b>\n\n";
    $message .= "<b>👤 User:</b> {$user['first_name']}\n";
    $message .= "<b>ℹ️ User ID:</b> <code>{$userId}</code>\n";
    $message .= "<b>💵 Amount:</b> <code>₹{$amount}</code>\n\n";
    
    $message .= "<b>👇 Account details:\n\n";
    $message .= "Name: {$user['name']}\n";
    $message .= "IFSC:</b> <code>{$user['ifsc']}</code>\n";
    $message .= "<b>Email: {$user['email']}\n";
    $message .= "Account Number:</b> <code>{$user['account_number']}</code>\n";
    $message .= "<b>Mobile Number:</b> <code>{$user['mobile_number']}</code>\n\n";
    
    $message .= "{$statusColor} <b>Status:</b> {$status}\n";
    $message .= "👨‍💼 <b>Processed by:</b> {$adminName}\n";
    $message .= "⏰ <b>Processed at:</b> {$processedDate}\n\n";
    
    if ($status === 'APPROVED') {
        $message .= "✅ <i>User has been notified of approval</i>\n";
        $message .= "💳 <i>Payment should be processed within 1-2 working days</i>";
    } else {
        $message .= "❌ <i>User has been notified of rejection</i>\n";
        $message .= "🔄 <i>User can submit a new request after resolving issues</i>";
    }
    
    return $message;
}



/**
 * Post withdrawal success message to main channel
 */
function postWithdrawalSuccessToMainChannel($user, $amount) {
    try {
        $firstName = htmlspecialchars($user['first_name'] ?? 'User');
        $mainChannelId = '@' . MAIN_CHANNEL;

        // Create the success message
        $message = "🎉Congratulations to 👤{$firstName}\n";
        $message .= "Get ₹100 By Invitation!💥💥💥\n\n";
        $message .= "✅🎉WELCOME TO OUR CHANNEL!\n\n";
        $message .= "GET UP TO ₹100 INSTANTLY!\n";
        $message .= "👇️👇️👇️ CLICK NOW!👇️👇👇️\n\n";
        $message .= "BUTTON: 🎁🔥\n\n";
        $message .= "    👉🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔 (https://t.me/" . BOT_USERNAME . "?start=from_channel)👈\n";
        $message .= "    👉🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔 (https://t.me/" . BOT_USERNAME . "?start=from_channel)👈\n";
        $message .= "    👉🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔 (https://t.me/" . BOT_USERNAME . "?start=from_channel)👈\n";
        $message .= "    👉🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔 (https://t.me/" . BOT_USERNAME . "?start=from_channel)👈\n";
        $message .= "    👉🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔 (https://t.me/" . BOT_USERNAME . "?start=from_channel)👈\n\n";
        $message .= "👇️👇️👇️ CLICK NOW!👇️👇👇️";

        // Create inline keyboard with the main button
        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔥🎁𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔',
                        'url' => 'https://t.me/' . BOT_USERNAME . '?start=from_channel'
                    ]
                ]
            ]
        ];

        // Send message to main channel
        $result = sendMessage($mainChannelId, $message, $keyboard, 'HTML');

        if ($result) {
            error_log("Withdrawal success message posted to main channel for user: {$user['user_id']}, amount: ₹{$amount}");
        } else {
            error_log("Failed to post withdrawal success message to main channel for user: {$user['user_id']}");
        }

        return $result;

    } catch (Exception $e) {
        error_log("Error posting withdrawal success to main channel: " . $e->getMessage());
        return false;
    }
}


?>
